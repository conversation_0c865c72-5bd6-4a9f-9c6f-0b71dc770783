{"version": 3, "file": "aes-gcm.browser.js", "sourceRoot": "", "sources": ["../../../src/ciphers/aes-gcm.browser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,SAAS,MAAM,uBAAuB,CAAA;AAG7C,4EAA4E;AAC5E,gFAAgF;AAChF,4DAA4D;AAC5D,iBAAiB;AACjB,uCAAuC;AACvC,mCAAmC;AACnC,kGAAkG;AAClG,yGAAyG;AACzG,sEAAsE;AACtE,IAAI;AACJ,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,IAAI;IACT,qCAAqC;IACrC,CAAC,EAAE,wBAAwB;IAC3B,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B,GAAG,EAAE,KAAK;CACX,CAAA;AAED,yFAAyF;AAEzF,MAAM,UAAU,MAAM,CAAE,IAA6B;IACnD,MAAM,SAAS,GAAG,IAAI,EAAE,SAAS,IAAI,SAAS,CAAA;IAC9C,IAAI,SAAS,GAAG,IAAI,EAAE,SAAS,IAAI,EAAE,CAAA;IACrC,MAAM,WAAW,GAAG,IAAI,EAAE,WAAW,IAAI,EAAE,CAAA;IAC3C,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,SAAS,CAAA;IACxC,MAAM,UAAU,GAAG,IAAI,EAAE,UAAU,IAAI,EAAE,CAAA;IACzC,MAAM,UAAU,GAAG,IAAI,EAAE,UAAU,IAAI,KAAK,CAAA;IAE5C,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,CAAA;IAC9B,SAAS,IAAI,CAAC,CAAA,CAAC,4CAA4C;IAE3D;;;OAGG;IACH,KAAK,UAAU,OAAO,CAAE,IAAgB,EAAE,QAA6B;QACrE,MAAM,IAAI,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAA;QAC/D,MAAM,KAAK,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAA;QACjE,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAA;QAE7C,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAA;QACjC,CAAC;QAED,IAAI,SAAoB,CAAA;QACxB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,uBAAuB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;YACjH,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,CAAA;gBACjF,MAAM,2BAA2B,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA;gBAC5H,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,2BAA2B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;YACjJ,CAAC;YAAC,MAAM,CAAC;gBACP,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,uBAAuB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;YACnH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,6BAA6B;YAC7B,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,CAAA;YACjF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA;YACvG,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;QAC5H,CAAC;QAED,sBAAsB;QACtB,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;QACvE,OAAO,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;IAC9D,CAAC;IAED;;;;;OAKG;IACH,KAAK,UAAU,OAAO,CAAE,IAAgB,EAAE,QAA6B;QACrE,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,GAAG,WAAW,CAAC,CAAA;QACjE,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,WAAW,CAAC,CAAA;QAC1D,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAA;QAE7C,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAA;QACjC,CAAC;QAED,IAAI,SAAoB,CAAA;QACxB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,CAAA;gBACjF,MAAM,2BAA2B,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA;gBAC5H,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,2BAA2B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;YACjJ,CAAC;YAAC,MAAM,CAAC;gBACP,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,uBAAuB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;YACnH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,+BAA+B;YAC/B,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,CAAA;YACjF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA;YACvG,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;QAC5H,CAAC;QAED,sBAAsB;QACtB,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;QAC5E,OAAO,IAAI,UAAU,CAAC,SAAS,CAAC,CAAA;IAClC,CAAC;IAED,MAAM,MAAM,GAAc;QACxB,OAAO;QACP,OAAO;KACR,CAAA;IAED,OAAO,MAAM,CAAA;AACf,CAAC"}