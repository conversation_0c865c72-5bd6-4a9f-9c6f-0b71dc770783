{"version": 3, "file": "aes-gcm.js", "sourceRoot": "", "sources": ["../../../src/ciphers/aes-gcm.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,QAAQ,CAAA;AAC3B,OAAO,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAC/D,OAAO,EAAE,UAAU,IAAI,oBAAoB,EAAE,MAAM,yBAAyB,CAAA;AAK5E,yFAAyF;AAEzF,MAAM,UAAU,MAAM,CAAE,IAA6B;IACnD,MAAM,SAAS,GAAG,IAAI,EAAE,SAAS,IAAI,aAAa,CAAA;IAClD,MAAM,SAAS,GAAG,IAAI,EAAE,SAAS,IAAI,EAAE,CAAA;IACvC,MAAM,WAAW,GAAG,IAAI,EAAE,WAAW,IAAI,EAAE,CAAA;IAC3C,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,QAAQ,CAAA;IACvC,MAAM,UAAU,GAAG,IAAI,EAAE,UAAU,IAAI,EAAE,CAAA;IACzC,MAAM,UAAU,GAAG,IAAI,EAAE,UAAU,IAAI,KAAK,CAAA;IAC5C,MAAM,kBAAkB,GAAG,IAAI,EAAE,kBAAkB,IAAI,EAAE,CAAA;IAEzD,SAAS,cAAc,CAAE,IAAgB,EAAE,GAAe;QACxD,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QAE7C,8BAA8B;QAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC,CAAA;QAE3D,6BAA6B;QAC7B,MAAM,UAAU,GAAG,gBAAgB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QAE1E,gDAAgD;QAChD,OAAO,gBAAgB,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAA;IACnE,CAAC;IAED;;;OAGG;IACH,KAAK,UAAU,OAAO,CAAE,IAAgB,EAAE,QAA6B;QACrE,0BAA0B;QAC1B,MAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAE3C,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAA;QAC3C,CAAC;QAED,6BAA6B;QAC7B,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;QAE5E,4BAA4B;QAC5B,OAAO,gBAAgB,CAAC,CAAC,IAAI,EAAE,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;IAC7E,CAAC;IAED;;;;;;OAMG;IACH,SAAS,cAAc,CAAE,kBAA8B,EAAE,GAAe;QACtE,mDAAmD;QACnD,MAAM,KAAK,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,CAAA;QACzD,MAAM,UAAU,GAAG,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE,kBAAkB,CAAC,MAAM,GAAG,kBAAkB,CAAC,CAAA;QAC3G,MAAM,GAAG,GAAG,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC,CAAA;QAExE,8BAA8B;QAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC,CAAA;QAE7D,6BAA6B;QAC7B,gDAAgD;QAChD,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;QACtB,OAAO,gBAAgB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;IACtE,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,UAAU,OAAO,CAAE,IAAgB,EAAE,QAA6B;QACrE,qDAAqD;QACrD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;QACzC,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAEpD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAA;QAC3C,CAAC;QAED,+BAA+B;QAC/B,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;QAE5E,6BAA6B;QAC7B,OAAO,cAAc,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAA;IAChD,CAAC;IAED,MAAM,MAAM,GAAc;QACxB,OAAO;QACP,OAAO;KACR,CAAA;IAED,OAAO,MAAM,CAAA;AACf,CAAC"}