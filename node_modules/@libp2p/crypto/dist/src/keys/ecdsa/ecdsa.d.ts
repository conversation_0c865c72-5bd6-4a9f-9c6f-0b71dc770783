import { CID } from 'multiformats/cid';
import type { ECDS<PERSON><PERSON><PERSON><PERSON>ey as ECDSAPublicKeyInterface, ECDSAPrivate<PERSON>ey as ECDSAPrivateKeyInterface, AbortOptions } from '@libp2p/interface';
import type { Digest } from 'multiformats/hashes/digest';
import type { Uint8ArrayList } from 'uint8arraylist';
export declare class EC<PERSON><PERSON><PERSON>lic<PERSON><PERSON> implements ECDSAPublicKeyInterface {
    readonly type = "ECDSA";
    readonly jwk: JsonWeb<PERSON>ey;
    private _raw?;
    constructor(jwk: JsonWebKey);
    get raw(): Uint8Array;
    toMultihash(): Digest<0x0, number>;
    toCID(): CID<unknown, 114, 0x0, 1>;
    toString(): string;
    equals(key?: any): boolean;
    verify(data: Uint8Array | Uint8ArrayList, sig: Uint8Array, options?: AbortOptions): Promise<boolean>;
}
export declare class EC<PERSON><PERSON>rivate<PERSON>ey implements ECDSAPrivateKeyInterface {
    readonly type = "ECDSA";
    readonly jwk: J<PERSON><PERSON><PERSON><PERSON><PERSON>;
    readonly publicKey: ECDSAPublicKey;
    private _raw?;
    constructor(jwk: JsonWeb<PERSON>ey);
    get raw(): Uint8Array;
    equals(key?: any): boolean;
    sign(message: Uint8Array | Uint8ArrayList, options?: AbortOptions): Promise<Uint8Array>;
}
//# sourceMappingURL=ecdsa.d.ts.map