{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/keys/ecdsa/utils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAA;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAA;AAC/C,OAAO,EAAE,UAAU,IAAI,oBAAoB,EAAE,MAAM,yBAAyB,CAAA;AAC5E,OAAO,EAAE,QAAQ,IAAI,kBAAkB,EAAE,MAAM,uBAAuB,CAAA;AACtE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM,eAAe,CAAA;AAC5G,OAAO,EAAE,eAAe,IAAI,oBAAoB,EAAE,cAAc,IAAI,mBAAmB,EAAE,MAAM,YAAY,CAAA;AAC3G,OAAO,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAA;AAI7C,mEAAmE;AACnE,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;AAC7F,gEAAgE;AAChE,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;AAC3E,gEAAgE;AAChE,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;AAE3E,MAAM,aAAa,GAAG;IACpB,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,OAAO;CACb,CAAA;AAED,MAAM,aAAa,GAAG;IACpB,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,OAAO;CACb,CAAA;AAED,MAAM,aAAa,GAAG;IACpB,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,OAAO;CACb,CAAA;AAED,MAAM,gBAAgB,GAAG,EAAE,CAAA;AAC3B,MAAM,gBAAgB,GAAG,EAAE,CAAA;AAC3B,MAAM,gBAAgB,GAAG,EAAE,CAAA;AAE3B,MAAM,UAAU,wBAAwB,CAAE,KAAiB;IACzD,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,CAAA;IAEhC,OAAO,2BAA2B,CAAC,OAAO,CAAC,CAAA;AAC7C,CAAC;AAED,MAAM,UAAU,2BAA2B,CAAE,OAAY;IACvD,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;IAC7B,MAAM,CAAC,GAAG,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;IACrD,MAAM,WAAW,GAAe,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAChD,MAAM,MAAM,GAAG,CAAC,CAAA;IAChB,IAAI,CAAS,CAAA;IACb,IAAI,CAAS,CAAA;IAEb,IAAI,UAAU,CAAC,UAAU,KAAK,gBAAgB,EAAE,CAAC;QAC/C,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAC5F,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAEpF,OAAO,IAAI,oBAAoB,CAAC;YAC9B,GAAG,aAAa;YAChB,OAAO,EAAE,CAAC,MAAM,CAAC;YACjB,CAAC;YACD,CAAC;YACD,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,UAAU,CAAC,UAAU,KAAK,gBAAgB,EAAE,CAAC;QAC/C,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAC5F,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAEpF,OAAO,IAAI,oBAAoB,CAAC;YAC9B,GAAG,aAAa;YAChB,OAAO,EAAE,CAAC,MAAM,CAAC;YACjB,CAAC;YACD,CAAC;YACD,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,UAAU,CAAC,UAAU,KAAK,gBAAgB,EAAE,CAAC;QAC/C,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAC5F,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAEpF,OAAO,IAAI,oBAAoB,CAAC;YAC9B,GAAG,aAAa;YAChB,OAAO,EAAE,CAAC,MAAM,CAAC;YACjB,CAAC;YACD,CAAC;YACD,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,IAAI,sBAAsB,CAAC,4CAA4C,UAAU,CAAC,UAAU,yBAAyB,CAAC,CAAA;AAC9H,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAE,KAAiB;IACxD,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,CAAA;IAEhC,OAAO,0BAA0B,CAAC,OAAO,CAAC,CAAA;AAC5C,CAAC;AAED,MAAM,UAAU,0BAA0B,CAAE,OAAY;IACtD,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACpC,MAAM,MAAM,GAAG,CAAC,CAAA;IAChB,IAAI,CAAS,CAAA;IACb,IAAI,CAAS,CAAA;IAEb,IAAI,WAAW,CAAC,UAAU,KAAK,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QAC5D,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAC5F,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAEpF,OAAO,IAAI,mBAAmB,CAAC;YAC7B,GAAG,aAAa;YAChB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,CAAC;YACD,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,WAAW,CAAC,UAAU,KAAK,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QAC5D,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAC5F,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAEpF,OAAO,IAAI,mBAAmB,CAAC;YAC7B,GAAG,aAAa;YAChB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,CAAC;YACD,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,WAAW,CAAC,UAAU,KAAK,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QAC5D,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAC5F,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAA;QAEpF,OAAO,IAAI,mBAAmB,CAAC;YAC7B,GAAG,aAAa;YAChB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,CAAC;YACD,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,IAAI,sBAAsB,CAAC,sCAAsC,WAAW,CAAC,UAAU,0BAA0B,CAAC,CAAA;AAC1H,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAE,UAAsB;IAC5D,OAAO,cAAc,CAAC;QACpB,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS;QAC9C,iBAAiB,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,CAAC,EAAE,OAAO;QACjF,cAAc,CAAC;YACb,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;SACvB,EAAE,IAAI,CAAC;QACR,cAAc,CAAC;YACb,eAAe,CACb,IAAI,cAAc,CAChB,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EACvB,oBAAoB,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,EACrD,oBAAoB,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,CACtD,CACF;SACF,EAAE,IAAI,CAAC;KACT,CAAC,CAAC,QAAQ,EAAE,CAAA;AACf,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAE,SAAqB;IAC1D,OAAO,cAAc,CAAC;QACpB,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS;QAC9C,cAAc,CAAC;YACb,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;SACtB,EAAE,IAAI,CAAC;QACR,cAAc,CAAC;YACb,eAAe,CACb,IAAI,cAAc,CAChB,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EACvB,oBAAoB,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,EACpD,oBAAoB,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,CACrD,CACF;SACF,EAAE,IAAI,CAAC;KACT,CAAC,CAAC,QAAQ,EAAE,CAAA;AACf,CAAC;AAED,SAAS,MAAM,CAAE,KAAc;IAC7B,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QACtB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QACtB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QACtB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,MAAM,IAAI,sBAAsB,CAAC,iBAAiB,KAAK,EAAE,CAAC,CAAA;AAC5D,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,oBAAoB,CAAE,QAAe,OAAO;IAChE,MAAM,GAAG,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC,CAAA;IAEzC,OAAO,IAAI,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;AACjD,CAAC;AAED,MAAM,UAAU,cAAc,CAAE,GAAe,EAAE,MAAc;IAC7D,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAA;IAChC,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC1B,MAAM,IAAI,sBAAsB,CAAC,sCAAsC,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,CAAC,CAAA;IACrG,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC"}