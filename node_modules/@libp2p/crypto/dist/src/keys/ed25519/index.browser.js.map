{"version": 3, "file": "index.browser.js", "sourceRoot": "", "sources": ["../../../../src/keys/ed25519/index.browser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,IAAI,EAAE,EAAE,MAAM,uBAAuB,CAAA;AACrD,OAAO,EAAE,QAAQ,IAAI,kBAAkB,EAAE,MAAM,uBAAuB,CAAA;AACtE,OAAO,MAAM,MAAM,0BAA0B,CAAA;AAI7C,MAAM,sBAAsB,GAAG,EAAE,CAAA;AACjC,MAAM,uBAAuB,GAAG,EAAE,CAAA,CAAC,gGAAgG;AACnI,MAAM,gBAAgB,GAAG,EAAE,CAAA;AAE3B,OAAO,EAAE,sBAAsB,IAAI,eAAe,EAAE,CAAA;AACpD,OAAO,EAAE,uBAAuB,IAAI,gBAAgB,EAAE,CAAA;AAEtD,+EAA+E;AAC/E,IAAI,gBAAqC,CAAA;AACzC,MAAM,gCAAgC,GAAG,CAAC,KAAK,IAAI,EAAE;IACnD,IAAI,CAAC;QACH,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;QACpF,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC,CAAC,EAAE,CAAA;AAEJ,MAAM,UAAU,WAAW;IACzB,oCAAoC;IACpC,MAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAA;IACjD,MAAM,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAA;IAEhD,iDAAiD;IACjD,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;IAEvD,OAAO;QACL,UAAU;QACV,SAAS;KACV,CAAA;AACH,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAE,IAAgB;IACnD,IAAI,IAAI,CAAC,MAAM,KAAK,gBAAgB,EAAE,CAAC;QACrC,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAA;IAC3D,CAAC;SAAM,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,CAAC,EAAE,CAAC;QACzC,MAAM,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAA;IACxE,CAAC;IAED,2EAA2E;IAC3E,MAAM,aAAa,GAAG,IAAI,CAAA;IAC1B,MAAM,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAA;IAEhD,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;IAEvD,OAAO;QACL,UAAU;QACV,SAAS;KACV,CAAA;AACH,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAE,UAAsB,EAAE,GAAgC;IAC3F,IAAI,aAAyB,CAAA;IAC7B,IAAI,UAAU,CAAC,MAAM,KAAK,uBAAuB,EAAE,CAAC;QAClD,aAAa,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC5C,CAAC;SAAM,CAAC;QACN,aAAa,GAAG,UAAU,CAAA;IAC5B,CAAC;IAED,MAAM,GAAG,GAAe;QACtB,GAAG,EAAE,SAAS;QACd,GAAG,EAAE,KAAK;QACV,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC;QAC3D,CAAC,EAAE,kBAAkB,CAAC,aAAa,EAAE,WAAW,CAAC;QACjD,GAAG,EAAE,IAAI;QACT,OAAO,EAAE,CAAC,MAAM,CAAC;KAClB,CAAA;IAED,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;IAChG,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAA;IAEtH,OAAO,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,CAAA;AAC/C,CAAC;AAED,SAAS,gBAAgB,CAAE,UAAsB,EAAE,GAAgC;IACjF,MAAM,aAAa,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAA;IAE9D,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAA;AACjF,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,WAAW,CAAE,UAAsB,EAAE,GAAgC;IACzF,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,gBAAgB,GAAG,MAAM,gCAAgC,CAAA;IAC3D,CAAC;IAED,IAAI,gBAAgB,EAAE,CAAC;QACrB,OAAO,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;IAC9C,CAAC;IAED,OAAO,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;AAC1C,CAAC;AAED,KAAK,UAAU,sBAAsB,CAAE,SAAqB,EAAE,GAAe,EAAE,GAAgC;IAC7G,IAAI,SAAS,CAAC,MAAM,YAAY,WAAW,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;QAChH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAA;QACjI,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,MAAM,IAAI,SAAS,CAAC,+DAA+D,CAAC,CAAA;AACtF,CAAC;AAED,SAAS,kBAAkB,CAAE,SAAqB,EAAE,GAAe,EAAE,GAAgC;IACnG,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAA;AACpF,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,aAAa,CAAE,SAAqB,EAAE,GAAe,EAAE,GAAgC;IAC3G,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,gBAAgB,GAAG,MAAM,gCAAgC,CAAA;IAC3D,CAAC;IAED,IAAI,gBAAgB,EAAE,CAAC;QACrB,OAAO,sBAAsB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;IACpD,CAAC;IAED,OAAO,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AAChD,CAAC;AAED,SAAS,UAAU,CAAE,aAAyB,EAAE,SAAqB;IACnE,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAA;IAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC;QAC1C,UAAU,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;QAChC,UAAU,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;IACjD,CAAC;IACD,OAAO,UAAU,CAAA;AACnB,CAAC"}