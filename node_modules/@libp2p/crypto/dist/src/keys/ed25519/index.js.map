{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/keys/ed25519/index.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,QAAQ,CAAA;AAC3B,OAAO,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAC/D,OAAO,EAAE,UAAU,IAAI,oBAAoB,EAAE,MAAM,yBAAyB,CAAA;AAC5E,OAAO,EAAE,QAAQ,IAAI,kBAAkB,EAAE,MAAM,uBAAuB,CAAA;AAItE,MAAM,OAAO,GAAG,MAAM,CAAC,mBAAmB,CAAA;AAE1C,MAAM,sBAAsB,GAAG,EAAE,CAAA;AACjC,MAAM,uBAAuB,GAAG,EAAE,CAAA,CAAC,gGAAgG;AACnI,MAAM,gBAAgB,GAAG,EAAE,CAAA;AAC3B,MAAM,qBAAqB,GAAG,EAAE,CAAA;AAEhC,OAAO,EAAE,sBAAsB,IAAI,eAAe,EAAE,CAAA;AACpD,OAAO,EAAE,uBAAuB,IAAI,gBAAgB,EAAE,CAAA;AAEtD,SAAS,eAAe,CAAE,UAAsB;IAC9C,MAAM,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC;QACxC,MAAM,EAAE,KAAK;QACb,GAAG,EAAE;YACH,GAAG,EAAE,SAAS;YACd,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC;YAC9C,GAAG,EAAE,KAAK;SACX;KACF,CAAC,CAAA;IACF,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;QAC3B,MAAM,EAAE,KAAK;KACd,CAAC,CAAA;IAEF,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;IACpD,CAAC;IAED,OAAO,oBAAoB,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAA;AACjD,CAAC;AAED,MAAM,UAAU,WAAW;IACzB,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,EAAE;QAC7B,iBAAiB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;QAClD,kBAAkB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE;KACrD,CAAC,CAAA;IAEF,0DAA0D;IAC1D,MAAM,aAAa,GAAG,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,WAAW,CAAC,CAAA;IACzE,0DAA0D;IAC1D,MAAM,YAAY,GAAG,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,CAAA;IAEvE,OAAO;QACL,UAAU,EAAE,gBAAgB,CAAC,CAAC,aAAa,EAAE,YAAY,CAAC,EAAE,aAAa,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAC/G,SAAS,EAAE,YAAY;KACxB,CAAA;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAE,IAAgB;IACnD,IAAI,IAAI,CAAC,MAAM,KAAK,gBAAgB,EAAE,CAAC;QACrC,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAA;IAC3D,CAAC;SAAM,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,CAAC,EAAE,CAAC;QACzC,MAAM,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAA;IACxE,CAAC;IAED,2EAA2E;IAC3E,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,CAAA;IAE1C,OAAO;QACL,UAAU,EAAE,gBAAgB,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAC7F,SAAS,EAAE,YAAY;KACxB,CAAA;AACH,CAAC;AAED,MAAM,UAAU,WAAW,CAAE,GAAe,EAAE,GAAgC;IAC5E,IAAI,CAAC,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC;QACjC,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAA;IACvE,CAAC;IAED,IAAI,UAAsB,CAAA;IAC1B,IAAI,SAAqB,CAAA;IAEzB,IAAI,GAAG,CAAC,UAAU,KAAK,uBAAuB,EAAE,CAAC;QAC/C,UAAU,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QAChC,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;SAAM,IAAI,GAAG,CAAC,UAAU,KAAK,gBAAgB,EAAE,CAAC;QAC/C,UAAU,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QAChC,SAAS,GAAG,eAAe,CAAC,UAAU,CAAC,CAAA;IACzC,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,SAAS,CAAC,yCAAyC,CAAC,CAAA;IAChE,CAAC;IAED,MAAM,GAAG,GAAG,MAAM,CAAC,gBAAgB,CAAC;QAClC,MAAM,EAAE,KAAK;QACb,GAAG,EAAE;YACH,GAAG,EAAE,SAAS;YACd,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC;YAC9C,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC;YAC7C,GAAG,EAAE,KAAK;SACX;KACF,CAAC,CAAA;IAEF,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAA;AACjF,CAAC;AAED,MAAM,UAAU,aAAa,CAAE,GAAe,EAAE,GAAe,EAAE,GAAgC;IAC/F,IAAI,GAAG,CAAC,UAAU,KAAK,sBAAsB,EAAE,CAAC;QAC9C,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;IAC1D,CAAC;SAAM,IAAI,CAAC,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAA;IACvE,CAAC;IAED,IAAI,GAAG,CAAC,UAAU,KAAK,qBAAqB,EAAE,CAAC;QAC7C,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;IAC1D,CAAC;SAAM,IAAI,CAAC,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAA;IACvE,CAAC;IAED,MAAM,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC;QACjC,MAAM,EAAE,KAAK;QACb,GAAG,EAAE;YACH,GAAG,EAAE,SAAS;YACd,CAAC,EAAE,kBAAkB,CAAC,GAAG,EAAE,WAAW,CAAC;YACvC,GAAG,EAAE,KAAK;SACX;KACF,CAAC,CAAA;IAEF,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AACxF,CAAC"}