{"version": 3, "file": "ed25519.js", "sourceRoot": "", "sources": ["../../../../src/keys/ed25519/ed25519.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAA;AACrD,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAA;AACtC,OAAO,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAA;AACvD,OAAO,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAC/D,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAA;AACzC,OAAO,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAA;AACjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAA;AAC7C,OAAO,KAAK,MAAM,MAAM,YAAY,CAAA;AAKpC,MAAM,OAAO,gBAAgB;IACX,IAAI,GAAG,SAAS,CAAA;IAChB,GAAG,CAAY;IAE/B,YAAa,GAAe;QAC1B,IAAI,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,eAAe,CAAC,CAAA;IAC1D,CAAC;IAED,WAAW;QACT,OAAO,QAAQ,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAA;IACnD,CAAC;IAED,KAAK;QACH,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;IAC9C,CAAC;IAED,QAAQ;QACN,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;IAChE,CAAC;IAED,MAAM,CAAE,GAAS;QACf,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC;YACpD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;IAC5C,CAAC;IAED,MAAM,CAAE,IAAiC,EAAE,GAAe,EAAE,OAAsB;QAChF,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,CAAA;QACjC,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;QAExD,IAAI,SAAS,CAAU,MAAM,CAAC,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACvB,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,CAAA;gBACjC,OAAO,GAAG,CAAA;YACZ,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AAED,MAAM,OAAO,iBAAiB;IACZ,IAAI,GAAG,SAAS,CAAA;IAChB,GAAG,CAAY;IACf,SAAS,CAAkB;IAE3C,wDAAwD;IACxD,uDAAuD;IACvD,YAAa,GAAe,EAAE,SAAqB;QACjD,IAAI,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAA;QACzD,IAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,SAAS,CAAC,CAAA;IAClD,CAAC;IAED,MAAM,CAAE,GAAS;QACf,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC;YACpD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;IAC5C,CAAC;IAED,IAAI,CAAE,OAAoC,EAAE,OAAsB;QAChE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,CAAA;QACjC,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QAEjD,IAAI,SAAS,CAAa,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACpB,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,CAAA;gBACjC,OAAO,GAAG,CAAA;YACZ,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,CAAA;QACjC,OAAO,GAAG,CAAA;IACZ,CAAC;CACF"}