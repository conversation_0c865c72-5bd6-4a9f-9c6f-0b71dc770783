{"version": 3, "file": "index.browser.js", "sourceRoot": "", "sources": ["../../../../src/keys/ecdh/index.browser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAA;AAC1D,OAAO,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAC/D,OAAO,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAC/D,OAAO,EAAE,QAAQ,IAAI,kBAAkB,EAAE,MAAM,uBAAuB,CAAA;AACtE,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAA;AACjD,OAAO,SAAS,MAAM,0BAA0B,CAAA;AAIhD,MAAM,YAAY,GAAG;IACnB,OAAO,EAAE,EAAE;IACX,OAAO,EAAE,EAAE;IACX,OAAO,EAAE,EAAE;CACZ,CAAA;AAED,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;AAC5C,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAEpC,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAAE,KAAY;IAC1D,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QAChE,MAAM,IAAI,sBAAsB,CAAC,kBAAkB,KAAK,aAAa,KAAK,EAAE,CAAC,CAAA;IAC/E,CAAC;IAED,MAAM,IAAI,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CACnD;QACE,IAAI,EAAE,MAAM;QACZ,UAAU,EAAE,KAAK;KAClB,EACD,IAAI,EACJ,CAAC,YAAY,CAAC,CACf,CAAA;IAED,wCAAwC;IACxC,MAAM,YAAY,GAAG,KAAK,EAAE,QAAoB,EAAE,YAA0B,EAAuB,EAAE;QACnG,IAAI,UAAU,CAAA;QAEd,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,UAAU,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,SAAS,CACjD,KAAK,EACL,mBAAmB,CAAC,KAAK,EAAE,YAAY,CAAC,EACxC;gBACE,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,KAAK;aAClB,EACD,KAAK,EACL,CAAC,YAAY,CAAC,CACf,CAAA;QACH,CAAC;aAAM,CAAC;YACN,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAC9B,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,SAAS,CAChD,KAAK,EACL,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,EACnC;YACE,IAAI,EAAE,MAAM;YACZ,UAAU,EAAE,KAAK;SAClB,EACD,KAAK,EACL,EAAE,CACH,CAAA;QAED,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CACpD;YACE,IAAI,EAAE,MAAM;YACZ,MAAM,EAAE,GAAG;SACZ,EACD,UAAU,EACV,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CACxB,CAAA;QAED,OAAO,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAA;IACrD,CAAC,CAAA;IAED,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;IAE/E,MAAM,OAAO,GAAY;QACvB,GAAG,EAAE,gBAAgB,CAAC,SAAS,CAAC;QAChC,YAAY;KACb,CAAA;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AAED,0DAA0D;AAC1D,qEAAqE;AACrE,gBAAgB;AAChB,SAAS,gBAAgB,CAAE,GAAe;IACxC,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QACtD,MAAM,IAAI,sBAAsB,CAAC,4BAA4B,CAAC,CAAA;IAChE,CAAC;IAED,IAAI,GAAG,CAAC,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK,OAAO,EAAE,CAAC;QACtE,MAAM,IAAI,sBAAsB,CAAC,kBAAkB,GAAG,CAAC,GAAG,aAAa,KAAK,EAAE,CAAC,CAAA;IACjF,CAAC;IAED,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAErC,OAAO,gBAAgB,CAAC;QACtB,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,qBAAqB;QAC3C,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;QACjC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;KAClC,EAAE,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,CAAA;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAE,KAAY,EAAE,GAAe;IACxD,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QAChE,MAAM,IAAI,sBAAsB,CAAC,kBAAkB,KAAK,aAAa,KAAK,EAAE,CAAC,CAAA;IAC/E,CAAC;IAED,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;IAEnC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChE,MAAM,IAAI,sBAAsB,CAAC,kDAAkD,CAAC,CAAA;IACtF,CAAC;IAED,OAAO;QACL,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,KAAK;QACV,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC;QAChE,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,WAAW,CAAC;QAC7D,GAAG,EAAE,IAAI;KACV,CAAA;AACH,CAAC;AAED,MAAM,mBAAmB,GAAG,CAAC,KAAY,EAAE,GAAgB,EAAwB,EAAE,CAAC,CAAC;IACrF,GAAG,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC;IACxC,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC;CAChD,CAAC,CAAA"}