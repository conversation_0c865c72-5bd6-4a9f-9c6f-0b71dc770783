{"version": 3, "file": "secp256k1.js", "sourceRoot": "", "sources": ["../../../../src/keys/secp256k1/secp256k1.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAA;AACrD,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAA;AACtC,OAAO,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAA;AACvD,OAAO,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAC/D,OAAO,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAA;AACjD,OAAO,EAAE,0BAA0B,EAAE,0BAA0B,EAAE,yBAAyB,EAAE,2BAA2B,EAAE,MAAM,YAAY,CAAA;AAC3I,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,YAAY,CAAA;AAKvD,MAAM,OAAO,kBAAkB;IACb,IAAI,GAAG,WAAW,CAAA;IAClB,GAAG,CAAY;IACf,IAAI,CAAY;IAEhC,YAAa,GAAe;QAC1B,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC,GAAG,CAAC,CAAA;QAC3C,IAAI,CAAC,GAAG,GAAG,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAClD,CAAC;IAED,WAAW;QACT,OAAO,QAAQ,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAA;IACnD,CAAC;IAED,KAAK;QACH,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;IAC9C,CAAC;IAED,QAAQ;QACN,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;IAChE,CAAC;IAED,MAAM,CAAE,GAAQ;QACd,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC;YACpD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;IAC5C,CAAC;IAED,MAAM,CAAE,IAAiC,EAAE,GAAe,EAAE,OAAsB;QAChF,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACrD,CAAC;CACF;AAED,MAAM,OAAO,mBAAmB;IACd,IAAI,GAAG,WAAW,CAAA;IAClB,GAAG,CAAY;IACf,SAAS,CAAoB;IAE7C,YAAa,GAAe,EAAE,SAAsB;QAClD,IAAI,CAAC,GAAG,GAAG,2BAA2B,CAAC,GAAG,CAAC,CAAA;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,kBAAkB,CAAC,SAAS,IAAI,yBAAyB,CAAC,GAAG,CAAC,CAAC,CAAA;IACtF,CAAC;IAED,MAAM,CAAE,GAAS;QACf,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC;YACpD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;IAC5C,CAAC;IAED,IAAI,CAAE,OAAoC,EAAE,OAAsB;QAChE,OAAO,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,CAAC;CACF"}