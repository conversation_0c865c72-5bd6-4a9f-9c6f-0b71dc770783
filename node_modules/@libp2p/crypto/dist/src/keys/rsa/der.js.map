{"version": 3, "file": "der.js", "sourceRoot": "", "sources": ["../../../../src/keys/rsa/der.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAA;AAM/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;AACrC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAA;AAChD,MAAM,sBAAsB,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAA;AAMtD,MAAM,QAAQ,GAA4B;IACxC,GAAG,EAAE,YAAY;IACjB,GAAG,EAAE,YAAY;IACjB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,aAAa;IAClB,GAAG,EAAE,eAAe;IACpB,GAAG,EAAE,QAAQ;IACb,GAAG,EAAE,oBAAoB;IACzB,IAAI,EAAE,YAAY;IAClB,IAAI,EAAE,YAAY;IAClB,IAAI,EAAE,YAAY;CACnB,CAAA;AAED,MAAM,UAAU,SAAS,CAAE,GAAe,EAAE,UAAmB,EAAE,MAAM,EAAE,CAAC,EAAE;IAC1E,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAA;IAC1C,OAAO,CAAC,MAAM,EAAE,CAAA;IAEhB,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;QAC1B,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACpC,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,GAAG,CAAC,CAAA;AAC9C,CAAC;AAED,SAAS,UAAU,CAAE,GAAe,EAAE,OAAgB;IACpD,IAAI,MAAM,GAAG,CAAC,CAAA;IAEd,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,KAAK,gBAAgB,EAAE,CAAC;QAClE,cAAc;QACd,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,sBAAsB,CAAA;QAC1D,IAAI,GAAG,GAAG,IAAI,CAAA;QACd,OAAO,CAAC,MAAM,EAAE,CAAA;QAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACjD,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;IAC5B,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC5B,OAAO,CAAC,MAAM,EAAE,CAAA;IAClB,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,YAAY,CAAE,GAAe,EAAE,OAAgB;IACtD,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACxB,MAAM,OAAO,GAAU,EAAE,CAAA;IAEzB,OAAO,IAAI,EAAE,CAAC;QACZ,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;YACrC,MAAK;QACP,CAAC;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QAEtC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACpB,MAAK;QACP,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACtB,CAAC;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AAED,SAAS,WAAW,CAAE,GAAe,EAAE,OAAgB;IACrD,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACvC,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAA;IAC5B,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,MAAM,CAAA;IAEnC,MAAM,IAAI,GAAa,EAAE,CAAA;IAEzB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QACjC,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,SAAQ;QACV,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IACnB,CAAC;IAED,OAAO,CAAC,MAAM,IAAI,MAAM,CAAA;IAExB,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAC9B,CAAC;AAED,SAAS,oBAAoB,CAAE,GAAe,EAAE,OAAgB;IAC9D,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACtC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,KAAK,CAAA;IAE1C,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IAChC,OAAO,CAAC,MAAM,EAAE,CAAA;IAEhB,IAAI,IAAI,GAAG,CAAC,CAAA;IACZ,IAAI,IAAI,GAAG,CAAC,CAAA;IAEZ,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;QACd,IAAI,GAAG,CAAC,CAAA;QACR,IAAI,GAAG,IAAI,CAAA;IACb,CAAC;SAAM,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;QACrB,IAAI,GAAG,CAAC,CAAA;QACR,IAAI,GAAG,IAAI,GAAG,EAAE,CAAA;IAClB,CAAC;SAAM,CAAC;QACN,IAAI,GAAG,CAAC,CAAA;QACR,IAAI,GAAG,IAAI,GAAG,EAAE,CAAA;IAClB,CAAC;IAED,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,CAAA;IAC3B,IAAI,GAAG,GAAa,EAAE,CAAA;IAEtB,OAAO,OAAO,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;QACpC,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAChC,OAAO,CAAC,MAAM,EAAE,CAAA;QAEhB,aAAa;QACb,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,CAAA;QAE3B,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;YACf,GAAG,CAAC,OAAO,EAAE,CAAA;YAEb,kCAAkC;YAClC,IAAI,GAAG,GAAG,CAAC,CAAA;YAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACpC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YAC1B,CAAC;YAED,GAAG,IAAI,IAAI,GAAG,EAAE,CAAA;YAChB,GAAG,GAAG,EAAE,CAAA;QACV,CAAC;IACH,CAAC;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,SAAS,QAAQ,CAAE,GAAe,EAAE,OAAgB;IAClD,OAAO,CAAC,MAAM,EAAE,CAAA;IAEhB,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,aAAa,CAAE,GAAe,EAAE,OAAgB;IACvD,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACvC,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACtC,OAAO,CAAC,MAAM,EAAE,CAAA;IAChB,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,CAAA;IACvE,OAAO,CAAC,MAAM,IAAI,MAAM,CAAA;IAExB,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;QACrB,kDAAkD;QAClD,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;IAC/D,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,eAAe,CAAE,GAAe,EAAE,OAAgB;IACzD,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACvC,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,CAAA;IACnE,OAAO,CAAC,MAAM,IAAI,MAAM,CAAA;IAExB,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,YAAY,CAAE,KAAa;IAClC,IAAI,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAE/B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5B,MAAM,GAAG,GAAG,GAAG,MAAM,CAAA;IACvB,CAAC;IAED,MAAM,KAAK,GAAG,IAAI,cAAc,EAAE,CAAA;IAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1C,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IAC/E,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,YAAY,CAAE,KAA6B;IAClD,IAAI,KAAK,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;QAC3B,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;IAC5C,CAAC;IAED,cAAc;IACd,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;IAE7C,OAAO,IAAI,cAAc,CACvB,UAAU,CAAC,IAAI,CAAC;QACd,MAAM,CAAC,UAAU,GAAG,gBAAgB;KACrC,CAAC,EACF,MAAM,CACP,CAAA;AACH,CAAC;AAED,MAAM,UAAU,aAAa,CAAE,KAAkC;IAC/D,MAAM,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAA;IAErC,MAAM,IAAI,GAAG,UAAU,CAAA;IACvB,MAAM,QAAQ,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAA;IAEtD,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACvC,CAAC;IAED,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAEtB,OAAO,IAAI,cAAc,CACvB,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EACvB,YAAY,CAAC,QAAQ,CAAC,EACtB,QAAQ,CACT,CAAA;AACH,CAAC;AAED,MAAM,UAAU,eAAe,CAAE,KAAkC;IACjE,qDAAqD;IACrD,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAEvC,MAAM,QAAQ,GAAG,IAAI,cAAc,CACjC,UAAU,EACV,KAAK,CACN,CAAA;IAED,OAAO,IAAI,cAAc,CACvB,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EACvB,YAAY,CAAC,QAAQ,CAAC,EACtB,QAAQ,CACT,CAAA;AACH,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAE,KAAkC;IACnE,OAAO,IAAI,cAAc,CACvB,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EACvB,YAAY,CAAC,KAAK,CAAC,EACnB,KAAK,CACN,CAAA;AACH,CAAC;AAED,MAAM,UAAU,cAAc,CAAE,MAA0C,EAAE,GAAG,GAAG,IAAI;IACpF,MAAM,MAAM,GAAG,IAAI,cAAc,EAAE,CAAA;IAEnC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QACzB,MAAM,CAAC,MAAM,CACX,GAAG,CACJ,CAAA;IACH,CAAC;IAED,OAAO,IAAI,cAAc,CACvB,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EACtB,YAAY,CAAC,MAAM,CAAC,EACpB,MAAM,CACP,CAAA;AACH,CAAC"}