{"version": 3, "file": "peer-store.d.ts", "sourceRoot": "", "sources": ["../../src/peer-store.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,YAAY,CAAA;AAC9C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AAC1C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,cAAc,CAAA;AAC1C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAA;AAC9C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAA;AAExD;;;;;;;GAOG;AACH,eAAO,MAAM,UAAU,eAAe,CAAA;AAEtC;;GAEG;AACH,MAAM,WAAW,OAAO;IACtB;;OAEG;IACH,SAAS,EAAE,SAAS,CAAA;IAEpB;;OAEG;IACH,WAAW,EAAE,OAAO,CAAA;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,IAAI;IACnB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAA;IAEV;;;OAGG;IACH,SAAS,EAAE,OAAO,EAAE,CAAA;IAEpB;;OAEG;IACH,SAAS,EAAE,MAAM,EAAE,CAAA;IAEnB;;OAEG;IACH,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;IAEjC;;OAEG;IACH,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IAEtB;;OAEG;IACH,kBAAkB,CAAC,EAAE,UAAU,CAAA;CAChC;AAED;;GAEG;AACH,MAAM,WAAW,QAAQ;IACvB;;;;;;;OAOG;IACH,SAAS,CAAC,EAAE,OAAO,EAAE,CAAA;IAErB;;;;;;OAMG;IACH,UAAU,CAAC,EAAE,SAAS,EAAE,CAAA;IAExB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,EAAE,CAAA;IAEpB;;OAEG;IACH,QAAQ,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,GAAG,SAAS,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,UAAU,GAAG,SAAS,CAAC,CAAA;IAEvF;;OAEG;IACH,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,GAAG,SAAS,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,UAAU,GAAG,SAAS,CAAC,CAAA;IAEnF;;;;OAIG;IACH,SAAS,CAAC,EAAE,SAAS,CAAA;IAErB;;OAEG;IACH,kBAAkB,CAAC,EAAE,UAAU,CAAA;CAChC;AAED,MAAM,WAAW,UAAU;IACzB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IAEd;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAA;CACb;AAED,MAAM,WAAW,GAAG;IAClB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAA;CACd;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAAG,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO,CAAA;CAAE;AAE1D;;GAEG;AACH,MAAM,WAAW,cAAc;IAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;CAAE;AAElE;;GAEG;AACH,MAAM,WAAW,SAAU,SAAQ,YAAY;IAC7C,OAAO,CAAC,EAAE,eAAe,EAAE,CAAA;IAC3B,MAAM,CAAC,EAAE,cAAc,EAAE,CAAA;IACzB,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,MAAM,CAAC,EAAE,MAAM,CAAA;CAChB;AAED,MAAM,WAAW,wBAAyB,SAAQ,YAAY;IAC5D,YAAY,CAAC,EAAE,MAAM,CAAA;CACtB;AAED,MAAM,WAAW,SAAS;IACxB;;;;;;;;;;;;;;OAcG;IACH,OAAO,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAEnE;;;;;;;;;;OAUG;IACH,GAAG,CAAC,KAAK,CAAC,EAAE,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;IAEvC;;;;;;;;;;;;;;;OAeG;IACH,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAE7D;;;;;;;;;;;;OAYG;IACH,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;IAE7D;;;;;;;;;OASG;IACH,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAE1D;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,OAAO,CAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAA;IAEnE;;;;;;;;;;OAUG;IACH,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAEvE;;;;;;;;;;OAUG;IACH,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAExE;;;;;;;;;;OAUG;IACH,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAExE;;;;;;;;;;;;OAYG;IACH,iBAAiB,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,wBAAwB,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;IAExF;;OAEG;IACH,iBAAiB,CAAC,GAAG,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;CACpG"}