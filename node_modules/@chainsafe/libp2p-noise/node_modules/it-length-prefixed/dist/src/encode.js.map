{"version": 3, "file": "encode.js", "sourceRoot": "", "sources": ["../../src/encode.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,MAAM,cAAc,CAAA;AACtC,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAA;AAC/C,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAA;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAA;AAChD,OAAO,EAAE,sBAAsB,EAAE,MAAM,aAAa,CAAA;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AAI5C,mEAAmE;AACnE,SAAS,qBAAqB,CAAE,KAAkC,EAAE,aAAqB;IACvF,IAAI,KAAK,CAAC,UAAU,GAAG,aAAa,EAAE,CAAC;QACrC,MAAM,IAAI,sBAAsB,CAAC,yBAAyB,CAAC,CAAA;IAC7D,CAAC;AACH,CAAC;AAED,MAAM,cAAc,GAA0B,CAAC,MAAM,EAAE,EAAE;IACvD,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;IAClD,MAAM,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,CAAA;IAE3C,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;IAEhC,cAAc,CAAC,KAAK,GAAG,YAAY,CAAA;IAEnC,OAAO,SAAS,CAAA;AAClB,CAAC,CAAA;AACD,cAAc,CAAC,KAAK,GAAG,CAAC,CAAA;AAIxB,MAAM,UAAU,MAAM,CAAE,MAA2C,EAAE,OAAwB;IAC3F,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;IAEvB,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,IAAI,cAAc,CAAA;IAC5D,MAAM,aAAa,GAAG,OAAO,EAAE,aAAa,IAAI,eAAe,CAAA;IAE/D,QAAS,CAAC,CAAC,UAAU,CAAE,KAAkC;QACvD,qBAAqB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAA;QAE3C,gBAAgB;QAChB,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;QAE7C,yBAAyB;QACzB,IAAI,MAAM,YAAY,UAAU,EAAE,CAAC;YACjC,MAAM,MAAM,CAAA;QACd,CAAC;aAAM,CAAC;YACN,KAAM,CAAC,CAAC,MAAM,CAAA;QAChB,CAAC;QAED,yBAAyB;QACzB,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;YAChC,MAAM,KAAK,CAAA;QACb,CAAC;aAAM,CAAC;YACN,KAAM,CAAC,CAAC,KAAK,CAAA;QACf,CAAC;IACH,CAAC;IAED,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5B,OAAO,CAAC,KAAK,SAAU,CAAC;YACtB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,KAAM,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;YAC3B,CAAC;QACH,CAAC,CAAC,EAAE,CAAA;IACN,CAAC;IAED,OAAO,CAAC,QAAS,CAAC;QAChB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,KAAM,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;IACH,CAAC,CAAC,EAAE,CAAA;AACN,CAAC;AAED,MAAM,CAAC,MAAM,GAAG,CAAC,KAAkC,EAAE,OAAwB,EAAE,EAAE;IAC/E,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;IACvB,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,IAAI,cAAc,CAAA;IAC5D,MAAM,aAAa,GAAG,OAAO,EAAE,aAAa,IAAI,eAAe,CAAA;IAE/D,qBAAqB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAA;IAE3C,OAAO,IAAI,cAAc,CACvB,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,EAC9B,KAAK,CACN,CAAA;AACH,CAAC,CAAA"}