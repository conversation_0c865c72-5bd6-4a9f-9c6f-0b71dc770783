/**
 * The reported length of the next data message was not a positive integer
 */
export declare class InvalidMessageLengthError extends Error {
    name: string;
    code: string;
}
/**
 * The reported length of the next data message was larger than the configured
 * max allowable value
 */
export declare class InvalidDataLengthError extends <PERSON><PERSON>r {
    name: string;
    code: string;
}
/**
 * The varint used to specify the length of the next data message contained more
 * bytes than the configured max allowable value
 */
export declare class InvalidDataLengthLengthError extends <PERSON>rror {
    name: string;
    code: string;
}
/**
 * The incoming stream ended before the expected number of bytes were read
 */
export declare class UnexpectedEOFError extends Error {
    name: string;
    code: string;
}
//# sourceMappingURL=errors.d.ts.map