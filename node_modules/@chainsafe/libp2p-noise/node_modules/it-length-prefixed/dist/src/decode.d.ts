import { Uint8ArrayList } from 'uint8arraylist';
import type { DecoderOptions } from './index.js';
import type { Reader } from 'it-reader';
import type { Source } from 'it-stream-types';
export declare function decode(source: Iterable<Uint8ArrayList | Uint8Array>, options?: DecoderOptions): Generator<Uint8ArrayList, void, unknown>;
export declare function decode(source: Source<Uint8ArrayList | Uint8Array>, options?: DecoderOptions): AsyncGenerator<Uint8ArrayList, void, unknown>;
export declare namespace decode {
    var fromReader: (reader: Reader, options?: DecoderOptions | undefined) => AsyncGenerator<Uint8ArrayList, void, unknown>;
}
//# sourceMappingURL=decode.d.ts.map