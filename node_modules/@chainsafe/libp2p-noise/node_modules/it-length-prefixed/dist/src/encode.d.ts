import { Uint8ArrayList } from 'uint8arraylist';
import type { EncoderOptions } from './index.js';
import type { Source } from 'it-stream-types';
export declare function encode(source: Iterable<Uint8ArrayList | Uint8Array>, options?: EncoderOptions): Generator<Uint8Array, void, undefined>;
export declare function encode(source: Source<Uint8ArrayList | Uint8Array>, options?: EncoderOptions): AsyncGenerator<Uint8Array, void, undefined>;
export declare namespace encode {
    var single: (chunk: Uint8Array | Uint8ArrayList, options?: EncoderOptions | undefined) => Uint8ArrayList;
}
//# sourceMappingURL=encode.d.ts.map