/**
 * @packageDocumentation
 *
 * Encode/decode streams of bytes with length-prefixes.
 *
 * @example
 *
 * ```js
 * import { pipe } from 'it-pipe'
 * import * as lp from 'it-length-prefixed'
 *
 * const encoded = []
 *
 * // encode
 * await pipe(
 *   [uint8ArrayFromString('hello world')],
 *   (source) => lp.encode(source),
 *   async source => {
 *     for await (const chunk of source) {
 *       encoded.push(chunk.slice()) // (.slice converts Uint8ArrayList to Uint8Array)
 *     }
 *   }
 * )
 *
 * console.log(encoded)
 * // => [Buffer <0b 68 65 6c 6c 6f 20 77 6f 72 6c 64>]
 *
 * const decoded = []
 *
 * // decode
 * await pipe(
 *   encoded, // e.g. from above
 *   (source) => lp.decode(source),
 *   async source => {
 *     for await (const chunk of source) {
 *       decoded.push(chunk.slice()) // (.slice converts Uint8ArrayList to Uint8Array)
 *     }
 *   }
 * )
 *
 * console.log(decoded)
 * // => [<PERSON><PERSON><PERSON> <68 65 6c 6c 6f 20 77 6f 72 6c 64>]
 * ```
 */
export { encode } from './encode.js';
export { decode } from './decode.js';
//# sourceMappingURL=index.js.map