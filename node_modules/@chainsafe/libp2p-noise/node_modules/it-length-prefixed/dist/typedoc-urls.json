{"decode": "https://alanshaw.github.io/it-length-prefixed/functions/decode.decode.html", "./decode:decode": "https://alanshaw.github.io/it-length-prefixed/functions/decode.decode.html", "encode": "https://alanshaw.github.io/it-length-prefixed/functions/encode.encode.html", "./encode:encode": "https://alanshaw.github.io/it-length-prefixed/functions/encode.encode.html", "DecoderOptions": "https://alanshaw.github.io/it-length-prefixed/interfaces/index.DecoderOptions.html", ".:DecoderOptions": "https://alanshaw.github.io/it-length-prefixed/interfaces/index.DecoderOptions.html", "EncoderOptions": "https://alanshaw.github.io/it-length-prefixed/interfaces/index.EncoderOptions.html", ".:EncoderOptions": "https://alanshaw.github.io/it-length-prefixed/interfaces/index.EncoderOptions.html", "LengthDecoderFunction": "https://alanshaw.github.io/it-length-prefixed/interfaces/index.LengthDecoderFunction.html", ".:LengthDecoderFunction": "https://alanshaw.github.io/it-length-prefixed/interfaces/index.LengthDecoderFunction.html", "LengthEncoderFunction": "https://alanshaw.github.io/it-length-prefixed/interfaces/index.LengthEncoderFunction.html", ".:LengthEncoderFunction": "https://alanshaw.github.io/it-length-prefixed/interfaces/index.LengthEncoderFunction.html"}