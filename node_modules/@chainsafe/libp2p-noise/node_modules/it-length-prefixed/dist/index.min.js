(function (root, factory) {(typeof module === 'object' && module.exports) ? module.exports = factory() : root.ItLengthPrefixed = factory()}(typeof self !== 'undefined' ? self : this, function () {
"use strict";var ItLengthPrefixed=(()=>{var _=Object.defineProperty;var Y=Object.getOwnPropertyDescriptor;var z=Object.getOwnPropertyNames;var J=Object.prototype.hasOwnProperty;var K=(r,t)=>{for(var e in t)_(r,e,{get:t[e],enumerable:!0})},Q=(r,t,e,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of z(t))!J.call(r,i)&&i!==e&&_(r,i,{get:()=>t[i],enumerable:!(n=Y(t,i))||n.enumerable});return r};var W=r=>Q(_({},"__esModule",{value:!0}),r);var st={};K(st,{decode:()=>T,encode:()=>I});function w(r=0){return new Uint8Array(r)}function l(r=0){return new Uint8Array(r)}var Z=Math.pow(2,7),$=Math.pow(2,14),v=Math.pow(2,21),M=Math.pow(2,28),G=Math.pow(2,35),R=Math.pow(2,42),V=Math.pow(2,49),o=128,c=127;function y(r){if(r<Z)return 1;if(r<$)return 2;if(r<v)return 3;if(r<M)return 4;if(r<G)return 5;if(r<R)return 6;if(r<V)return 7;if(Number.MAX_SAFE_INTEGER!=null&&r>Number.MAX_SAFE_INTEGER)throw new RangeError("Could not encode varint");return 8}function tt(r,t,e=0){switch(y(r)){case 8:t[e++]=r&255|o,r/=128;case 7:t[e++]=r&255|o,r/=128;case 6:t[e++]=r&255|o,r/=128;case 5:t[e++]=r&255|o,r/=128;case 4:t[e++]=r&255|o,r>>>=7;case 3:t[e++]=r&255|o,r>>>=7;case 2:t[e++]=r&255|o,r>>>=7;case 1:{t[e++]=r&255,r>>>=7;break}default:throw new Error("unreachable")}return t}function et(r,t,e=0){switch(y(r)){case 8:t.set(e++,r&255|o),r/=128;case 7:t.set(e++,r&255|o),r/=128;case 6:t.set(e++,r&255|o),r/=128;case 5:t.set(e++,r&255|o),r/=128;case 4:t.set(e++,r&255|o),r>>>=7;case 3:t.set(e++,r&255|o),r>>>=7;case 2:t.set(e++,r&255|o),r>>>=7;case 1:{t.set(e++,r&255),r>>>=7;break}default:throw new Error("unreachable")}return t}function nt(r,t){let e=r[t],n=0;if(n+=e&c,e<o||(e=r[t+1],n+=(e&c)<<7,e<o)||(e=r[t+2],n+=(e&c)<<14,e<o)||(e=r[t+3],n+=(e&c)<<21,e<o)||(e=r[t+4],n+=(e&c)*M,e<o)||(e=r[t+5],n+=(e&c)*G,e<o)||(e=r[t+6],n+=(e&c)*R,e<o)||(e=r[t+7],n+=(e&c)*V,e<o))return n;throw new RangeError("Could not decode varint")}function rt(r,t){let e=r.get(t),n=0;if(n+=e&c,e<o||(e=r.get(t+1),n+=(e&c)<<7,e<o)||(e=r.get(t+2),n+=(e&c)<<14,e<o)||(e=r.get(t+3),n+=(e&c)<<21,e<o)||(e=r.get(t+4),n+=(e&c)*M,e<o)||(e=r.get(t+5),n+=(e&c)*G,e<o)||(e=r.get(t+6),n+=(e&c)*R,e<o)||(e=r.get(t+7),n+=(e&c)*V,e<o))return n;throw new RangeError("Could not decode varint")}function S(r,t,e=0){return t==null&&(t=l(y(r))),t instanceof Uint8Array?tt(r,t,e):et(r,t,e)}function X(r,t=0){return r instanceof Uint8Array?nt(r,t):rt(r,t)}function O(r,t){t==null&&(t=r.reduce((i,s)=>i+s.length,0));let e=l(t),n=0;for(let i of r)e.set(i,n),n+=i.length;return e}function C(r,t){if(r===t)return!0;if(r.byteLength!==t.byteLength)return!1;for(let e=0;e<r.byteLength;e++)if(r[e]!==t[e])return!1;return!0}var P=Symbol.for("@achingbrain/uint8arraylist");function q(r,t){if(t==null||t<0)throw new RangeError("index is out of bounds");let e=0;for(let n of r){let i=e+n.byteLength;if(t<i)return{buf:n,index:t-e};e=i}throw new RangeError("index is out of bounds")}function x(r){return!!r?.[P]}var d=class r{bufs;length;[P]=!0;constructor(...t){this.bufs=[],this.length=0,t.length>0&&this.appendAll(t)}*[Symbol.iterator](){yield*this.bufs}get byteLength(){return this.length}append(...t){this.appendAll(t)}appendAll(t){let e=0;for(let n of t)if(n instanceof Uint8Array)e+=n.byteLength,this.bufs.push(n);else if(x(n))e+=n.byteLength,this.bufs.push(...n.bufs);else throw new Error("Could not append value, must be an Uint8Array or a Uint8ArrayList");this.length+=e}prepend(...t){this.prependAll(t)}prependAll(t){let e=0;for(let n of t.reverse())if(n instanceof Uint8Array)e+=n.byteLength,this.bufs.unshift(n);else if(x(n))e+=n.byteLength,this.bufs.unshift(...n.bufs);else throw new Error("Could not prepend value, must be an Uint8Array or a Uint8ArrayList");this.length+=e}get(t){let e=q(this.bufs,t);return e.buf[e.index]}set(t,e){let n=q(this.bufs,t);n.buf[n.index]=e}write(t,e=0){if(t instanceof Uint8Array)for(let n=0;n<t.length;n++)this.set(e+n,t[n]);else if(x(t))for(let n=0;n<t.length;n++)this.set(e+n,t.get(n));else throw new Error("Could not write value, must be an Uint8Array or a Uint8ArrayList")}consume(t){if(t=Math.trunc(t),!(Number.isNaN(t)||t<=0)){if(t===this.byteLength){this.bufs=[],this.length=0;return}for(;this.bufs.length>0;)if(t>=this.bufs[0].byteLength)t-=this.bufs[0].byteLength,this.length-=this.bufs[0].byteLength,this.bufs.shift();else{this.bufs[0]=this.bufs[0].subarray(t),this.length-=t;break}}}slice(t,e){let{bufs:n,length:i}=this._subList(t,e);return O(n,i)}subarray(t,e){let{bufs:n,length:i}=this._subList(t,e);return n.length===1?n[0]:O(n,i)}sublist(t,e){let{bufs:n,length:i}=this._subList(t,e),s=new r;return s.length=i,s.bufs=[...n],s}_subList(t,e){if(t=t??0,e=e??this.length,t<0&&(t=this.length+t),e<0&&(e=this.length+e),t<0||e>this.length)throw new RangeError("index is out of bounds");if(t===e)return{bufs:[],length:0};if(t===0&&e===this.length)return{bufs:this.bufs,length:this.length};let n=[],i=0;for(let s=0;s<this.bufs.length;s++){let a=this.bufs[s],u=i,g=u+a.byteLength;if(i=g,t>=g)continue;let h=t>=u&&t<g,b=e>u&&e<=g;if(h&&b){if(t===u&&e===g){n.push(a);break}let f=t-u;n.push(a.subarray(f,f+(e-t)));break}if(h){if(t===0){n.push(a);continue}n.push(a.subarray(t-u));continue}if(b){if(e===g){n.push(a);break}n.push(a.subarray(0,e-u));break}n.push(a)}return{bufs:n,length:e-t}}indexOf(t,e=0){if(!x(t)&&!(t instanceof Uint8Array))throw new TypeError('The "value" argument must be a Uint8ArrayList or Uint8Array');let n=t instanceof Uint8Array?t:t.subarray();if(e=Number(e??0),isNaN(e)&&(e=0),e<0&&(e=this.length+e),e<0&&(e=0),t.length===0)return e>this.length?this.length:e;let i=n.byteLength;if(i===0)throw new TypeError("search must be at least 1 byte long");let s=256,a=new Int32Array(s);for(let f=0;f<s;f++)a[f]=-1;for(let f=0;f<i;f++)a[n[f]]=f;let u=a,g=this.byteLength-n.byteLength,h=n.byteLength-1,b;for(let f=e;f<=g;f+=b){b=0;for(let m=h;m>=0;m--){let H=this.get(f+m);if(n[m]!==H){b=Math.max(1,m-u[H]);break}}if(b===0)return f}return-1}getInt8(t){let e=this.subarray(t,t+1);return new DataView(e.buffer,e.byteOffset,e.byteLength).getInt8(0)}setInt8(t,e){let n=l(1);new DataView(n.buffer,n.byteOffset,n.byteLength).setInt8(0,e),this.write(n,t)}getInt16(t,e){let n=this.subarray(t,t+2);return new DataView(n.buffer,n.byteOffset,n.byteLength).getInt16(0,e)}setInt16(t,e,n){let i=w(2);new DataView(i.buffer,i.byteOffset,i.byteLength).setInt16(0,e,n),this.write(i,t)}getInt32(t,e){let n=this.subarray(t,t+4);return new DataView(n.buffer,n.byteOffset,n.byteLength).getInt32(0,e)}setInt32(t,e,n){let i=w(4);new DataView(i.buffer,i.byteOffset,i.byteLength).setInt32(0,e,n),this.write(i,t)}getBigInt64(t,e){let n=this.subarray(t,t+8);return new DataView(n.buffer,n.byteOffset,n.byteLength).getBigInt64(0,e)}setBigInt64(t,e,n){let i=w(8);new DataView(i.buffer,i.byteOffset,i.byteLength).setBigInt64(0,e,n),this.write(i,t)}getUint8(t){let e=this.subarray(t,t+1);return new DataView(e.buffer,e.byteOffset,e.byteLength).getUint8(0)}setUint8(t,e){let n=l(1);new DataView(n.buffer,n.byteOffset,n.byteLength).setUint8(0,e),this.write(n,t)}getUint16(t,e){let n=this.subarray(t,t+2);return new DataView(n.buffer,n.byteOffset,n.byteLength).getUint16(0,e)}setUint16(t,e,n){let i=w(2);new DataView(i.buffer,i.byteOffset,i.byteLength).setUint16(0,e,n),this.write(i,t)}getUint32(t,e){let n=this.subarray(t,t+4);return new DataView(n.buffer,n.byteOffset,n.byteLength).getUint32(0,e)}setUint32(t,e,n){let i=w(4);new DataView(i.buffer,i.byteOffset,i.byteLength).setUint32(0,e,n),this.write(i,t)}getBigUint64(t,e){let n=this.subarray(t,t+8);return new DataView(n.buffer,n.byteOffset,n.byteLength).getBigUint64(0,e)}setBigUint64(t,e,n){let i=w(8);new DataView(i.buffer,i.byteOffset,i.byteLength).setBigUint64(0,e,n),this.write(i,t)}getFloat32(t,e){let n=this.subarray(t,t+4);return new DataView(n.buffer,n.byteOffset,n.byteLength).getFloat32(0,e)}setFloat32(t,e,n){let i=w(4);new DataView(i.buffer,i.byteOffset,i.byteLength).setFloat32(0,e,n),this.write(i,t)}getFloat64(t,e){let n=this.subarray(t,t+8);return new DataView(n.buffer,n.byteOffset,n.byteLength).getFloat64(0,e)}setFloat64(t,e,n){let i=w(8);new DataView(i.buffer,i.byteOffset,i.byteLength).setFloat64(0,e,n),this.write(i,t)}equals(t){if(t==null||!(t instanceof r)||t.bufs.length!==this.bufs.length)return!1;for(let e=0;e<this.bufs.length;e++)if(!C(this.bufs[e],t.bufs[e]))return!1;return!0}static fromUint8Arrays(t,e){let n=new r;return n.bufs=t,e==null&&(e=t.reduce((i,s)=>i+s.byteLength,0)),n.length=e,n}};var U=class extends Error{name="InvalidMessageLengthError";code="ERR_INVALID_MSG_LENGTH"},p=class extends Error{name="InvalidDataLengthError";code="ERR_MSG_DATA_TOO_LONG"},E=class extends Error{name="InvalidDataLengthLengthError";code="ERR_MSG_LENGTH_TOO_LONG"},A=class extends Error{name="UnexpectedEOFError";code="ERR_UNEXPECTED_EOF"};function D(r){return r[Symbol.asyncIterator]!=null}function j(r,t){if(r.byteLength>t)throw new p("Message length too long")}var N=r=>{let t=y(r),e=l(t);return S(r,e),N.bytes=t,e};N.bytes=0;function I(r,t){t=t??{};let e=t.lengthEncoder??N,n=t?.maxDataLength??4194304;function*i(s){j(s,n);let a=e(s.byteLength);a instanceof Uint8Array?yield a:yield*a,s instanceof Uint8Array?yield s:yield*s}return D(r)?async function*(){for await(let s of r)yield*i(s)}():function*(){for(let s of r)yield*i(s)}()}I.single=(r,t)=>{t=t??{};let e=t.lengthEncoder??N,n=t?.maxDataLength??4194304;return j(r,n),new d(e(r.byteLength),r)};var L;(function(r){r[r.LENGTH=0]="LENGTH",r[r.DATA=1]="DATA"})(L||(L={}));var B=r=>{let t=X(r);return B.bytes=y(t),t};B.bytes=0;function T(r,t){let e=new d,n=L.LENGTH,i=-1,s=t?.lengthDecoder??B,a=t?.maxLengthLength??8,u=t?.maxDataLength??4194304;function*g(){for(;e.byteLength>0;){if(n===L.LENGTH)try{if(i=s(e),i<0)throw new U("Invalid message length");if(i>u)throw new p("Message length too long");let h=s.bytes;e.consume(h),t?.onLength!=null&&t.onLength(i),n=L.DATA}catch(h){if(h instanceof RangeError){if(e.byteLength>a)throw new E("Message length length too long");break}throw h}if(n===L.DATA){if(e.byteLength<i)break;let h=e.sublist(0,i);e.consume(i),t?.onData!=null&&t.onData(h),yield h,n=L.LENGTH}}}return D(r)?async function*(){for await(let h of r)e.append(h),yield*g();if(e.byteLength>0)throw new A("Unexpected end of input")}():function*(){for(let h of r)e.append(h),yield*g();if(e.byteLength>0)throw new A("Unexpected end of input")}()}T.fromReader=(r,t)=>{let e=1,n=async function*(){for(;;)try{let{done:s,value:a}=await r.next(e);if(s===!0)return;a!=null&&(yield a)}catch(s){if(s.code==="ERR_UNDER_READ")return{done:!0,value:null};throw s}finally{e=1}}();return T(n,{...t??{},onLength:s=>{e=s}})};return W(st);})();
return ItLengthPrefixed}));
