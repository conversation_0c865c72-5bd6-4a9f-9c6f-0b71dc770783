{"name": "it-length-prefixed", "version": "10.0.1", "description": "Streaming length prefixed buffers with async iterables", "author": "<PERSON>", "license": "Apache-2.0 OR MIT", "homepage": "https://github.com/alanshaw/it-length-prefixed#readme", "repository": {"type": "git", "url": "git+https://github.com/alanshaw/it-length-prefixed.git"}, "bugs": {"url": "https://github.com/alanshaw/it-length-prefixed/issues"}, "publishConfig": {"access": "public", "provenance": true}, "keywords": ["async", "iterable", "iterator", "length-prefixed", "length-prefixed-stream", "varint"], "type": "module", "types": "./dist/src/index.d.ts", "typesVersions": {"*": {"*": ["*", "dist/*", "dist/src/*", "dist/src/*/index"], "src/*": ["*", "dist/*", "dist/src/*", "dist/src/*/index"]}}, "files": ["src", "dist", "!dist/test", "!**/*.tsbuildinfo"], "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./dist/src/index.js"}, "./decode": {"types": "./dist/src/decode.d.ts", "import": "./dist/src/decode.js"}, "./encode": {"types": "./dist/src/encode.d.ts", "import": "./dist/src/encode.js"}}, "eslintConfig": {"extends": "ipfs", "parserOptions": {"project": true, "sourceType": "module"}, "ignorePatterns": ["benchmarks"]}, "release": {"branches": ["master"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"breaking": true, "release": "major"}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"type": "deps", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Documentation"}, {"type": "deps", "section": "Dependencies"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"]}, "scripts": {"clean": "aegir clean", "lint": "aegir lint", "dep-check": "aegir dep-check", "build": "aegir build", "test": "aegir test", "test:chrome": "aegir test -t browser --cov", "test:chrome-webworker": "aegir test -t webworker", "test:firefox": "aegir test -t browser -- --browser firefox", "test:firefox-webworker": "aegir test -t webworker -- --browser firefox", "test:node": "aegir test -t node --cov", "test:electron-main": "aegir test -t electron-main", "release": "aegir release", "docs": "aegir docs"}, "dependencies": {"it-reader": "^6.0.1", "it-stream-types": "^2.0.1", "uint8-varint": "^2.0.1", "uint8arraylist": "^2.0.0", "uint8arrays": "^5.0.1"}, "devDependencies": {"aegir": "^45.0.8", "iso-random-stream": "^2.0.0", "it-all": "^3.0.0", "it-block": "^6.0.0", "it-drain": "^3.0.0", "it-foreach": "^2.0.0", "it-map": "^3.0.0", "it-pipe": "^3.0.0", "it-pushable": "^3.0.0", "p-defer": "^4.0.0", "random-int": "^3.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}