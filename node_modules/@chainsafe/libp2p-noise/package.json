{"name": "@chainsafe/libp2p-noise", "version": "16.1.4", "description": "Noise libp2p handshake for js-libp2p", "author": "ChainSafe <<EMAIL>>", "license": "Apache-2.0 OR MIT", "homepage": "https://github.com/ChainSafe/js-libp2p-noise#readme", "repository": {"type": "git", "url": "git+https://github.com/ChainSafe/js-libp2p-noise.git"}, "bugs": {"url": "https://github.com/ChainSafe/js-libp2p-noise/issues"}, "publishConfig": {"access": "public", "provenance": true}, "keywords": ["crypto", "libp2p", "noise"], "type": "module", "types": "./dist/src/index.d.ts", "files": ["src", "dist", "!dist/test", "!**/*.tsbuildinfo"], "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./dist/src/index.js"}}, "release": {"branches": ["master"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"breaking": true, "release": "major"}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"type": "deps", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Documentation"}, {"type": "deps", "section": "Dependencies"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json"]}]]}, "scripts": {"bench": "node benchmarks/benchmark.js", "clean": "aegir clean", "dep-check": "aegir dep-check", "build": "aegir build", "lint": "aegir lint", "lint:fix": "aegir lint --fix", "test": "aegir test", "test:node": "aegir test -t node", "test:browser": "aegir test -t browser -t webworker", "test:electron-main": "aegir test -t electron-main", "test:interop": "aegir test -t node -f dist/test/interop.js", "docs": "aegir docs", "proto:gen": "protons ./src/proto/payload.proto", "prepublish": "npm run build", "release": "aegir release"}, "dependencies": {"@chainsafe/as-chacha20poly1305": "^0.1.0", "@chainsafe/as-sha256": "^1.0.0", "@libp2p/crypto": "^5.0.0", "@libp2p/interface": "^2.9.0", "@libp2p/peer-id": "^5.0.0", "@noble/ciphers": "^1.1.3", "@noble/curves": "^1.1.0", "@noble/hashes": "^1.3.1", "it-length-prefixed": "^10.0.1", "it-length-prefixed-stream": "^2.0.1", "it-pair": "^2.0.6", "it-pipe": "^3.0.1", "it-stream-types": "^2.0.1", "protons-runtime": "^5.5.0", "uint8arraylist": "^2.4.3", "uint8arrays": "^5.0.0", "wherearewe": "^2.0.1"}, "devDependencies": {"@chainsafe/libp2p-yamux": "^7.0.0", "@libp2p/daemon-client": "^9.0.0", "@libp2p/daemon-server": "^8.0.0", "@libp2p/interface-compliance-tests": "^6.0.0", "@libp2p/interop": "^13.0.0", "@libp2p/logger": "^5.0.0", "@libp2p/tcp": "^10.0.0", "@multiformats/multiaddr": "^12.1.0", "@types/sinon": "^17.0.1", "aegir": "^47.0.18", "benchmark": "^2.1.4", "execa": "^9.3.0", "go-libp2p": "^1.0.3", "iso-random-stream": "^2.0.2", "it-byte-stream": "^2.0.1", "libp2p": "^2.0.0", "mkdirp": "^3.0.0", "multiformats": "^13.2.2", "p-defer": "^4.0.0", "protons": "^7.6.0", "sinon": "^21.0.0", "sinon-ts": "^2.0.0"}, "browser": {"./dist/src/crypto/index.js": "./dist/src/crypto/index.browser.js"}}