import { NoiseHandshakePayload } from './proto/payload.js';
import type { NoiseExtensions } from './proto/payload.js';
import type { PrivateKey, PublicKey } from '@libp2p/interface';
import type { Uint8ArrayList } from 'uint8arraylist';
export declare function createHandshakePayload(privateKey: PrivateKey, staticPublicKey: Uint8Array | Uint8ArrayList, extensions?: NoiseExtensions): Promise<Uint8Array | Uint8ArrayList>;
export declare function decodeHandshakePayload(payloadBytes: Uint8Array | Uint8ArrayList, remoteStaticKey?: Uint8Array | Uint8ArrayList, remoteIdentityKey?: PublicKey): Promise<NoiseHandshakePayload>;
export declare function getSignaturePayload(publicKey: Uint8Array | Uint8ArrayList): Uint8Array | Uint8ArrayList;
//# sourceMappingURL=utils.d.ts.map