{"version": 3, "file": "protocol.js", "sourceRoot": "", "sources": ["../../src/protocol.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAA;AAC/C,OAAO,EAAE,UAAU,IAAI,oBAAoB,EAAE,MAAM,aAAa,CAAA;AAChE,OAAO,EAAE,KAAK,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAA;AAC5D,OAAO,EAAE,0BAA0B,EAAE,MAAM,aAAa,CAAA;AACxD,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AAGlC,oHAAoH;AACpH,sCAAsC;AAEtC,MAAM,CAAC,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,CAAC,CAAA;AAMzC,MAAM,OAAO,WAAW;IACf,CAAC,CAAa;IACd,CAAC,CAAO;IACE,MAAM,CAAS;IAEhC,YAAa,MAAe,EAAE,IAA4B,SAAS,EAAE,CAAC,GAAG,CAAC;QACxE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACV,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAA;IACvB,CAAC;IAEM,MAAM;QACX,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;IAEM,aAAa,CAAE,EAAc,EAAE,SAAsC;QAC1E,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,OAAO,SAAS,CAAA;QAClB,CAAC;QAED,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;QACpB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;QACvE,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAA;QAElB,OAAO,CAAC,CAAA;IACV,CAAC;IAEM,aAAa,CAAE,EAAc,EAAE,UAAuC,EAAE,GAAgB;QAC7F,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,OAAO,UAAU,CAAA;QACnB,CAAC;QAED,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;QACrF,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAA;QAElB,OAAO,SAAS,CAAA;IAClB,CAAC;CACF;AAED,MAAM,OAAO,cAAc;IAClB,EAAE,CAAa;IACf,EAAE,CAAY;IACd,CAAC,CAAY;IACH,MAAM,CAAS;IAEhC,YAAa,MAAe,EAAE,YAAoB;QAChD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QAEpB,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;QACrE,IAAI,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;QAEpD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;QAChB,IAAI,CAAC,EAAE,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAA;IACnC,CAAC;IAEM,MAAM,CAAE,GAAe;QAC5B,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;QAClD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC/C,CAAC;IAEM,OAAO,CAAE,IAAiC;QAC/C,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAEM,cAAc,CAAE,SAAsC;QAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAA;QAC3D,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QACxB,OAAO,UAAU,CAAA;IACnB,CAAC;IAEM,cAAc,CAAE,UAAuC;QAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;QAC3D,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QACxB,OAAO,SAAS,CAAA;IAClB,CAAC;IAEM,KAAK;QACV,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QAC3D,OAAO,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;IACrF,CAAC;CACF;AAgBD,MAAM,OAAgB,sBAAsB;IACnC,EAAE,CAAgB;IAClB,CAAC,CAAU;IACX,CAAC,CAAU;IACX,EAAE,CAA8B;IAChC,EAAE,CAA8B;IAChC,SAAS,CAAS;IACN,MAAM,CAAS;IAElC,YAAa,IAAwB;QACnC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAA;QACxE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,EAAE,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAClD,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACV,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACV,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;IACd,CAAC;IAES,MAAM;QACd,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;QACrD,CAAC;QACD,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAA;QACvC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QAC5B,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACV,OAAO,CAAC,CAAC,SAAS,CAAA;IACpB,CAAC;IAES,MAAM;QACd,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAC9C,CAAC;QACD,OAAO,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;IACjD,CAAC;IAES,OAAO;QACf,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACjD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;QAC3D,CAAC;QACD,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;IACjD,CAAC;IAES,OAAO;QACf,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;YACjD,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;YACxD,CAAC;YACD,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;QACjD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;YAC9C,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;YAC3D,CAAC;YACD,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAES,OAAO;QACf,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;YAC9C,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;YAC3D,CAAC;YACD,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;QACjD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;YACjD,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;YACxD,CAAC;YACD,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAES,KAAK,CAAE,OAAuB,EAAE,MAAM,GAAG,CAAC;QAClD,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;QAC/D,CAAC;QACD,IAAI,OAAO,CAAC,UAAU,GAAG,MAAM,GAAG,EAAE,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;QACD,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,CAAA;QAC9C,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IAC1B,CAAC;IAES,KAAK,CAAE,OAAuB,EAAE,MAAM,GAAG,CAAC;QAClD,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;QAC5D,CAAC;QACD,MAAM,YAAY,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACxD,IAAI,OAAO,CAAC,UAAU,GAAG,MAAM,GAAG,YAAY,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;QACD,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,YAAY,CAAC,CAAA;QAC3D,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QACtC,OAAO,YAAY,CAAA;IACrB,CAAC;IAES,MAAM;QACd,IAAI,CAAC,OAAO,EAAE,CAAA;IAChB,CAAC;IAES,MAAM;QACd,IAAI,CAAC,OAAO,EAAE,CAAA;IAChB,CAAC;IAES,MAAM;QACd,IAAI,CAAC,OAAO,EAAE,CAAA;IAChB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,sBAAsB;IAC1D,IAAI;IACJ,aAAa,CAAE,OAAoC;QACjD,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAA;IAC3E,CAAC;IAED,eAAe;IACf,aAAa,CAAE,OAAoC;QACjD,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QACvB,IAAI,CAAC,OAAO,EAAE,CAAA;QACd,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QAC1B,IAAI,CAAC,OAAO,EAAE,CAAA;QAEd,OAAO,IAAI,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAA;IACrE,CAAC;IAED,QAAQ;IACR,aAAa,CAAE,OAAoC;QACjD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QAC1B,IAAI,CAAC,OAAO,EAAE,CAAA;QAEd,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAA;IAClE,CAAC;IAED,IAAI;IACJ,YAAY,CAAE,OAAuB;QACnC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YAEnB,OAAO,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAA;QACpD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,0BAA0B,CAAC,sCAAuC,CAAW,CAAC,OAAO,EAAE,CAAC,CAAA;QACpG,CAAC;IACH,CAAC;IAED,eAAe;IACf,YAAY,CAAE,OAAuB;QACnC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YACnB,IAAI,CAAC,MAAM,EAAE,CAAA;YACb,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YACxC,IAAI,CAAC,MAAM,EAAE,CAAA;YAEb,OAAO,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAA;QAC/D,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,0BAA0B,CAAC,sCAAuC,CAAW,CAAC,OAAO,EAAE,CAAC,CAAA;QACpG,CAAC;IACH,CAAC;IAED,QAAQ;IACR,YAAY,CAAE,OAAuB;QACnC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YACpC,IAAI,CAAC,MAAM,EAAE,CAAA;YAEb,OAAO,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;QAC1D,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,0BAA0B,CAAC,sCAAuC,CAAW,CAAC,OAAO,EAAE,CAAC,CAAA;QACpG,CAAC;IACH,CAAC;CACF;AAED,SAAS,gBAAgB,CAAE,MAAe,EAAE,YAAwB;IAClE,IAAI,YAAY,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;QAC9B,MAAM,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,CAAA;QAC7B,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QACnB,OAAO,CAAC,CAAA;IACV,CAAC;SAAM,CAAC;QACN,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IAClC,CAAC;AACH,CAAC"}