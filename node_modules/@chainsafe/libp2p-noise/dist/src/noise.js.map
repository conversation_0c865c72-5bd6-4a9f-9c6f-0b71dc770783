{"version": 3, "file": "noise.js", "sourceRoot": "", "sources": ["../../src/noise.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,qBAAqB,CAAA;AAC3D,OAAO,EAAE,0BAA0B,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAA;AACnF,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAA;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAA;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAA;AAC3C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAA;AAC9B,OAAO,EAAE,KAAK,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAA;AAC5D,OAAO,EAAE,0BAA0B,EAAE,MAAM,gBAAgB,CAAA;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAA;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAA;AACxC,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,cAAc,CAAA;AAC7D,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAA;AAC9C,OAAO,EAAE,yBAAyB,EAAE,yBAAyB,EAAE,MAAM,uBAAuB,CAAA;AAC5F,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAA;AAwB7D,MAAM,OAAO,KAAK;IACT,QAAQ,GAAG,QAAQ,CAAA;IACnB,MAAM,CAAS;IAEL,QAAQ,CAAY;IACpB,SAAS,CAAS;IAClB,UAAU,CAAkB;IAC5B,OAAO,CAAkB;IACzB,UAAU,CAAiB;IAE5C,YAAa,UAA2B,EAAE,OAAkB,EAAE;QAC5D,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAA;QAClE,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAA;QAE9B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,aAAa,CAAA;QACvC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAA;QACjC,IAAI,CAAC,UAAU,GAAG;YAChB,sBAAsB,EAAE,EAAE;YAC1B,GAAG,UAAU;SACd,CAAA;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAE7D,IAAI,cAAc,EAAE,CAAC;YACnB,0CAA0C;YAC1C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,6BAA6B,CAAC,cAAc,CAAC,CAAA;QACxE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAA;QAClD,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,aAAa,IAAI,eAAe,CAAC,CAAC,CAAC,CAAA;IACrD,CAAC;IAEQ,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,yBAAyB,CAAA;IAEhD,CAAC,mBAAmB,CAAC,GAAa;QACzC,+BAA+B;QAC/B,yBAAyB;KAC1B,CAAA;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,cAAc,CAA6F,UAAkB,EAAE,OAAiC;QAC3K,MAAM,iBAAiB,GAAG,QAAQ,CAChC,UAAU,EACV;YACE,aAAa,EAAE,cAAc;YAC7B,aAAa,EAAE,cAAc;YAC7B,aAAa,EAAE,0BAA0B;SAC1C,CACF,CAAA;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACpD,iBAAiB,EACjB,IAAI,CAAC,UAAU,CAAC,UAAU,EAC1B,OAAO,EAAE,UAAU,EAAE,SAAS,EAC9B,OAAO,CACR,CAAA;QACD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAA;QAE5E,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC/B,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QAE3B,MAAM,SAAS,GAAG,qBAAqB,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QAEtE,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,gBAAgB,EAAE,SAAS,CAAC,OAAO,CAAC,UAAU;YAC9C,UAAU,EAAE,mBAAmB,CAAC,SAAS,CAAC;YAC1C,WAAW,EAAE,OAAO,EAAE,0BAA0B,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC;SACxI,CAAA;IACH,CAAC;IAEO,cAAc,CAAE,SAAoB;QAC1C,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,OAAM;QACR,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAA;QAE/D,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBAE9C,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;oBACxB,OAAO,WAAW,CAAA;gBACpB,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,MAAM,IAAI,0BAA0B,CAAC,4FAA4F,CAAC,CAAA;QACpI,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,aAAa,CAA6F,UAAkB,EAAE,OAAiC;QAC1K,MAAM,iBAAiB,GAAG,QAAQ,CAChC,UAAU,EACV;YACE,aAAa,EAAE,cAAc;YAC7B,aAAa,EAAE,cAAc;YAC7B,aAAa,EAAE,0BAA0B;SAC1C,CACF,CAAA;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACpD,iBAAiB,EACjB,IAAI,CAAC,UAAU,CAAC,UAAU,EAC1B,OAAO,EAAE,UAAU,EAAE,SAAS,EAC9B,OAAO,CACR,CAAA;QACD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAA;QAE5E,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC/B,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QAE3B,MAAM,SAAS,GAAG,qBAAqB,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QAEtE,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,gBAAgB,EAAE,SAAS,CAAC,OAAO,CAAC,UAAU;YAC9C,UAAU,EAAE,mBAAmB,CAAC,SAAS,CAAC;YAC1C,WAAW,EAAE,OAAO,EAAE,0BAA0B,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC;SACxI,CAAA;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CACrC,UAAgC;IAChC,6DAA6D;IAC7D,UAAsB,EACtB,iBAA6B,EAC7B,OAAiC;QAEjC,IAAI,MAAuB,CAAA;QAC3B,MAAM,YAAY,GAAG,OAAO,EAAE,0BAA0B,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;QAE/H,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,yBAAyB,CAAC;gBACvC,UAAU;gBACV,UAAU;gBACV,iBAAiB;gBACjB,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,0BAA0B,CAAC;gBACpE,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,CAAC,EAAE,IAAI,CAAC,SAAS;gBACjB,UAAU,EAAE;oBACV,YAAY;oBACZ,sBAAsB,EAAE,EAAE;oBAC1B,GAAG,IAAI,CAAC,UAAU;iBACnB;aACF,EAAE,OAAO,CAAC,CAAA;YACX,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC,SAAS,EAAE,CAAA;QAChD,CAAC;QAAC,OAAO,CAAU,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,SAAS,EAAE,CAAA;YAC3C,MAAM,CAAC,CAAA;QACT,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CACrC,UAAgC,EAChC,UAAsB,EACtB,iBAA6B,EAC7B,OAAiC;QAEjC,IAAI,MAAuB,CAAA;QAC3B,MAAM,YAAY,GAAG,OAAO,EAAE,0BAA0B,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;QAE/H,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,yBAAyB,CAAC;gBACvC,UAAU;gBACV,UAAU;gBACV,iBAAiB;gBACjB,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,0BAA0B,CAAC;gBACpE,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,CAAC,EAAE,IAAI,CAAC,SAAS;gBACjB,UAAU,EAAE;oBACV,YAAY;oBACZ,sBAAsB,EAAE,EAAE;oBAC1B,GAAG,IAAI,CAAC,UAAU;iBACnB;aACF,EAAE,OAAO,CAAC,CAAA;YACX,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC,SAAS,EAAE,CAAA;QAChD,CAAC;QAAC,OAAO,CAAU,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,SAAS,EAAE,CAAA;YAC3C,MAAM,CAAC,CAAA;QACT,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,UAAqF,EACrF,SAA0B;QAE1B,sCAAsC;QACtC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,UAAU,EAA+B,CAAA;QAChE,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,EAAE,CAAA;QAEnC,MAAM,IAAI,CACR,MAAM,EAAE,mBAAmB;QAC3B,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,4CAA4C;QACpF,OAAO,EAAE,0BAA0B;QACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,cAAc,EAAE,CAAC,EAAE,6BAA6B;QAC5F,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,4BAA4B;QACpE,MAAM,CAAC,sBAAsB;SAC9B,CAAA;QAED,OAAO,IAAI,CAAA;IACb,CAAC;CACF"}