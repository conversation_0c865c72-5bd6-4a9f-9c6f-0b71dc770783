{"version": 3, "file": "noise.d.ts", "sourceRoot": "", "sources": ["../../src/noise.ts"], "names": [], "mappings": "AACA,OAAO,EAA8B,mBAAmB,EAAE,MAAM,mBAAmB,CAAA;AAcnF,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAA;AACnD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AAEjD,OAAO,KAAK,EAAmB,OAAO,EAAE,gBAAgB,EAAW,MAAM,YAAY,CAAA;AACrF,OAAO,KAAK,EAAE,mBAAmB,EAAE,iBAAiB,EAA6C,uBAAuB,EAAE,MAAM,mBAAmB,CAAA;AAEnJ,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAA;AAC7C,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAA;AAEpD,MAAM,WAAW,eAAe;IAC9B,sBAAsB,EAAE,UAAU,EAAE,CAAA;CACrC;AAED,MAAM,WAAW,SAAS;IACxB;;OAEG;IACH,cAAc,CAAC,EAAE,UAAU,CAAA;IAC3B,UAAU,CAAC,EAAE,OAAO,CAAC,eAAe,CAAC,CAAA;IACrC,MAAM,CAAC,EAAE,gBAAgB,CAAA;IACzB,aAAa,CAAC,EAAE,UAAU,CAAA;CAC3B;AAED,qBAAa,KAAM,YAAW,gBAAgB;IACrC,QAAQ,SAAW;IACnB,MAAM,EAAE,OAAO,CAAA;IAEtB,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAY;IACrC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAS;IACnC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAiB;IAC7C,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAiB;IAC1C,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAiB;gBAE/B,UAAU,EAAE,eAAe,EAAE,IAAI,GAAE,SAAc;IAsB9D,QAAQ,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,6BAA4B;IAEzD,QAAQ,CAAC,CAAC,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAGvC;IAED;;;;;;;OAOG;IACU,cAAc,CAAE,MAAM,SAAS,MAAM,CAAC,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,GAAG,mBAAmB,EAAG,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,uBAAuB,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IA+BnO,OAAO,CAAC,cAAc;IAsBtB;;;;;;;OAOG;IACU,aAAa,CAAE,MAAM,SAAS,MAAM,CAAC,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,GAAG,mBAAmB,EAAG,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,uBAAuB,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IA+BlO;;OAEG;YACW,yBAAyB;IAkCvC;;OAEG;YACW,yBAAyB;YAiCzB,sBAAsB;CAmBrC"}