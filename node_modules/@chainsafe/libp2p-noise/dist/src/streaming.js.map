{"version": 3, "file": "streaming.js", "sourceRoot": "", "sources": ["../../src/streaming.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAA;AAC/C,OAAO,EAAE,0BAA0B,EAAE,sCAAsC,EAAE,MAAM,gBAAgB,CAAA;AACnG,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAA;AAK7C,MAAM,iBAAiB,GAAG,EAAE,CAAA;AAE5B,wDAAwD;AACxD,MAAM,UAAU,aAAa,CAAE,SAA0B,EAAE,OAAyB;IAClF,OAAO,KAAK,SAAU,CAAC,EAAE,MAAM;QAC7B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,sCAAsC,EAAE,CAAC;gBAC9E,IAAI,GAAG,GAAG,CAAC,GAAG,sCAAsC,CAAA;gBACpD,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;oBACvB,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;gBACpB,CAAC;gBAED,IAAI,IAAiC,CAAA;gBAErC,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;oBAChC,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;gBAClD,CAAC;qBAAM,CAAC;oBACN,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;gBACjD,CAAC;gBAED,OAAO,EAAE,gBAAgB,CAAC,SAAS,EAAE,CAAA;gBAErC,MAAM,IAAI,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,CAAA;YACjE,CAAC;QACH,CAAC;IACH,CAAC,CAAA;AACH,CAAC;AAED,uCAAuC;AACvC,MAAM,UAAU,aAAa,CAAE,SAA0B,EAAE,OAAyB;IAClF,OAAO,KAAK,SAAU,CAAC,EAAE,MAAM;QAC7B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,0BAA0B,EAAE,CAAC;gBAClE,IAAI,GAAG,GAAG,CAAC,GAAG,0BAA0B,CAAA;gBACxC,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;oBACvB,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;gBACpB,CAAC;gBAED,IAAI,GAAG,GAAG,iBAAiB,GAAG,CAAC,EAAE,CAAC;oBAChC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;gBAClC,CAAC;gBAED,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;gBACvC,mEAAmE;gBACnE,6EAA6E;gBAC7E,iFAAiF;gBACjF,6JAA6J;gBAC7J,MAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,GAAG,iBAAiB,CAAC,CAAA;gBACtD,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;oBACnD,OAAO,EAAE,gBAAgB,CAAC,SAAS,EAAE,CAAA;oBACrC,MAAM,SAAS,CAAA;gBACjB,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,OAAO,EAAE,aAAa,CAAC,SAAS,EAAE,CAAA;oBAClC,MAAM,CAAC,CAAA;gBACT,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAA;AACH,CAAC"}