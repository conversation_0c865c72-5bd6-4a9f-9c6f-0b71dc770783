{"version": 3, "file": "protocol.d.ts", "sourceRoot": "", "sources": ["../../src/protocol.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAA;AAI/C,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AAClC,OAAO,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAA;AAKlG,eAAO,MAAM,OAAO,6BAAqB,CAAA;AAEzC,UAAU,mBAAoB,SAAQ,YAAY;IAChD,CAAC,EAAE,UAAU,CAAA;CACd;AAED,qBAAa,WAAY,YAAW,YAAY;IACvC,CAAC,CAAC,EAAE,UAAU,CAAA;IACd,CAAC,EAAE,KAAK,CAAA;IACf,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAS;gBAEnB,MAAM,EAAE,OAAO,EAAE,CAAC,GAAE,UAAU,GAAG,SAAqB,EAAE,CAAC,SAAI;IAMnE,MAAM,IAAK,IAAI,IAAI,mBAAmB;IAItC,aAAa,CAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,GAAG,cAAc,GAAG,UAAU,GAAG,cAAc;IAYnG,aAAa,CAAE,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,GAAG,cAAc,EAAE,GAAG,CAAC,EAAE,UAAU,GAAG,UAAU,GAAG,cAAc;CAW9H;AAED,qBAAa,cAAe,YAAW,eAAe;IAC7C,EAAE,EAAE,WAAW,CAAA;IACf,EAAE,EAAE,UAAU,CAAA;IACd,CAAC,EAAE,UAAU,CAAA;IACpB,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAS;gBAEnB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM;IAU3C,MAAM,CAAE,GAAG,EAAE,UAAU,GAAG,IAAI;IAM9B,OAAO,CAAE,IAAI,EAAE,UAAU,GAAG,cAAc,GAAG,IAAI;IAIjD,cAAc,CAAE,SAAS,EAAE,UAAU,GAAG,cAAc,GAAG,UAAU,GAAG,cAAc;IAMpF,cAAc,CAAE,UAAU,EAAE,UAAU,GAAG,cAAc,GAAG,UAAU,GAAG,cAAc;IAMrF,KAAK,IAAK,CAAC,WAAW,EAAE,WAAW,CAAC;CAI5C;AAKD,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,OAAO,CAAA;IACf,YAAY,EAAE,MAAM,CAAA;IACpB,SAAS,EAAE,OAAO,CAAA;IAClB,QAAQ,EAAE,UAAU,CAAA;IACpB,CAAC,CAAC,EAAE,OAAO,CAAA;IACX,CAAC,CAAC,EAAE,OAAO,CAAA;IACX,EAAE,CAAC,EAAE,UAAU,GAAG,cAAc,CAAA;IAChC,EAAE,CAAC,EAAE,UAAU,GAAG,cAAc,CAAA;CACjC;AAED,8BAAsB,sBAAuB,YAAW,eAAe;IAC9D,EAAE,EAAE,cAAc,CAAA;IAClB,CAAC,CAAC,EAAE,OAAO,CAAA;IACX,CAAC,CAAC,EAAE,OAAO,CAAA;IACX,EAAE,CAAC,EAAE,UAAU,GAAG,cAAc,CAAA;IAChC,EAAE,CAAC,EAAE,UAAU,GAAG,cAAc,CAAA;IAChC,SAAS,EAAE,OAAO,CAAA;IACzB,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAA;gBAErB,IAAI,EAAE,kBAAkB;IAYrC,SAAS,CAAC,MAAM,IAAK,UAAU;IAU/B,SAAS,CAAC,MAAM,IAAK,UAAU,GAAG,cAAc;IAOhD,SAAS,CAAC,OAAO,IAAK,IAAI;IAU1B,SAAS,CAAC,OAAO,IAAK,IAAI;IAoB1B,SAAS,CAAC,OAAO,IAAK,IAAI;IAoB1B,SAAS,CAAC,KAAK,CAAE,OAAO,EAAE,cAAc,EAAE,MAAM,SAAI,GAAG,IAAI;IAW3D,SAAS,CAAC,KAAK,CAAE,OAAO,EAAE,cAAc,EAAE,MAAM,SAAI,GAAG,MAAM;IAa7D,SAAS,CAAC,MAAM,IAAK,IAAI;IAIzB,SAAS,CAAC,MAAM,IAAK,IAAI;IAIzB,SAAS,CAAC,MAAM,IAAK,IAAI;CAG1B;AAED;;GAEG;AACH,qBAAa,gBAAiB,SAAQ,sBAAsB;IAE1D,aAAa,CAAE,OAAO,EAAE,UAAU,GAAG,cAAc,GAAG,UAAU,GAAG,cAAc;IAKjF,aAAa,CAAE,OAAO,EAAE,UAAU,GAAG,cAAc,GAAG,UAAU,GAAG,cAAc;IAUjF,aAAa,CAAE,OAAO,EAAE,UAAU,GAAG,cAAc,GAAG,UAAU,GAAG,cAAc;IAQjF,YAAY,CAAE,OAAO,EAAE,cAAc,GAAG,UAAU,GAAG,cAAc;IAWnE,YAAY,CAAE,OAAO,EAAE,cAAc,GAAG,UAAU,GAAG,cAAc;IAcnE,YAAY,CAAE,OAAO,EAAE,cAAc,GAAG,UAAU,GAAG,cAAc;CAUpE"}