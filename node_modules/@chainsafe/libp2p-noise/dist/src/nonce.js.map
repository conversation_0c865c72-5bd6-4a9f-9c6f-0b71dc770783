{"version": 3, "file": "nonce.js", "sourceRoot": "", "sources": ["../../src/nonce.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAA;AAE5D,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,CAAA;AAC1B,qEAAqE;AACrE,8FAA8F;AAC9F,oDAAoD;AACpD,+FAA+F;AAC/F,0KAA0K;AAC1K,qGAAqG;AACrG,MAAM,CAAC,MAAM,SAAS,GAAG,UAAU,CAAA;AAEnC,MAAM,aAAa,GAAG,sEAAsE,CAAA;AAE5F;;;GAGG;AACH,MAAM,OAAO,KAAK;IACR,CAAC,CAAQ;IACA,KAAK,CAAY;IACjB,IAAI,CAAU;IAE/B,YAAa,CAAC,GAAG,SAAS;QACxB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACV,IAAI,CAAC,KAAK,GAAG,eAAe,CAAC,EAAE,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;QACzF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;IACjC,CAAC;IAED,SAAS;QACP,IAAI,CAAC,CAAC,EAAE,CAAA;QACR,2FAA2F;QAC3F,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;IACtC,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,CAAC,CAAA;IACf,CAAC;IAED,WAAW;QACT,IAAI,IAAI,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAA;QAChC,CAAC;IACH,CAAC;CACF"}