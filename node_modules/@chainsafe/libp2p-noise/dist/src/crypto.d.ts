import type { I<PERSON>rypt<PERSON>, KeyPair } from './types.js';
import type { Uint8ArrayList } from 'uint8arraylist';
/** Underlying crypto implementation, meant to be overridable */
export interface ICryptoInterface {
    hashSHA256(data: Uint8Array | Uint8ArrayList): Uint8Array;
    getHKDF(ck: Uint8Array, ikm: Uint8Array): [Uint8Array, Uint8Array, Uint8Array];
    generateX25519KeyPair(): KeyPair;
    generateX25519KeyPairFromSeed(seed: Uint8Array): KeyPair;
    generateX25519SharedKey(privateKey: Uint8Array | Uint8ArrayList, publicKey: Uint8Array | Uint8ArrayList): Uint8Array;
    chaCha20Poly1305Encrypt(plaintext: Uint8Array | Uint8ArrayList, nonce: Uint8Array, ad: Uint8Array, k: Uint8Array): Uint8ArrayList | Uint8Array;
    chaCha20Poly1305Decrypt(ciphertext: Uint8Array | Uint8ArrayList, nonce: Uint8Array, ad: Uint8Array, k: Uint8Array, dst?: Uint8Array): Uint8ArrayList | Uint8Array;
}
export declare function wrapCrypto(crypto: ICryptoInterface): ICrypto;
//# sourceMappingURL=crypto.d.ts.map