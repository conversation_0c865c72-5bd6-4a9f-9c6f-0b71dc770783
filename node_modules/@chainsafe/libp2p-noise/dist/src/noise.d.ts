import { serviceCapabilities } from '@libp2p/interface';
import type { ICryptoInterface } from './crypto.js';
import type { NoiseComponents } from './index.js';
import type { ICrypto, INoiseConnection } from './types.js';
import type { MultiaddrConnection, SecuredConnection, SecureConnectionOptions } from '@libp2p/interface';
import type { Duplex } from 'it-stream-types';
import type { Uint8ArrayList } from 'uint8arraylist';
export interface NoiseExtensions {
    webtransportCerthashes: Uint8Array[];
}
export interface NoiseInit {
    /**
     * x25519 private key, reuse for faster handshakes
     */
    staticNoiseKey?: Uint8Array;
    extensions?: Partial<NoiseExtensions>;
    crypto?: ICryptoInterface;
    prologueBytes?: Uint8Array;
}
export declare class Noise implements INoiseConnection {
    protocol: string;
    crypto: ICrypto;
    private readonly prologue;
    private readonly staticKey;
    private readonly extensions?;
    private readonly metrics?;
    private readonly components;
    constructor(components: NoiseComponents, init?: NoiseInit);
    readonly [Symbol.toStringTag] = "@chainsafe/libp2p-noise";
    readonly [serviceCapabilities]: string[];
    /**
     * Encrypt outgoing data to the remote party (handshake as initiator)
     *
     * @param connection - streaming iterable duplex that will be encrypted
     * @param options
     * @param options.remotePeer - PeerId of the remote peer. Used to validate the integrity of the remote peer
     * @param options.signal - Used to abort the operation
     */
    secureOutbound<Stream extends Duplex<AsyncGenerator<Uint8Array | Uint8ArrayList>> = MultiaddrConnection>(connection: Stream, options?: SecureConnectionOptions): Promise<SecuredConnection<Stream, NoiseExtensions>>;
    private getStreamMuxer;
    /**
     * Decrypt incoming data (handshake as responder).
     *
     * @param connection - streaming iterable duplex that will be encrypted
     * @param options
     * @param options.remotePeer - PeerId of the remote peer. Used to validate the integrity of the remote peer
     * @param options.signal - Used to abort the operation
     */
    secureInbound<Stream extends Duplex<AsyncGenerator<Uint8Array | Uint8ArrayList>> = MultiaddrConnection>(connection: Stream, options?: SecureConnectionOptions): Promise<SecuredConnection<Stream, NoiseExtensions>>;
    /**
     * Perform XX handshake as initiator.
     */
    private performHandshakeInitiator;
    /**
     * Perform XX handshake as responder.
     */
    private performHandshakeResponder;
    private createSecureConnection;
}
//# sourceMappingURL=noise.d.ts.map