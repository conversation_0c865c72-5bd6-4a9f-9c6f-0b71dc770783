{"version": 3, "file": "encoder.js", "sourceRoot": "", "sources": ["../../src/encoder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,IAAI,qBAAqB,EAAE,MAAM,mBAAmB,CAAA;AAIxE,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,KAAa,EAAc,EAAE;IAC1D,MAAM,MAAM,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAA;IACvC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAA;IACtB,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAA;IACjB,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AACD,cAAc,CAAC,KAAK,GAAG,CAAC,CAAA;AAExB,MAAM,CAAC,MAAM,cAAc,GAA0B,CAAC,IAAiC,EAAU,EAAE;IACjG,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAAC,MAAM,UAAU,CAAC,0BAA0B,CAAC,CAAA;IAAC,CAAC;IAErE,IAAI,IAAI,YAAY,UAAU,EAAE,CAAC;QAC/B,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;QACrB,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAA;QAChB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;AAC1B,CAAC,CAAA;AACD,cAAc,CAAC,KAAK,GAAG,CAAC,CAAA"}