(function (root, factory) {(typeof module === 'object' && module.exports) ? module.exports = factory() : root.ChainsafeLibp2PNoise = factory()}(typeof self !== 'undefined' ? self : this, function () {
"use strict";var ChainsafeLibp2PNoise=(()=>{var _n=Object.defineProperty;var Ja=Object.getOwnPropertyDescriptor;var Qa=Object.getOwnPropertyNames;var tc=Object.prototype.hasOwnProperty;var xt=(e,t)=>{for(var r in t)_n(e,r,{get:t[r],enumerable:!0})},ec=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Qa(t))!tc.call(e,o)&&o!==r&&_n(e,o,{get:()=>t[o],enumerable:!(n=Ja(t,o))||n.enumerable});return e};var rc=e=>ec(_n({},"__esModule",{value:!0}),e);var dh={};xt(dh,{noise:()=>lh,pureJsCrypto:()=>Sn});var In=Symbol.for("@libp2p/peer-id");var Br=class extends Error{static name="UnexpectedPeerError";constructor(t="Unexpected Peer"){super(t),this.name="UnexpectedPeerError"}},_r=class extends Error{static name="InvalidCryptoExchangeError";constructor(t="Invalid crypto exchange"){super(t),this.name="InvalidCryptoExchangeError"}},Q=class extends Error{static name="InvalidParametersError";constructor(t="Invalid parameters"){super(t),this.name="InvalidParametersError"}},be=class extends Error{static name="InvalidPublicKeyError";constructor(t="Invalid public key"){super(t),this.name="InvalidPublicKeyError"}};var we=class extends Error{static name="UnsupportedKeyTypeError";constructor(t="Unsupported key type"){super(t),this.name="UnsupportedKeyTypeError"}};var Es=Symbol.for("@libp2p/service-capabilities"),xh=Symbol.for("@libp2p/service-dependencies");var Un={};xt(Un,{base58btc:()=>J,base58flickr:()=>fc});var Hh=new Uint8Array(0);function Ss(e,t){if(e===t)return!0;if(e.byteLength!==t.byteLength)return!1;for(let r=0;r<e.byteLength;r++)if(e[r]!==t[r])return!1;return!0}function kt(e){if(e instanceof Uint8Array&&e.constructor.name==="Uint8Array")return e;if(e instanceof ArrayBuffer)return new Uint8Array(e);if(ArrayBuffer.isView(e))return new Uint8Array(e.buffer,e.byteOffset,e.byteLength);throw new Error("Unknown type, must be binary type")}function As(e){return new TextEncoder().encode(e)}function vs(e){return new TextDecoder().decode(e)}function nc(e,t){if(e.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var o=0;o<e.length;o++){var s=e.charAt(o),i=s.charCodeAt(0);if(r[i]!==255)throw new TypeError(s+" is ambiguous");r[i]=o}var a=e.length,c=e.charAt(0),h=Math.log(a)/Math.log(256),f=Math.log(256)/Math.log(a);function u(w){if(w instanceof Uint8Array||(ArrayBuffer.isView(w)?w=new Uint8Array(w.buffer,w.byteOffset,w.byteLength):Array.isArray(w)&&(w=Uint8Array.from(w))),!(w instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(w.length===0)return"";for(var x=0,l=0,E=0,I=w.length;E!==I&&w[E]===0;)E++,x++;for(var _=(I-E)*f+1>>>0,P=new Uint8Array(_);E!==I;){for(var S=w[E],D=0,K=_-1;(S!==0||D<l)&&K!==-1;K--,D++)S+=256*P[K]>>>0,P[K]=S%a>>>0,S=S/a>>>0;if(S!==0)throw new Error("Non-zero carry");l=D,E++}for(var T=_-l;T!==_&&P[T]===0;)T++;for(var d=c.repeat(x);T<_;++T)d+=e.charAt(P[T]);return d}function m(w){if(typeof w!="string")throw new TypeError("Expected String");if(w.length===0)return new Uint8Array;var x=0;if(w[x]!==" "){for(var l=0,E=0;w[x]===c;)l++,x++;for(var I=(w.length-x)*h+1>>>0,_=new Uint8Array(I);w[x];){var P=r[w.charCodeAt(x)];if(P===255)return;for(var S=0,D=I-1;(P!==0||S<E)&&D!==-1;D--,S++)P+=a*_[D]>>>0,_[D]=P%256>>>0,P=P/256>>>0;if(P!==0)throw new Error("Non-zero carry");E=S,x++}if(w[x]!==" "){for(var K=I-E;K!==I&&_[K]===0;)K++;for(var T=new Uint8Array(l+(I-K)),d=l;K!==I;)T[d++]=_[K++];return T}}}function y(w){var x=m(w);if(x)return x;throw new Error(`Non-${t} character`)}return{encode:u,decodeUnsafe:m,decode:y}}var oc=nc,sc=oc,_s=sc;var Ln=class{name;prefix;baseEncode;constructor(t,r,n){this.name=t,this.prefix=r,this.baseEncode=n}encode(t){if(t instanceof Uint8Array)return`${this.prefix}${this.baseEncode(t)}`;throw Error("Unknown type, must be binary type")}},Tn=class{name;prefix;baseDecode;prefixCodePoint;constructor(t,r,n){this.name=t,this.prefix=r;let o=r.codePointAt(0);if(o===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=o,this.baseDecode=n}decode(t){if(typeof t=="string"){if(t.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(t)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(t.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(t){return Is(this,t)}},Kn=class{decoders;constructor(t){this.decoders=t}or(t){return Is(this,t)}decode(t){let r=t[0],n=this.decoders[r];if(n!=null)return n.decode(t);throw RangeError(`Unable to decode multibase string ${JSON.stringify(t)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}};function Is(e,t){return new Kn({...e.decoders??{[e.prefix]:e},...t.decoders??{[t.prefix]:t}})}var Cn=class{name;prefix;baseEncode;baseDecode;encoder;decoder;constructor(t,r,n,o){this.name=t,this.prefix=r,this.baseEncode=n,this.baseDecode=o,this.encoder=new Ln(t,r,n),this.decoder=new Tn(t,r,o)}encode(t){return this.encoder.encode(t)}decode(t){return this.decoder.decode(t)}};function Ee({name:e,prefix:t,encode:r,decode:n}){return new Cn(e,t,r,n)}function jt({name:e,prefix:t,alphabet:r}){let{encode:n,decode:o}=_s(r,e);return Ee({prefix:t,name:e,encode:n,decode:s=>kt(o(s))})}function ic(e,t,r,n){let o=e.length;for(;e[o-1]==="=";)--o;let s=new Uint8Array(o*r/8|0),i=0,a=0,c=0;for(let h=0;h<o;++h){let f=t[e[h]];if(f===void 0)throw new SyntaxError(`Non-${n} character`);a=a<<r|f,i+=r,i>=8&&(i-=8,s[c++]=255&a>>i)}if(i>=r||(255&a<<8-i)!==0)throw new SyntaxError("Unexpected end of data");return s}function ac(e,t,r){let n=t[t.length-1]==="=",o=(1<<r)-1,s="",i=0,a=0;for(let c=0;c<e.length;++c)for(a=a<<8|e[c],i+=8;i>r;)i-=r,s+=t[o&a>>i];if(i!==0&&(s+=t[o&a<<r-i]),n)for(;(s.length*r&7)!==0;)s+="=";return s}function cc(e){let t={};for(let r=0;r<e.length;++r)t[e[r]]=r;return t}function $({name:e,prefix:t,bitsPerChar:r,alphabet:n}){let o=cc(n);return Ee({prefix:t,name:e,encode(s){return ac(s,n,r)},decode(s){return ic(s,o,r,e)}})}var J=jt({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),fc=jt({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var Dn={};xt(Dn,{base32:()=>Se,base32hex:()=>dc,base32hexpad:()=>yc,base32hexpadupper:()=>mc,base32hexupper:()=>pc,base32pad:()=>hc,base32padupper:()=>lc,base32upper:()=>uc,base32z:()=>xc});var Se=$({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),uc=$({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),hc=$({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),lc=$({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),dc=$({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),pc=$({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),yc=$({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),mc=$({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),xc=$({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var Rn={};xt(Rn,{base36:()=>Ze,base36upper:()=>gc});var Ze=jt({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),gc=jt({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var bc=Ks,Ls=128,wc=127,Ec=~wc,Sc=Math.pow(2,31);function Ks(e,t,r){t=t||[],r=r||0;for(var n=r;e>=Sc;)t[r++]=e&255|Ls,e/=128;for(;e&Ec;)t[r++]=e&255|Ls,e>>>=7;return t[r]=e|0,Ks.bytes=r-n+1,t}var Ac=Pn,vc=128,Ts=127;function Pn(e,n){var r=0,n=n||0,o=0,s=n,i,a=e.length;do{if(s>=a)throw Pn.bytes=0,new RangeError("Could not decode varint");i=e[s++],r+=o<28?(i&Ts)<<o:(i&Ts)*Math.pow(2,o),o+=7}while(i>=vc);return Pn.bytes=s-n,r}var Bc=Math.pow(2,7),_c=Math.pow(2,14),Ic=Math.pow(2,21),Lc=Math.pow(2,28),Tc=Math.pow(2,35),Kc=Math.pow(2,42),Cc=Math.pow(2,49),Uc=Math.pow(2,56),Dc=Math.pow(2,63),Rc=function(e){return e<Bc?1:e<_c?2:e<Ic?3:e<Lc?4:e<Tc?5:e<Kc?6:e<Cc?7:e<Uc?8:e<Dc?9:10},Pc={encode:bc,decode:Ac,encodingLength:Rc},Nc=Pc,Ye=Nc;function Xe(e,t=0){return[Ye.decode(e,t),Ye.decode.bytes]}function Ae(e,t,r=0){return Ye.encode(e,t,r),t}function ve(e){return Ye.encodingLength(e)}function At(e,t){let r=t.byteLength,n=ve(e),o=n+ve(r),s=new Uint8Array(o+r);return Ae(e,s,0),Ae(r,s,n),s.set(t,o),new Be(e,r,t,s)}function Nn(e){let t=kt(e),[r,n]=Xe(t),[o,s]=Xe(t.subarray(n)),i=t.subarray(n+s);if(i.byteLength!==o)throw new Error("Incorrect length");return new Be(r,o,i,t)}function Cs(e,t){if(e===t)return!0;{let r=t;return e.code===r.code&&e.size===r.size&&r.bytes instanceof Uint8Array&&Ss(e.bytes,r.bytes)}}var Be=class{code;size;digest;bytes;constructor(t,r,n,o){this.code=t,this.size=r,this.digest=n,this.bytes=o}};function Us(e,t){let{bytes:r,version:n}=e;switch(n){case 0:return Mc(r,kn(e),t??J.encoder);default:return Hc(r,kn(e),t??Se.encoder)}}var Ds=new WeakMap;function kn(e){let t=Ds.get(e);if(t==null){let r=new Map;return Ds.set(e,r),r}return t}var ct=class e{code;version;multihash;bytes;"/";constructor(t,r,n,o){this.code=r,this.version=t,this.multihash=n,this.bytes=o,this["/"]=o}get asCID(){return this}get byteOffset(){return this.bytes.byteOffset}get byteLength(){return this.bytes.byteLength}toV0(){switch(this.version){case 0:return this;case 1:{let{code:t,multihash:r}=this;if(t!==We)throw new Error("Cannot convert a non dag-pb CID to CIDv0");if(r.code!==Oc)throw new Error("Cannot convert non sha2-256 multihash CID to CIDv0");return e.createV0(r)}default:throw Error(`Can not convert CID version ${this.version} to version 0. This is a bug please report`)}}toV1(){switch(this.version){case 0:{let{code:t,digest:r}=this.multihash,n=At(t,r);return e.createV1(this.code,n)}case 1:return this;default:throw Error(`Can not convert CID version ${this.version} to version 1. This is a bug please report`)}}equals(t){return e.equals(this,t)}static equals(t,r){let n=r;return n!=null&&t.code===n.code&&t.version===n.version&&Cs(t.multihash,n.multihash)}toString(t){return Us(this,t)}toJSON(){return{"/":Us(this)}}link(){return this}[Symbol.toStringTag]="CID";[Symbol.for("nodejs.util.inspect.custom")](){return`CID(${this.toString()})`}static asCID(t){if(t==null)return null;let r=t;if(r instanceof e)return r;if(r["/"]!=null&&r["/"]===r.bytes||r.asCID===r){let{version:n,code:o,multihash:s,bytes:i}=r;return new e(n,o,s,i??Rs(n,o,s.bytes))}else if(r[qc]===!0){let{version:n,multihash:o,code:s}=r,i=Nn(o);return e.create(n,s,i)}else return null}static create(t,r,n){if(typeof r!="number")throw new Error("String codecs are no longer supported");if(!(n.bytes instanceof Uint8Array))throw new Error("Invalid digest");switch(t){case 0:{if(r!==We)throw new Error(`Version 0 CID must use dag-pb (code: ${We}) block encoding`);return new e(t,r,n,n.bytes)}case 1:{let o=Rs(t,r,n.bytes);return new e(t,r,n,o)}default:throw new Error("Invalid version")}}static createV0(t){return e.create(0,We,t)}static createV1(t,r){return e.create(1,t,r)}static decode(t){let[r,n]=e.decodeFirst(t);if(n.length!==0)throw new Error("Incorrect length");return r}static decodeFirst(t){let r=e.inspectBytes(t),n=r.size-r.multihashSize,o=kt(t.subarray(n,n+r.multihashSize));if(o.byteLength!==r.multihashSize)throw new Error("Incorrect length");let s=o.subarray(r.multihashSize-r.digestSize),i=new Be(r.multihashCode,r.digestSize,s,o);return[r.version===0?e.createV0(i):e.createV1(r.codec,i),t.subarray(r.size)]}static inspectBytes(t){let r=0,n=()=>{let[u,m]=Xe(t.subarray(r));return r+=m,u},o=n(),s=We;if(o===18?(o=0,r=0):s=n(),o!==0&&o!==1)throw new RangeError(`Invalid CID version ${o}`);let i=r,a=n(),c=n(),h=r+c,f=h-i;return{version:o,codec:s,multihashCode:a,digestSize:c,multihashSize:f,size:h}}static parse(t,r){let[n,o]=kc(t,r),s=e.decode(o);if(s.version===0&&t[0]!=="Q")throw Error("Version 0 CID string must not include multibase prefix");return kn(s).set(n,t),s}};function kc(e,t){switch(e[0]){case"Q":{let r=t??J;return[J.prefix,r.decode(`${J.prefix}${e}`)]}case J.prefix:{let r=t??J;return[J.prefix,r.decode(e)]}case Se.prefix:{let r=t??Se;return[Se.prefix,r.decode(e)]}case Ze.prefix:{let r=t??Ze;return[Ze.prefix,r.decode(e)]}default:{if(t==null)throw Error("To parse non base32, base36 or base58btc encoded CID multibase decoder must be provided");return[e[0],t.decode(e)]}}}function Mc(e,t,r){let{prefix:n}=r;if(n!==J.prefix)throw Error(`Cannot string encode V0 in ${r.name} encoding`);let o=t.get(n);if(o==null){let s=r.encode(e).slice(1);return t.set(n,s),s}else return o}function Hc(e,t,r){let{prefix:n}=r,o=t.get(n);if(o==null){let s=r.encode(e);return t.set(n,s),s}else return o}var We=112,Oc=18;function Rs(e,t,r){let n=ve(e),o=n+ve(t),s=new Uint8Array(o+r.byteLength);return Ae(e,s,0),Ae(t,s,n),s.set(r,o),s}var qc=Symbol.for("@ipld/js-cid/CID");var Mn={};xt(Mn,{identity:()=>Kt});var Ps=0,Vc="identity",Ns=kt;function zc(e){return At(Ps,Ns(e))}var Kt={code:Ps,name:Vc,encode:Ns,digest:zc};function dt(e,t){if(e===t)return!0;if(e.byteLength!==t.byteLength)return!1;for(let r=0;r<e.byteLength;r++)if(e[r]!==t[r])return!1;return!0}function tt(e=0){return new Uint8Array(e)}function et(e=0){return new Uint8Array(e)}function re(e,t){t==null&&(t=e.reduce((o,s)=>o+s.length,0));let r=et(t),n=0;for(let o of e)r.set(o,n),n+=o.length;return r}var Ms=Symbol.for("@achingbrain/uint8arraylist");function ks(e,t){if(t==null||t<0)throw new RangeError("index is out of bounds");let r=0;for(let n of e){let o=r+n.byteLength;if(t<o)return{buf:n,index:t-r};r=o}throw new RangeError("index is out of bounds")}function Lr(e){return!!e?.[Ms]}var z=class e{bufs;length;[Ms]=!0;constructor(...t){this.bufs=[],this.length=0,t.length>0&&this.appendAll(t)}*[Symbol.iterator](){yield*this.bufs}get byteLength(){return this.length}append(...t){this.appendAll(t)}appendAll(t){let r=0;for(let n of t)if(n instanceof Uint8Array)r+=n.byteLength,this.bufs.push(n);else if(Lr(n))r+=n.byteLength,this.bufs.push(...n.bufs);else throw new Error("Could not append value, must be an Uint8Array or a Uint8ArrayList");this.length+=r}prepend(...t){this.prependAll(t)}prependAll(t){let r=0;for(let n of t.reverse())if(n instanceof Uint8Array)r+=n.byteLength,this.bufs.unshift(n);else if(Lr(n))r+=n.byteLength,this.bufs.unshift(...n.bufs);else throw new Error("Could not prepend value, must be an Uint8Array or a Uint8ArrayList");this.length+=r}get(t){let r=ks(this.bufs,t);return r.buf[r.index]}set(t,r){let n=ks(this.bufs,t);n.buf[n.index]=r}write(t,r=0){if(t instanceof Uint8Array)for(let n=0;n<t.length;n++)this.set(r+n,t[n]);else if(Lr(t))for(let n=0;n<t.length;n++)this.set(r+n,t.get(n));else throw new Error("Could not write value, must be an Uint8Array or a Uint8ArrayList")}consume(t){if(t=Math.trunc(t),!(Number.isNaN(t)||t<=0)){if(t===this.byteLength){this.bufs=[],this.length=0;return}for(;this.bufs.length>0;)if(t>=this.bufs[0].byteLength)t-=this.bufs[0].byteLength,this.length-=this.bufs[0].byteLength,this.bufs.shift();else{this.bufs[0]=this.bufs[0].subarray(t),this.length-=t;break}}}slice(t,r){let{bufs:n,length:o}=this._subList(t,r);return re(n,o)}subarray(t,r){let{bufs:n,length:o}=this._subList(t,r);return n.length===1?n[0]:re(n,o)}sublist(t,r){let{bufs:n,length:o}=this._subList(t,r),s=new e;return s.length=o,s.bufs=[...n],s}_subList(t,r){if(t=t??0,r=r??this.length,t<0&&(t=this.length+t),r<0&&(r=this.length+r),t<0||r>this.length)throw new RangeError("index is out of bounds");if(t===r)return{bufs:[],length:0};if(t===0&&r===this.length)return{bufs:this.bufs,length:this.length};let n=[],o=0;for(let s=0;s<this.bufs.length;s++){let i=this.bufs[s],a=o,c=a+i.byteLength;if(o=c,t>=c)continue;let h=t>=a&&t<c,f=r>a&&r<=c;if(h&&f){if(t===a&&r===c){n.push(i);break}let u=t-a;n.push(i.subarray(u,u+(r-t)));break}if(h){if(t===0){n.push(i);continue}n.push(i.subarray(t-a));continue}if(f){if(r===c){n.push(i);break}n.push(i.subarray(0,r-a));break}n.push(i)}return{bufs:n,length:r-t}}indexOf(t,r=0){if(!Lr(t)&&!(t instanceof Uint8Array))throw new TypeError('The "value" argument must be a Uint8ArrayList or Uint8Array');let n=t instanceof Uint8Array?t:t.subarray();if(r=Number(r??0),isNaN(r)&&(r=0),r<0&&(r=this.length+r),r<0&&(r=0),t.length===0)return r>this.length?this.length:r;let o=n.byteLength;if(o===0)throw new TypeError("search must be at least 1 byte long");let s=256,i=new Int32Array(s);for(let u=0;u<s;u++)i[u]=-1;for(let u=0;u<o;u++)i[n[u]]=u;let a=i,c=this.byteLength-n.byteLength,h=n.byteLength-1,f;for(let u=r;u<=c;u+=f){f=0;for(let m=h;m>=0;m--){let y=this.get(u+m);if(n[m]!==y){f=Math.max(1,m-a[y]);break}}if(f===0)return u}return-1}getInt8(t){let r=this.subarray(t,t+1);return new DataView(r.buffer,r.byteOffset,r.byteLength).getInt8(0)}setInt8(t,r){let n=et(1);new DataView(n.buffer,n.byteOffset,n.byteLength).setInt8(0,r),this.write(n,t)}getInt16(t,r){let n=this.subarray(t,t+2);return new DataView(n.buffer,n.byteOffset,n.byteLength).getInt16(0,r)}setInt16(t,r,n){let o=tt(2);new DataView(o.buffer,o.byteOffset,o.byteLength).setInt16(0,r,n),this.write(o,t)}getInt32(t,r){let n=this.subarray(t,t+4);return new DataView(n.buffer,n.byteOffset,n.byteLength).getInt32(0,r)}setInt32(t,r,n){let o=tt(4);new DataView(o.buffer,o.byteOffset,o.byteLength).setInt32(0,r,n),this.write(o,t)}getBigInt64(t,r){let n=this.subarray(t,t+8);return new DataView(n.buffer,n.byteOffset,n.byteLength).getBigInt64(0,r)}setBigInt64(t,r,n){let o=tt(8);new DataView(o.buffer,o.byteOffset,o.byteLength).setBigInt64(0,r,n),this.write(o,t)}getUint8(t){let r=this.subarray(t,t+1);return new DataView(r.buffer,r.byteOffset,r.byteLength).getUint8(0)}setUint8(t,r){let n=et(1);new DataView(n.buffer,n.byteOffset,n.byteLength).setUint8(0,r),this.write(n,t)}getUint16(t,r){let n=this.subarray(t,t+2);return new DataView(n.buffer,n.byteOffset,n.byteLength).getUint16(0,r)}setUint16(t,r,n){let o=tt(2);new DataView(o.buffer,o.byteOffset,o.byteLength).setUint16(0,r,n),this.write(o,t)}getUint32(t,r){let n=this.subarray(t,t+4);return new DataView(n.buffer,n.byteOffset,n.byteLength).getUint32(0,r)}setUint32(t,r,n){let o=tt(4);new DataView(o.buffer,o.byteOffset,o.byteLength).setUint32(0,r,n),this.write(o,t)}getBigUint64(t,r){let n=this.subarray(t,t+8);return new DataView(n.buffer,n.byteOffset,n.byteLength).getBigUint64(0,r)}setBigUint64(t,r,n){let o=tt(8);new DataView(o.buffer,o.byteOffset,o.byteLength).setBigUint64(0,r,n),this.write(o,t)}getFloat32(t,r){let n=this.subarray(t,t+4);return new DataView(n.buffer,n.byteOffset,n.byteLength).getFloat32(0,r)}setFloat32(t,r,n){let o=tt(4);new DataView(o.buffer,o.byteOffset,o.byteLength).setFloat32(0,r,n),this.write(o,t)}getFloat64(t,r){let n=this.subarray(t,t+8);return new DataView(n.buffer,n.byteOffset,n.byteLength).getFloat64(0,r)}setFloat64(t,r,n){let o=tt(8);new DataView(o.buffer,o.byteOffset,o.byteLength).setFloat64(0,r,n),this.write(o,t)}equals(t){if(t==null||!(t instanceof e)||t.bufs.length!==this.bufs.length)return!1;for(let r=0;r<this.bufs.length;r++)if(!dt(this.bufs[r],t.bufs[r]))return!1;return!0}static fromUint8Arrays(t,r){let n=new e;return n.bufs=t,r==null&&(r=t.reduce((o,s)=>o+s.byteLength,0)),n.length=r,n}};var Hn={};xt(Hn,{base10:()=>Fc});var Fc=jt({prefix:"9",name:"base10",alphabet:"0123456789"});var On={};xt(On,{base16:()=>jc,base16upper:()=>Zc});var jc=$({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Zc=$({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var qn={};xt(qn,{base2:()=>Yc});var Yc=$({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var Vn={};xt(Vn,{base256emoji:()=>Qc});var Hs=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),Xc=Hs.reduce((e,t,r)=>(e[r]=t,e),[]),Wc=Hs.reduce((e,t,r)=>{let n=t.codePointAt(0);if(n==null)throw new Error(`Invalid character: ${t}`);return e[n]=r,e},[]);function $c(e){return e.reduce((t,r)=>(t+=Xc[r],t),"")}function Jc(e){let t=[];for(let r of e){let n=r.codePointAt(0);if(n==null)throw new Error(`Invalid character: ${r}`);let o=Wc[n];if(o==null)throw new Error(`Non-base256emoji character: ${r}`);t.push(o)}return new Uint8Array(t)}var Qc=Ee({prefix:"\u{1F680}",name:"base256emoji",encode:$c,decode:Jc});var zn={};xt(zn,{base64:()=>tf,base64pad:()=>ef,base64url:()=>rf,base64urlpad:()=>nf});var tf=$({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),ef=$({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),rf=$({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),nf=$({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var Gn={};xt(Gn,{base8:()=>of});var of=$({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var Fn={};xt(Fn,{identity:()=>sf});var sf=Ee({prefix:"\0",name:"identity",encode:e=>vs(e),decode:e=>As(e)});var El=new TextEncoder,Sl=new TextDecoder;var Yn={};xt(Yn,{sha256:()=>Tr,sha512:()=>ff});function Zn({name:e,code:t,encode:r}){return new jn(e,t,r)}var jn=class{name;code;encode;constructor(t,r,n){this.name=t,this.code=r,this.encode=n}digest(t){if(t instanceof Uint8Array){let r=this.encode(t);return r instanceof Uint8Array?At(this.code,r):r.then(n=>At(this.code,n))}else throw Error("Unknown type, must be binary type")}};function qs(e){return async t=>new Uint8Array(await crypto.subtle.digest(e,t))}var Tr=Zn({name:"sha2-256",code:18,encode:qs("SHA-256")}),ff=Zn({name:"sha2-512",code:19,encode:qs("SHA-512")});var Xn={...Fn,...qn,...Gn,...Hn,...On,...Dn,...Rn,...Un,...zn,...Vn},Rl={...Yn,...Mn};function zs(e,t,r,n){return{name:e,prefix:t,encoder:{name:e,prefix:t,encode:r},decoder:{decode:n}}}var Vs=zs("utf8","u",e=>"u"+new TextDecoder("utf8").decode(e),e=>new TextEncoder().encode(e.substring(1))),Wn=zs("ascii","a",e=>{let t="a";for(let r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},e=>{e=e.substring(1);let t=et(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}),uf={utf8:Vs,"utf-8":Vs,hex:Xn.base16,latin1:Wn,ascii:Wn,binary:Wn,...Xn},Kr=uf;function Z(e,t="utf8"){let r=Kr[t];if(r==null)throw new Error(`Unsupported encoding "${t}"`);return r.decoder.decode(`${r.prefix}${e}`)}function V(e,t="utf8"){let r=Kr[t];if(r==null)throw new Error(`Unsupported encoding "${t}"`);return r.encoder.encode(e).substring(1)}var hf=parseInt("11111",2),$n=parseInt("10000000",2),lf=parseInt("01111111",2),Gs={0:$e,1:$e,2:df,3:mf,4:xf,5:yf,6:pf,16:$e,22:$e,48:$e};function Mt(e,t={offset:0}){let r=e[t.offset]&hf;if(t.offset++,Gs[r]!=null)return Gs[r](e,t);throw new Error("No decoder for tag "+r)}function Je(e,t){let r=0;if((e[t.offset]&$n)===$n){let n=e[t.offset]&lf,o="0x";t.offset++;for(let s=0;s<n;s++,t.offset++)o+=e[t.offset].toString(16).padStart(2,"0");r=parseInt(o,16)}else r=e[t.offset],t.offset++;return r}function $e(e,t){Je(e,t);let r=[];for(;!(t.offset>=e.byteLength);){let n=Mt(e,t);if(n===null)break;r.push(n)}return r}function df(e,t){let r=Je(e,t),n=t.offset,o=t.offset+r,s=[];for(let i=n;i<o;i++)i===n&&e[i]===0||s.push(e[i]);return t.offset+=r,Uint8Array.from(s)}function pf(e,t){let r=Je(e,t),n=t.offset+r,o=e[t.offset];t.offset++;let s=0,i=0;o<40?(s=0,i=o):o<80?(s=1,i=o-40):(s=2,i=o-80);let a=`${s}.${i}`,c=[];for(;t.offset<n;){let h=e[t.offset];if(t.offset++,c.push(h&127),h<128){c.reverse();let f=0;for(let u=0;u<c.length;u++)f+=c[u]<<u*7;a+=`.${f}`,c=[]}}return a}function yf(e,t){return t.offset++,null}function mf(e,t){let r=Je(e,t),n=e[t.offset];t.offset++;let o=e.subarray(t.offset,t.offset+r-1);if(t.offset+=r,n!==0)throw new Error("Unused bits in bit string is unimplemented");return o}function xf(e,t){let r=Je(e,t),n=e.subarray(t.offset,t.offset+r);return t.offset+=r,n}function gf(e){let t=e.toString(16);t.length%2===1&&(t="0"+t);let r=new z;for(let n=0;n<t.length;n+=2)r.append(Uint8Array.from([parseInt(`${t[n]}${t[n+1]}`,16)]));return r}function Jn(e){if(e.byteLength<128)return Uint8Array.from([e.byteLength]);let t=gf(e.byteLength);return new z(Uint8Array.from([t.byteLength|$n]),t)}function gt(e){let t=new z,r=128;return(e.subarray()[0]&r)===r&&t.append(Uint8Array.from([0])),t.append(e),new z(Uint8Array.from([2]),Jn(t),t)}function Cr(e){let t=Uint8Array.from([0]),r=new z(t,e);return new z(Uint8Array.from([3]),Jn(r),r)}function Zt(e,t=48){let r=new z;for(let n of e)r.append(n);return new z(Uint8Array.from([t]),Jn(r),r)}async function Fs(e,t,r,n){let o=await crypto.subtle.importKey("jwk",e,{name:"ECDSA",namedCurve:e.crv??"P-256"},!1,["verify"]);n?.signal?.throwIfAborted();let s=await crypto.subtle.verify({name:"ECDSA",hash:{name:"SHA-256"}},o,t,r.subarray());return n?.signal?.throwIfAborted(),s}var bf=Uint8Array.from([6,8,42,134,72,206,61,3,1,7]),wf=Uint8Array.from([6,5,43,129,4,0,34]),Ef=Uint8Array.from([6,5,43,129,4,0,35]),Sf={ext:!0,kty:"EC",crv:"P-256"},Af={ext:!0,kty:"EC",crv:"P-384"},vf={ext:!0,kty:"EC",crv:"P-521"},Qn=32,to=48,eo=66;function js(e){let t=Mt(e);return Zs(t)}function Zs(e){let t=e[1][1][0],r=1,n,o;if(t.byteLength===Qn*2+1)return n=V(t.subarray(r,r+Qn),"base64url"),o=V(t.subarray(r+Qn),"base64url"),new Ie({...Sf,key_ops:["verify"],x:n,y:o});if(t.byteLength===to*2+1)return n=V(t.subarray(r,r+to),"base64url"),o=V(t.subarray(r+to),"base64url"),new Ie({...Af,key_ops:["verify"],x:n,y:o});if(t.byteLength===eo*2+1)return n=V(t.subarray(r,r+eo),"base64url"),o=V(t.subarray(r+eo),"base64url"),new Ie({...vf,key_ops:["verify"],x:n,y:o});throw new Q(`coordinates were wrong length, got ${t.byteLength}, expected 65, 97 or 133`)}function Ys(e){return Zt([gt(Uint8Array.from([1])),Zt([Bf(e.crv)],160),Zt([Cr(new z(Uint8Array.from([4]),Z(e.x??"","base64url"),Z(e.y??"","base64url")))],161)]).subarray()}function Bf(e){if(e==="P-256")return bf;if(e==="P-384")return wf;if(e==="P-521")return Ef;throw new Q(`Invalid curve ${e}`)}var Ie=class{type="ECDSA";jwk;_raw;constructor(t){this.jwk=t}get raw(){return this._raw==null&&(this._raw=Ys(this.jwk)),this._raw}toMultihash(){return Kt.digest(Yt(this))}toCID(){return ct.createV1(114,this.toMultihash())}toString(){return J.encode(this.toMultihash().bytes).substring(1)}equals(t){return t==null||!(t.raw instanceof Uint8Array)?!1:dt(this.raw,t.raw)}async verify(t,r,n){return Fs(this.jwk,r,t,n)}};var ne=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;function Te(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function oe(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function ft(e,...t){if(!Te(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function Qe(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.createHasher");oe(e.outputLen),oe(e.blockLen)}function Ke(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function Ws(e,t){ft(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}function vt(...e){for(let t=0;t<e.length;t++)e[t].fill(0)}function Ur(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Bt(e,t){return e<<32-t|e>>>t}var $s=typeof Uint8Array.from([]).toHex=="function"&&typeof Uint8Array.fromHex=="function",_f=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function Ct(e){if(ft(e),$s)return e.toHex();let t="";for(let r=0;r<e.length;r++)t+=_f[e[r]];return t}var Ht={_0:48,_9:57,A:65,F:70,a:97,f:102};function Xs(e){if(e>=Ht._0&&e<=Ht._9)return e-Ht._0;if(e>=Ht.A&&e<=Ht.F)return e-(Ht.A-10);if(e>=Ht.a&&e<=Ht.f)return e-(Ht.a-10)}function Ce(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);if($s)return Uint8Array.fromHex(e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let o=0,s=0;o<r;o++,s+=2){let i=Xs(e.charCodeAt(s)),a=Xs(e.charCodeAt(s+1));if(i===void 0||a===void 0){let c=e[s]+e[s+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+s)}n[o]=i*16+a}return n}function Js(e){if(typeof e!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(e))}function Xt(e){return typeof e=="string"&&(e=Js(e)),ft(e),e}function bt(...e){let t=0;for(let n=0;n<e.length;n++){let o=e[n];ft(o),t+=o.length}let r=new Uint8Array(t);for(let n=0,o=0;n<e.length;n++){let s=e[n];r.set(s,o),o+=s.length}return r}var Le=class{};function ro(e){let t=n=>e().update(Xt(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function se(e=32){if(ne&&typeof ne.getRandomValues=="function")return ne.getRandomValues(new Uint8Array(e));if(ne&&typeof ne.randomBytes=="function")return Uint8Array.from(ne.randomBytes(e));throw new Error("crypto.getRandomValues must be defined")}function If(e,t,r,n){if(typeof e.setBigUint64=="function")return e.setBigUint64(t,r,n);let o=BigInt(32),s=BigInt(4294967295),i=Number(r>>o&s),a=Number(r&s),c=n?4:0,h=n?0:4;e.setUint32(t+c,i,n),e.setUint32(t+h,a,n)}function Qs(e,t,r){return e&t^~e&r}function ti(e,t,r){return e&t^e&r^t&r}var tr=class extends Le{constructor(t,r,n,o){super(),this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.blockLen=t,this.outputLen=r,this.padOffset=n,this.isLE=o,this.buffer=new Uint8Array(t),this.view=Ur(this.buffer)}update(t){Ke(this),t=Xt(t),ft(t);let{view:r,buffer:n,blockLen:o}=this,s=t.length;for(let i=0;i<s;){let a=Math.min(o-this.pos,s-i);if(a===o){let c=Ur(t);for(;o<=s-i;i+=o)this.process(c,i);continue}n.set(t.subarray(i,i+a),this.pos),this.pos+=a,i+=a,this.pos===o&&(this.process(r,0),this.pos=0)}return this.length+=t.length,this.roundClean(),this}digestInto(t){Ke(this),Ws(t,this),this.finished=!0;let{buffer:r,view:n,blockLen:o,isLE:s}=this,{pos:i}=this;r[i++]=128,vt(this.buffer.subarray(i)),this.padOffset>o-i&&(this.process(n,0),i=0);for(let u=i;u<o;u++)r[u]=0;If(n,o-8,BigInt(this.length*8),s),this.process(n,0);let a=Ur(t),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");let h=c/4,f=this.get();if(h>f.length)throw new Error("_sha2: outputLen bigger than state");for(let u=0;u<h;u++)a.setUint32(4*u,f[u],s)}digest(){let{buffer:t,outputLen:r}=this;this.digestInto(t);let n=t.slice(0,r);return this.destroy(),n}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());let{blockLen:r,buffer:n,length:o,finished:s,destroyed:i,pos:a}=this;return t.destroyed=i,t.finished=s,t.length=o,t.pos=a,o%r&&t.buffer.set(n),t}clone(){return this._cloneInto()}},Ot=Uint32Array.from([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]);var st=Uint32Array.from([1779033703,4089235720,3144134277,2227873595,1013904242,4271175723,2773480762,1595750129,1359893119,2917565137,2600822924,725511199,528734635,4215389547,1541459225,327033209]);var Dr=BigInt(4294967295),ei=BigInt(32);function Lf(e,t=!1){return t?{h:Number(e&Dr),l:Number(e>>ei&Dr)}:{h:Number(e>>ei&Dr)|0,l:Number(e&Dr)|0}}function ri(e,t=!1){let r=e.length,n=new Uint32Array(r),o=new Uint32Array(r);for(let s=0;s<r;s++){let{h:i,l:a}=Lf(e[s],t);[n[s],o[s]]=[i,a]}return[n,o]}var no=(e,t,r)=>e>>>r,oo=(e,t,r)=>e<<32-r|t>>>r,ie=(e,t,r)=>e>>>r|t<<32-r,ae=(e,t,r)=>e<<32-r|t>>>r,er=(e,t,r)=>e<<64-r|t>>>r-32,rr=(e,t,r)=>e>>>r-32|t<<64-r;function Ut(e,t,r,n){let o=(t>>>0)+(n>>>0);return{h:e+r+(o/2**32|0)|0,l:o|0}}var ni=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0),oi=(e,t,r,n)=>t+r+n+(e/2**32|0)|0,si=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0),ii=(e,t,r,n,o)=>t+r+n+o+(e/2**32|0)|0,ai=(e,t,r,n,o)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(o>>>0),ci=(e,t,r,n,o,s)=>t+r+n+o+s+(e/2**32|0)|0;var Kf=Uint32Array.from([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Wt=new Uint32Array(64),Rr=class extends tr{constructor(t=32){super(64,t,8,!1),this.A=Ot[0]|0,this.B=Ot[1]|0,this.C=Ot[2]|0,this.D=Ot[3]|0,this.E=Ot[4]|0,this.F=Ot[5]|0,this.G=Ot[6]|0,this.H=Ot[7]|0}get(){let{A:t,B:r,C:n,D:o,E:s,F:i,G:a,H:c}=this;return[t,r,n,o,s,i,a,c]}set(t,r,n,o,s,i,a,c){this.A=t|0,this.B=r|0,this.C=n|0,this.D=o|0,this.E=s|0,this.F=i|0,this.G=a|0,this.H=c|0}process(t,r){for(let u=0;u<16;u++,r+=4)Wt[u]=t.getUint32(r,!1);for(let u=16;u<64;u++){let m=Wt[u-15],y=Wt[u-2],w=Bt(m,7)^Bt(m,18)^m>>>3,x=Bt(y,17)^Bt(y,19)^y>>>10;Wt[u]=x+Wt[u-7]+w+Wt[u-16]|0}let{A:n,B:o,C:s,D:i,E:a,F:c,G:h,H:f}=this;for(let u=0;u<64;u++){let m=Bt(a,6)^Bt(a,11)^Bt(a,25),y=f+m+Qs(a,c,h)+Kf[u]+Wt[u]|0,x=(Bt(n,2)^Bt(n,13)^Bt(n,22))+ti(n,o,s)|0;f=h,h=c,c=a,a=i+y|0,i=s,s=o,o=n,n=y+x|0}n=n+this.A|0,o=o+this.B|0,s=s+this.C|0,i=i+this.D|0,a=a+this.E|0,c=c+this.F|0,h=h+this.G|0,f=f+this.H|0,this.set(n,o,s,i,a,c,h,f)}roundClean(){vt(Wt)}destroy(){this.set(0,0,0,0,0,0,0,0),vt(this.buffer)}};var fi=ri(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(e=>BigInt(e))),Cf=fi[0],Uf=fi[1],$t=new Uint32Array(80),Jt=new Uint32Array(80),so=class extends tr{constructor(t=64){super(128,t,16,!1),this.Ah=st[0]|0,this.Al=st[1]|0,this.Bh=st[2]|0,this.Bl=st[3]|0,this.Ch=st[4]|0,this.Cl=st[5]|0,this.Dh=st[6]|0,this.Dl=st[7]|0,this.Eh=st[8]|0,this.El=st[9]|0,this.Fh=st[10]|0,this.Fl=st[11]|0,this.Gh=st[12]|0,this.Gl=st[13]|0,this.Hh=st[14]|0,this.Hl=st[15]|0}get(){let{Ah:t,Al:r,Bh:n,Bl:o,Ch:s,Cl:i,Dh:a,Dl:c,Eh:h,El:f,Fh:u,Fl:m,Gh:y,Gl:w,Hh:x,Hl:l}=this;return[t,r,n,o,s,i,a,c,h,f,u,m,y,w,x,l]}set(t,r,n,o,s,i,a,c,h,f,u,m,y,w,x,l){this.Ah=t|0,this.Al=r|0,this.Bh=n|0,this.Bl=o|0,this.Ch=s|0,this.Cl=i|0,this.Dh=a|0,this.Dl=c|0,this.Eh=h|0,this.El=f|0,this.Fh=u|0,this.Fl=m|0,this.Gh=y|0,this.Gl=w|0,this.Hh=x|0,this.Hl=l|0}process(t,r){for(let _=0;_<16;_++,r+=4)$t[_]=t.getUint32(r),Jt[_]=t.getUint32(r+=4);for(let _=16;_<80;_++){let P=$t[_-15]|0,S=Jt[_-15]|0,D=ie(P,S,1)^ie(P,S,8)^no(P,S,7),K=ae(P,S,1)^ae(P,S,8)^oo(P,S,7),T=$t[_-2]|0,d=Jt[_-2]|0,g=ie(T,d,19)^er(T,d,61)^no(T,d,6),p=ae(T,d,19)^rr(T,d,61)^oo(T,d,6),b=si(K,p,Jt[_-7],Jt[_-16]),A=ii(b,D,g,$t[_-7],$t[_-16]);$t[_]=A|0,Jt[_]=b|0}let{Ah:n,Al:o,Bh:s,Bl:i,Ch:a,Cl:c,Dh:h,Dl:f,Eh:u,El:m,Fh:y,Fl:w,Gh:x,Gl:l,Hh:E,Hl:I}=this;for(let _=0;_<80;_++){let P=ie(u,m,14)^ie(u,m,18)^er(u,m,41),S=ae(u,m,14)^ae(u,m,18)^rr(u,m,41),D=u&y^~u&x,K=m&w^~m&l,T=ai(I,S,K,Uf[_],Jt[_]),d=ci(T,E,P,D,Cf[_],$t[_]),g=T|0,p=ie(n,o,28)^er(n,o,34)^er(n,o,39),b=ae(n,o,28)^rr(n,o,34)^rr(n,o,39),A=n&s^n&a^s&a,B=o&i^o&c^i&c;E=x|0,I=l|0,x=y|0,l=w|0,y=u|0,w=m|0,{h:u,l:m}=Ut(h|0,f|0,d|0,g|0),h=a|0,f=c|0,a=s|0,c=i|0,s=n|0,i=o|0;let v=ni(g,b,B);n=oi(v,d,p,A),o=v|0}({h:n,l:o}=Ut(this.Ah|0,this.Al|0,n|0,o|0)),{h:s,l:i}=Ut(this.Bh|0,this.Bl|0,s|0,i|0),{h:a,l:c}=Ut(this.Ch|0,this.Cl|0,a|0,c|0),{h,l:f}=Ut(this.Dh|0,this.Dl|0,h|0,f|0),{h:u,l:m}=Ut(this.Eh|0,this.El|0,u|0,m|0),{h:y,l:w}=Ut(this.Fh|0,this.Fl|0,y|0,w|0),{h:x,l}=Ut(this.Gh|0,this.Gl|0,x|0,l|0),{h:E,l:I}=Ut(this.Hh|0,this.Hl|0,E|0,I|0),this.set(n,o,s,i,a,c,h,f,u,m,y,w,x,l,E,I)}roundClean(){vt($t,Jt)}destroy(){vt(this.buffer),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}};var Pr=ro(()=>new Rr);var ui=ro(()=>new so);var co=BigInt(0),ao=BigInt(1);function qt(e,t){if(typeof t!="boolean")throw new Error(e+" boolean expected, got "+t)}function nr(e){let t=e.toString(16);return t.length&1?"0"+t:t}function hi(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);return e===""?co:BigInt("0x"+e)}function Ue(e){return hi(Ct(e))}function Dt(e){return ft(e),hi(Ct(Uint8Array.from(e).reverse()))}function Nr(e,t){return Ce(e.toString(16).padStart(t*2,"0"))}function Qt(e,t){return Nr(e,t).reverse()}function F(e,t,r){let n;if(typeof t=="string")try{n=Ce(t)}catch(s){throw new Error(e+" must be hex string or Uint8Array, cause: "+s)}else if(Te(t))n=Uint8Array.from(t);else throw new Error(e+" must be hex string or Uint8Array");let o=n.length;if(typeof r=="number"&&o!==r)throw new Error(e+" of length "+r+" expected, got "+o);return n}var io=e=>typeof e=="bigint"&&co<=e;function li(e,t,r){return io(e)&&io(t)&&io(r)&&t<=e&&e<r}function _t(e,t,r,n){if(!li(t,r,n))throw new Error("expected valid "+e+": "+r+" <= n < "+n+", got "+t)}function di(e){let t;for(t=0;e>co;e>>=ao,t+=1);return t}var ce=e=>(ao<<BigInt(e))-ao;function pi(e,t,r){if(typeof e!="number"||e<2)throw new Error("hashLen must be a number");if(typeof t!="number"||t<2)throw new Error("qByteLen must be a number");if(typeof r!="function")throw new Error("hmacFn must be a function");let n=y=>new Uint8Array(y),o=y=>Uint8Array.of(y),s=n(e),i=n(e),a=0,c=()=>{s.fill(1),i.fill(0),a=0},h=(...y)=>r(i,s,...y),f=(y=n(0))=>{i=h(o(0),y),s=h(),y.length!==0&&(i=h(o(1),y),s=h())},u=()=>{if(a++>=1e3)throw new Error("drbg: tried 1000 values");let y=0,w=[];for(;y<t;){s=h();let x=s.slice();w.push(x),y+=s.length}return bt(...w)};return(y,w)=>{c(),f(y);let x;for(;!(x=w(u()));)f();return c(),x}}function Rt(e,t,r={}){if(!e||typeof e!="object")throw new Error("expected valid options object");function n(o,s,i){let a=e[o];if(i&&a===void 0)return;let c=typeof a;if(c!==s||a===null)throw new Error(`param "${o}" is invalid: expected ${s}, got ${c}`)}Object.entries(t).forEach(([o,s])=>n(o,s,!1)),Object.entries(r).forEach(([o,s])=>n(o,s,!0))}function De(e){let t=new WeakMap;return(r,...n)=>{let o=t.get(r);if(o!==void 0)return o;let s=e(r,...n);return t.set(r,s),s}}var pt=BigInt(0),rt=BigInt(1),fe=BigInt(2),Df=BigInt(3),xi=BigInt(4),gi=BigInt(5),bi=BigInt(8);function Y(e,t){let r=e%t;return r>=pt?r:t+r}function X(e,t,r){let n=e;for(;t-- >pt;)n*=n,n%=r;return n}function yi(e,t){if(e===pt)throw new Error("invert: expected non-zero number");if(t<=pt)throw new Error("invert: expected positive modulus, got "+t);let r=Y(e,t),n=t,o=pt,s=rt,i=rt,a=pt;for(;r!==pt;){let h=n/r,f=n%r,u=o-i*h,m=s-a*h;n=r,r=f,o=i,s=a,i=u,a=m}if(n!==rt)throw new Error("invert: does not exist");return Y(o,t)}function wi(e,t){let r=(e.ORDER+rt)/xi,n=e.pow(t,r);if(!e.eql(e.sqr(n),t))throw new Error("Cannot find square root");return n}function Rf(e,t){let r=(e.ORDER-gi)/bi,n=e.mul(t,fe),o=e.pow(n,r),s=e.mul(t,o),i=e.mul(e.mul(s,fe),o),a=e.mul(s,e.sub(i,e.ONE));if(!e.eql(e.sqr(a),t))throw new Error("Cannot find square root");return a}function Pf(e){if(e<BigInt(3))throw new Error("sqrt is not defined for small field");let t=e-rt,r=0;for(;t%fe===pt;)t/=fe,r++;let n=fe,o=It(e);for(;mi(o,n)===1;)if(n++>1e3)throw new Error("Cannot find square root: probably non-prime P");if(r===1)return wi;let s=o.pow(n,t),i=(t+rt)/fe;return function(c,h){if(c.is0(h))return h;if(mi(c,h)!==1)throw new Error("Cannot find square root");let f=r,u=c.mul(c.ONE,s),m=c.pow(h,t),y=c.pow(h,i);for(;!c.eql(m,c.ONE);){if(c.is0(m))return c.ZERO;let w=1,x=c.sqr(m);for(;!c.eql(x,c.ONE);)if(w++,x=c.sqr(x),w===f)throw new Error("Cannot find square root");let l=rt<<BigInt(f-w-1),E=c.pow(u,l);f=w,u=c.sqr(E),m=c.mul(m,u),y=c.mul(y,E)}return y}}function Nf(e){return e%xi===Df?wi:e%bi===gi?Rf:Pf(e)}var Ei=(e,t)=>(Y(e,t)&rt)===rt,kf=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function fo(e){let t={ORDER:"bigint",MASK:"bigint",BYTES:"number",BITS:"number"},r=kf.reduce((n,o)=>(n[o]="function",n),t);return Rt(e,r),e}function Mf(e,t,r){if(r<pt)throw new Error("invalid exponent, negatives unsupported");if(r===pt)return e.ONE;if(r===rt)return t;let n=e.ONE,o=t;for(;r>pt;)r&rt&&(n=e.mul(n,o)),o=e.sqr(o),r>>=rt;return n}function or(e,t,r=!1){let n=new Array(t.length).fill(r?e.ZERO:void 0),o=t.reduce((i,a,c)=>e.is0(a)?i:(n[c]=i,e.mul(i,a)),e.ONE),s=e.inv(o);return t.reduceRight((i,a,c)=>e.is0(a)?i:(n[c]=e.mul(i,n[c]),e.mul(i,a)),s),n}function mi(e,t){let r=(e.ORDER-rt)/fe,n=e.pow(t,r),o=e.eql(n,e.ONE),s=e.eql(n,e.ZERO),i=e.eql(n,e.neg(e.ONE));if(!o&&!s&&!i)throw new Error("invalid Legendre symbol result");return o?1:s?0:-1}function Si(e,t){t!==void 0&&oe(t);let r=t!==void 0?t:e.toString(2).length,n=Math.ceil(r/8);return{nBitLength:r,nByteLength:n}}function It(e,t,r=!1,n={}){if(e<=pt)throw new Error("invalid field: expected ORDER > 0, got "+e);let o,s;if(typeof t=="object"&&t!=null){if(n.sqrt||r)throw new Error("cannot specify opts in two arguments");let f=t;f.BITS&&(o=f.BITS),f.sqrt&&(s=f.sqrt),typeof f.isLE=="boolean"&&(r=f.isLE)}else typeof t=="number"&&(o=t),n.sqrt&&(s=n.sqrt);let{nBitLength:i,nByteLength:a}=Si(e,o);if(a>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let c,h=Object.freeze({ORDER:e,isLE:r,BITS:i,BYTES:a,MASK:ce(i),ZERO:pt,ONE:rt,create:f=>Y(f,e),isValid:f=>{if(typeof f!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof f);return pt<=f&&f<e},is0:f=>f===pt,isValidNot0:f=>!h.is0(f)&&h.isValid(f),isOdd:f=>(f&rt)===rt,neg:f=>Y(-f,e),eql:(f,u)=>f===u,sqr:f=>Y(f*f,e),add:(f,u)=>Y(f+u,e),sub:(f,u)=>Y(f-u,e),mul:(f,u)=>Y(f*u,e),pow:(f,u)=>Mf(h,f,u),div:(f,u)=>Y(f*yi(u,e),e),sqrN:f=>f*f,addN:(f,u)=>f+u,subN:(f,u)=>f-u,mulN:(f,u)=>f*u,inv:f=>yi(f,e),sqrt:s||(f=>(c||(c=Nf(e)),c(h,f))),toBytes:f=>r?Qt(f,a):Nr(f,a),fromBytes:f=>{if(f.length!==a)throw new Error("Field.fromBytes: expected "+a+" bytes, got "+f.length);return r?Dt(f):Ue(f)},invertBatch:f=>or(h,f),cmov:(f,u,m)=>m?u:f});return Object.freeze(h)}function Ai(e){if(typeof e!="bigint")throw new Error("field order must be bigint");let t=e.toString(2).length;return Math.ceil(t/8)}function uo(e){let t=Ai(e);return t+Math.ceil(t/2)}function vi(e,t,r=!1){let n=e.length,o=Ai(t),s=uo(t);if(n<16||n<s||n>1024)throw new Error("expected "+s+"-1024 bytes of input, got "+n);let i=r?Dt(e):Ue(e),a=Y(i,t-rt)+rt;return r?Qt(a,o):Nr(a,o)}var Pe=BigInt(0),ue=BigInt(1);function Re(e,t){let r=t.negate();return e?r:t}function kr(e,t,r){let n=t==="pz"?i=>i.pz:i=>i.ez,o=or(e.Fp,r.map(n));return r.map((i,a)=>i.toAffine(o[a])).map(e.fromAffine)}function Li(e,t){if(!Number.isSafeInteger(e)||e<=0||e>t)throw new Error("invalid window size, expected [1.."+t+"], got W="+e)}function ho(e,t){Li(e,t);let r=Math.ceil(t/e)+1,n=2**(e-1),o=2**e,s=ce(e),i=BigInt(e);return{windows:r,windowSize:n,mask:s,maxNumber:o,shiftBy:i}}function Bi(e,t,r){let{windowSize:n,mask:o,maxNumber:s,shiftBy:i}=r,a=Number(e&o),c=e>>i;a>n&&(a-=s,c+=ue);let h=t*n,f=h+Math.abs(a)-1,u=a===0,m=a<0,y=t%2!==0;return{nextN:c,offset:f,isZero:u,isNeg:m,isNegF:y,offsetF:h}}function Hf(e,t){if(!Array.isArray(e))throw new Error("array expected");e.forEach((r,n)=>{if(!(r instanceof t))throw new Error("invalid point at index "+n)})}function Of(e,t){if(!Array.isArray(e))throw new Error("array of scalars expected");e.forEach((r,n)=>{if(!t.isValid(r))throw new Error("invalid scalar at index "+n)})}var lo=new WeakMap,Ti=new WeakMap;function po(e){return Ti.get(e)||1}function _i(e){if(e!==Pe)throw new Error("invalid wNAF")}function Mr(e,t){return{constTimeNegate:Re,hasPrecomputes(r){return po(r)!==1},unsafeLadder(r,n,o=e.ZERO){let s=r;for(;n>Pe;)n&ue&&(o=o.add(s)),s=s.double(),n>>=ue;return o},precomputeWindow(r,n){let{windows:o,windowSize:s}=ho(n,t),i=[],a=r,c=a;for(let h=0;h<o;h++){c=a,i.push(c);for(let f=1;f<s;f++)c=c.add(a),i.push(c);a=c.double()}return i},wNAF(r,n,o){let s=e.ZERO,i=e.BASE,a=ho(r,t);for(let c=0;c<a.windows;c++){let{nextN:h,offset:f,isZero:u,isNeg:m,isNegF:y,offsetF:w}=Bi(o,c,a);o=h,u?i=i.add(Re(y,n[w])):s=s.add(Re(m,n[f]))}return _i(o),{p:s,f:i}},wNAFUnsafe(r,n,o,s=e.ZERO){let i=ho(r,t);for(let a=0;a<i.windows&&o!==Pe;a++){let{nextN:c,offset:h,isZero:f,isNeg:u}=Bi(o,a,i);if(o=c,!f){let m=n[h];s=s.add(u?m.negate():m)}}return _i(o),s},getPrecomputes(r,n,o){let s=lo.get(n);return s||(s=this.precomputeWindow(n,r),r!==1&&(typeof o=="function"&&(s=o(s)),lo.set(n,s))),s},wNAFCached(r,n,o){let s=po(r);return this.wNAF(s,this.getPrecomputes(s,r,o),n)},wNAFCachedUnsafe(r,n,o,s){let i=po(r);return i===1?this.unsafeLadder(r,n,s):this.wNAFUnsafe(i,this.getPrecomputes(i,r,o),n,s)},setWindowSize(r,n){Li(n,t),Ti.set(r,n),lo.delete(r)}}}function Ki(e,t,r,n){let o=t,s=e.ZERO,i=e.ZERO;for(;r>Pe||n>Pe;)r&ue&&(s=s.add(o)),n&ue&&(i=i.add(o)),o=o.double(),r>>=ue,n>>=ue;return{p1:s,p2:i}}function Hr(e,t,r,n){Hf(r,e),Of(n,t);let o=r.length,s=n.length;if(o!==s)throw new Error("arrays of points and scalars must have equal length");let i=e.ZERO,a=di(BigInt(o)),c=1;a>12?c=a-3:a>4?c=a-2:a>0&&(c=2);let h=ce(c),f=new Array(Number(h)+1).fill(i),u=Math.floor((t.BITS-1)/c)*c,m=i;for(let y=u;y>=0;y-=c){f.fill(i);for(let x=0;x<s;x++){let l=n[x],E=Number(l>>BigInt(y)&h);f[E]=f[E].add(r[x])}let w=i;for(let x=f.length-1,l=i;x>0;x--)l=l.add(f[x]),w=w.add(l);if(m=m.add(w),y!==0)for(let x=0;x<c;x++)m=m.double()}return m}function Ii(e,t){if(t){if(t.ORDER!==e)throw new Error("Field.ORDER must match order: Fp == p, Fn == n");return fo(t),t}else return It(e)}function Or(e,t,r={}){if(!t||typeof t!="object")throw new Error(`expected valid ${e} CURVE object`);for(let a of["p","n","h"]){let c=t[a];if(!(typeof c=="bigint"&&c>Pe))throw new Error(`CURVE.${a} must be positive bigint`)}let n=Ii(t.p,r.Fp),o=Ii(t.n,r.Fn),i=["Gx","Gy","a",e==="weierstrass"?"b":"d"];for(let a of i)if(!n.isValid(t[a]))throw new Error(`CURVE.${a} must be valid field element of CURVE.Fp`);return{Fp:n,Fn:o}}var Pt=BigInt(0),yt=BigInt(1),yo=BigInt(2),qf=BigInt(8),Vf={zip215:!0};function zf(e,t,r,n){let o=e.sqr(r),s=e.sqr(n),i=e.add(e.mul(t.a,o),s),a=e.add(e.ONE,e.mul(t.d,e.mul(o,s)));return e.eql(i,a)}function Gf(e,t={}){let{Fp:r,Fn:n}=Or("edwards",e,t),{h:o,n:s}=e;Rt(t,{},{uvRatio:"function"});let i=yo<<BigInt(n.BYTES*8)-yt,a=x=>r.create(x),c=t.uvRatio||((x,l)=>{try{return{isValid:!0,value:r.sqrt(r.div(x,l))}}catch{return{isValid:!1,value:Pt}}});if(!zf(r,e,e.Gx,e.Gy))throw new Error("bad curve params: generator point");function h(x,l,E=!1){let I=E?yt:Pt;return _t("coordinate "+x,l,I,i),l}function f(x){if(!(x instanceof y))throw new Error("ExtendedPoint expected")}let u=De((x,l)=>{let{ex:E,ey:I,ez:_}=x,P=x.is0();l==null&&(l=P?qf:r.inv(_));let S=a(E*l),D=a(I*l),K=a(_*l);if(P)return{x:Pt,y:yt};if(K!==yt)throw new Error("invZ was invalid");return{x:S,y:D}}),m=De(x=>{let{a:l,d:E}=e;if(x.is0())throw new Error("bad point: ZERO");let{ex:I,ey:_,ez:P,et:S}=x,D=a(I*I),K=a(_*_),T=a(P*P),d=a(T*T),g=a(D*l),p=a(T*a(g+K)),b=a(d+a(E*a(D*K)));if(p!==b)throw new Error("bad point: equation left != right (1)");let A=a(I*_),B=a(P*S);if(A!==B)throw new Error("bad point: equation left != right (2)");return!0});class y{constructor(l,E,I,_){this.ex=h("x",l),this.ey=h("y",E),this.ez=h("z",I,!0),this.et=h("t",_),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(l){if(l instanceof y)throw new Error("extended point not allowed");let{x:E,y:I}=l||{};return h("x",E),h("y",I),new y(E,I,yt,a(E*I))}static normalizeZ(l){return kr(y,"ez",l)}static msm(l,E){return Hr(y,n,l,E)}_setWindowSize(l){this.precompute(l)}precompute(l=8,E=!0){return w.setWindowSize(this,l),E||this.multiply(yo),this}assertValidity(){m(this)}equals(l){f(l);let{ex:E,ey:I,ez:_}=this,{ex:P,ey:S,ez:D}=l,K=a(E*D),T=a(P*_),d=a(I*D),g=a(S*_);return K===T&&d===g}is0(){return this.equals(y.ZERO)}negate(){return new y(a(-this.ex),this.ey,this.ez,a(-this.et))}double(){let{a:l}=e,{ex:E,ey:I,ez:_}=this,P=a(E*E),S=a(I*I),D=a(yo*a(_*_)),K=a(l*P),T=E+I,d=a(a(T*T)-P-S),g=K+S,p=g-D,b=K-S,A=a(d*p),B=a(g*b),v=a(d*b),L=a(p*g);return new y(A,B,L,v)}add(l){f(l);let{a:E,d:I}=e,{ex:_,ey:P,ez:S,et:D}=this,{ex:K,ey:T,ez:d,et:g}=l,p=a(_*K),b=a(P*T),A=a(D*I*g),B=a(S*d),v=a((_+P)*(K+T)-p-b),L=B-A,C=B+A,R=a(b-E*p),U=a(v*L),M=a(C*R),N=a(v*R),H=a(L*C);return new y(U,M,H,N)}subtract(l){return this.add(l.negate())}multiply(l){let E=l;_t("scalar",E,yt,s);let{p:I,f:_}=w.wNAFCached(this,E,y.normalizeZ);return y.normalizeZ([I,_])[0]}multiplyUnsafe(l,E=y.ZERO){let I=l;return _t("scalar",I,Pt,s),I===Pt?y.ZERO:this.is0()||I===yt?this:w.wNAFCachedUnsafe(this,I,y.normalizeZ,E)}isSmallOrder(){return this.multiplyUnsafe(o).is0()}isTorsionFree(){return w.wNAFCachedUnsafe(this,s).is0()}toAffine(l){return u(this,l)}clearCofactor(){return o===yt?this:this.multiplyUnsafe(o)}static fromBytes(l,E=!1){return ft(l),this.fromHex(l,E)}static fromHex(l,E=!1){let{d:I,a:_}=e,P=r.BYTES;l=F("pointHex",l,P),qt("zip215",E);let S=l.slice(),D=l[P-1];S[P-1]=D&-129;let K=Dt(S),T=E?i:r.ORDER;_t("pointHex.y",K,Pt,T);let d=a(K*K),g=a(d-yt),p=a(I*d-_),{isValid:b,value:A}=c(g,p);if(!b)throw new Error("Point.fromHex: invalid y coordinate");let B=(A&yt)===yt,v=(D&128)!==0;if(!E&&A===Pt&&v)throw new Error("Point.fromHex: x=0 and x_0=1");return v!==B&&(A=a(-A)),y.fromAffine({x:A,y:K})}static fromPrivateScalar(l){return y.BASE.multiply(l)}toBytes(){let{x:l,y:E}=this.toAffine(),I=Qt(E,r.BYTES);return I[I.length-1]|=l&yt?128:0,I}toRawBytes(){return this.toBytes()}toHex(){return Ct(this.toBytes())}toString(){return`<Point ${this.is0()?"ZERO":this.toHex()}>`}}y.BASE=new y(e.Gx,e.Gy,yt,a(e.Gx*e.Gy)),y.ZERO=new y(Pt,yt,yt,Pt),y.Fp=r,y.Fn=n;let w=Mr(y,n.BYTES*8);return y}function Ff(e,t){Rt(t,{hash:"function"},{adjustScalarBytes:"function",randomBytes:"function",domain:"function",prehash:"function",mapToCurve:"function"});let{prehash:r,hash:n}=t,{BASE:o,Fp:s,Fn:i}=e,a=i.ORDER,c=t.randomBytes||se,h=t.adjustScalarBytes||(S=>S),f=t.domain||((S,D,K)=>{if(qt("phflag",K),D.length||K)throw new Error("Contexts/pre-hash are not supported");return S});function u(S){return i.create(S)}function m(S){return u(Dt(S))}function y(S){let D=s.BYTES;S=F("private key",S,D);let K=F("hashed private key",n(S),2*D),T=h(K.slice(0,D)),d=K.slice(D,2*D),g=m(T);return{head:T,prefix:d,scalar:g}}function w(S){let{head:D,prefix:K,scalar:T}=y(S),d=o.multiply(T),g=d.toBytes();return{head:D,prefix:K,scalar:T,point:d,pointBytes:g}}function x(S){return w(S).pointBytes}function l(S=Uint8Array.of(),...D){let K=bt(...D);return m(n(f(K,F("context",S),!!r)))}function E(S,D,K={}){S=F("message",S),r&&(S=r(S));let{prefix:T,scalar:d,pointBytes:g}=w(D),p=l(K.context,T,S),b=o.multiply(p).toBytes(),A=l(K.context,b,g,S),B=u(p+A*d);_t("signature.s",B,Pt,a);let v=s.BYTES,L=bt(b,Qt(B,v));return F("result",L,v*2)}let I=Vf;function _(S,D,K,T=I){let{context:d,zip215:g}=T,p=s.BYTES;S=F("signature",S,2*p),D=F("message",D),K=F("publicKey",K,p),g!==void 0&&qt("zip215",g),r&&(D=r(D));let b=Dt(S.slice(p,2*p)),A,B,v;try{A=e.fromHex(K,g),B=e.fromHex(S.slice(0,p),g),v=o.multiplyUnsafe(b)}catch{return!1}if(!g&&A.isSmallOrder())return!1;let L=l(d,B.toBytes(),A.toBytes(),D);return B.add(A.multiplyUnsafe(L)).subtract(v).clearCofactor().is0()}return o.precompute(8),{getPublicKey:x,sign:E,verify:_,utils:{getExtendedPublicKey:w,randomPrivateKey:()=>c(s.BYTES),precompute(S=8,D=e.BASE){return D.precompute(S,!1)}},Point:e}}function jf(e){let t={a:e.a,d:e.d,p:e.Fp.ORDER,n:e.n,h:e.h,Gx:e.Gx,Gy:e.Gy},r=e.Fp,n=It(t.n,e.nBitLength,!0),o={Fp:r,Fn:n,uvRatio:e.uvRatio},s={hash:e.hash,randomBytes:e.randomBytes,adjustScalarBytes:e.adjustScalarBytes,domain:e.domain,prehash:e.prehash,mapToCurve:e.mapToCurve};return{CURVE:t,curveOpts:o,eddsaOpts:s}}function Zf(e,t){return Object.assign({},t,{ExtendedPoint:t.Point,CURVE:e})}function Ci(e){let{CURVE:t,curveOpts:r,eddsaOpts:n}=jf(e),o=Gf(t,r),s=Ff(o,n);return Zf(e,s)}var sr=BigInt(0),Ne=BigInt(1),qr=BigInt(2);function Yf(e){return Rt(e,{adjustScalarBytes:"function",powPminus2:"function"}),Object.freeze({...e})}function Ui(e){let t=Yf(e),{P:r,type:n,adjustScalarBytes:o,powPminus2:s,randomBytes:i}=t,a=n==="x25519";if(!a&&n!=="x448")throw new Error("invalid type");let c=i||se,h=a?255:448,f=a?32:56,u=BigInt(a?9:5),m=BigInt(a?121665:39081),y=a?qr**BigInt(254):qr**BigInt(447),w=a?BigInt(8)*qr**BigInt(251)-Ne:BigInt(4)*qr**BigInt(445)-Ne,x=y+w+Ne,l=d=>Y(d,r),E=I(u);function I(d){return Qt(l(d),f)}function _(d){let g=F("u coordinate",d,f);return a&&(g[31]&=127),l(Dt(g))}function P(d){return Dt(o(F("scalar",d,f)))}function S(d,g){let p=T(_(g),P(d));if(p===sr)throw new Error("invalid private or public key received");return I(p)}function D(d){return S(d,E)}function K(d,g,p){let b=l(d*(g-p));return g=l(g-b),p=l(p+b),{x_2:g,x_3:p}}function T(d,g){_t("u",d,sr,r),_t("scalar",g,y,x);let p=g,b=d,A=Ne,B=sr,v=d,L=Ne,C=sr;for(let U=BigInt(h-1);U>=sr;U--){let M=p>>U&Ne;C^=M,{x_2:A,x_3:v}=K(C,A,v),{x_2:B,x_3:L}=K(C,B,L),C=M;let N=A+B,H=l(N*N),q=A-B,j=l(q*q),O=H-j,W=v+L,mt=v-L,at=l(mt*N),ot=l(W*q),ge=at+ot,Nt=at-ot;v=l(ge*ge),L=l(b*l(Nt*Nt)),A=l(H*j),B=l(O*(H+l(m*O)))}({x_2:A,x_3:v}=K(C,A,v)),{x_2:B,x_3:L}=K(C,B,L);let R=s(B);return l(A*R)}return{scalarMult:S,scalarMultBase:D,getSharedSecret:(d,g)=>S(d,g),getPublicKey:d=>D(d),utils:{randomPrivateKey:()=>c(f)},GuBytes:E.slice()}}var zd=BigInt(0),Xf=BigInt(1),Di=BigInt(2),Wf=BigInt(3),$f=BigInt(5),Jf=BigInt(8),ir={p:BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffed"),n:BigInt("0x1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ed"),h:Jf,a:BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffec"),d:BigInt("0x52036cee2b6ffe738cc740797779e89800700a4d4141d8ab75eb4dca135978a3"),Gx:BigInt("0x216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a"),Gy:BigInt("0x6666666666666666666666666666666666666666666666666666666666666658")};function Pi(e){let t=BigInt(10),r=BigInt(20),n=BigInt(40),o=BigInt(80),s=ir.p,a=e*e%s*e%s,c=X(a,Di,s)*a%s,h=X(c,Xf,s)*e%s,f=X(h,$f,s)*h%s,u=X(f,t,s)*f%s,m=X(u,r,s)*u%s,y=X(m,n,s)*m%s,w=X(y,o,s)*y%s,x=X(w,o,s)*y%s,l=X(x,t,s)*f%s;return{pow_p_5_8:X(l,Di,s)*e%s,b2:a}}function Ni(e){return e[0]&=248,e[31]&=127,e[31]|=64,e}var Ri=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752");function Qf(e,t){let r=ir.p,n=Y(t*t*t,r),o=Y(n*n*t,r),s=Pi(e*o).pow_p_5_8,i=Y(e*n*s,r),a=Y(t*i*i,r),c=i,h=Y(i*Ri,r),f=a===e,u=a===Y(-e,r),m=a===Y(-e*Ri,r);return f&&(i=c),(u||m)&&(i=h),Ei(i,r)&&(i=Y(-i,r)),{isValid:f||u,value:i}}var tu=It(ir.p,void 0,!0),eu={...ir,Fp:tu,hash:ui,adjustScalarBytes:Ni,uvRatio:Qf},ki=Ci(eu);var ar=(()=>{let e=ir.p;return Ui({P:e,type:"x25519",powPminus2:t=>{let{pow_p_5_8:r,b2:n}=Pi(t);return Y(X(r,Wf,e)*n,e)},adjustScalarBytes:Ni})})();var cr=class extends Error{constructor(t="An error occurred while verifying a message"){super(t),this.name="VerificationError"}},Vr=class extends Error{constructor(t="Missing Web Crypto API"){super(t),this.name="WebCryptoMissingError"}};var Mi={get(e=globalThis){let t=e.crypto;if(t?.subtle==null)throw new Vr("Missing Web Crypto API. The most likely cause of this error is that this page is being accessed from an insecure context (i.e. not HTTPS). For more information and possible resolutions see https://github.com/libp2p/js-libp2p/blob/main/packages/crypto/README.md#web-crypto-api");return t}};var St=Mi;var zr=32;var mo,ru=(async()=>{try{return await St.get().subtle.generateKey({name:"Ed25519"},!0,["sign","verify"]),!0}catch{return!1}})();async function nu(e,t,r){if(e.buffer instanceof ArrayBuffer){let n=await St.get().subtle.importKey("raw",e.buffer,{name:"Ed25519"},!1,["verify"]);return await St.get().subtle.verify({name:"Ed25519"},n,t,r instanceof Uint8Array?r:r.subarray())}throw new TypeError("WebCrypto does not support SharedArrayBuffer for Ed25519 keys")}function ou(e,t,r){return ki.verify(t,r instanceof Uint8Array?r:r.subarray(),e)}async function Hi(e,t,r){return mo==null&&(mo=await ru),mo?nu(e,t,r):ou(e,t,r)}function Gr(e){return e==null?!1:typeof e.then=="function"&&typeof e.catch=="function"&&typeof e.finally=="function"}var Fr=class{type="Ed25519";raw;constructor(t){this.raw=xo(t,zr)}toMultihash(){return Kt.digest(Yt(this))}toCID(){return ct.createV1(114,this.toMultihash())}toString(){return J.encode(this.toMultihash().bytes).substring(1)}equals(t){return t==null||!(t.raw instanceof Uint8Array)?!1:dt(this.raw,t.raw)}verify(t,r,n){n?.signal?.throwIfAborted();let o=Hi(this.raw,r,t);return Gr(o)?o.then(s=>(n?.signal?.throwIfAborted(),s)):o}};function qi(e){return e=xo(e,zr),new Fr(e)}function xo(e,t){if(e=Uint8Array.from(e??[]),e.length!==t)throw new Q(`Key must be a Uint8Array of length ${t}, got ${e.length}`);return e}var iu=Math.pow(2,7),au=Math.pow(2,14),cu=Math.pow(2,21),go=Math.pow(2,28),bo=Math.pow(2,35),wo=Math.pow(2,42),Eo=Math.pow(2,49),G=128,ut=127;function wt(e){if(e<iu)return 1;if(e<au)return 2;if(e<cu)return 3;if(e<go)return 4;if(e<bo)return 5;if(e<wo)return 6;if(e<Eo)return 7;if(Number.MAX_SAFE_INTEGER!=null&&e>Number.MAX_SAFE_INTEGER)throw new RangeError("Could not encode varint");return 8}function So(e,t,r=0){switch(wt(e)){case 8:t[r++]=e&255|G,e/=128;case 7:t[r++]=e&255|G,e/=128;case 6:t[r++]=e&255|G,e/=128;case 5:t[r++]=e&255|G,e/=128;case 4:t[r++]=e&255|G,e>>>=7;case 3:t[r++]=e&255|G,e>>>=7;case 2:t[r++]=e&255|G,e>>>=7;case 1:{t[r++]=e&255,e>>>=7;break}default:throw new Error("unreachable")}return t}function fu(e,t,r=0){switch(wt(e)){case 8:t.set(r++,e&255|G),e/=128;case 7:t.set(r++,e&255|G),e/=128;case 6:t.set(r++,e&255|G),e/=128;case 5:t.set(r++,e&255|G),e/=128;case 4:t.set(r++,e&255|G),e>>>=7;case 3:t.set(r++,e&255|G),e>>>=7;case 2:t.set(r++,e&255|G),e>>>=7;case 1:{t.set(r++,e&255),e>>>=7;break}default:throw new Error("unreachable")}return t}function Ao(e,t){let r=e[t],n=0;if(n+=r&ut,r<G||(r=e[t+1],n+=(r&ut)<<7,r<G)||(r=e[t+2],n+=(r&ut)<<14,r<G)||(r=e[t+3],n+=(r&ut)<<21,r<G)||(r=e[t+4],n+=(r&ut)*go,r<G)||(r=e[t+5],n+=(r&ut)*bo,r<G)||(r=e[t+6],n+=(r&ut)*wo,r<G)||(r=e[t+7],n+=(r&ut)*Eo,r<G))return n;throw new RangeError("Could not decode varint")}function uu(e,t){let r=e.get(t),n=0;if(n+=r&ut,r<G||(r=e.get(t+1),n+=(r&ut)<<7,r<G)||(r=e.get(t+2),n+=(r&ut)<<14,r<G)||(r=e.get(t+3),n+=(r&ut)<<21,r<G)||(r=e.get(t+4),n+=(r&ut)*go,r<G)||(r=e.get(t+5),n+=(r&ut)*bo,r<G)||(r=e.get(t+6),n+=(r&ut)*wo,r<G)||(r=e.get(t+7),n+=(r&ut)*Eo,r<G))return n;throw new RangeError("Could not decode varint")}function jr(e,t,r=0){return t==null&&(t=et(wt(e))),t instanceof Uint8Array?So(e,t,r):fu(e,t,r)}function Zr(e,t=0){return e instanceof Uint8Array?Ao(e,t):uu(e,t)}var Bo=new Float32Array([-0]),te=new Uint8Array(Bo.buffer);function Vi(e,t,r){Bo[0]=e,t[r]=te[0],t[r+1]=te[1],t[r+2]=te[2],t[r+3]=te[3]}function zi(e,t){return te[0]=e[t],te[1]=e[t+1],te[2]=e[t+2],te[3]=e[t+3],Bo[0]}var _o=new Float64Array([-0]),ht=new Uint8Array(_o.buffer);function Gi(e,t,r){_o[0]=e,t[r]=ht[0],t[r+1]=ht[1],t[r+2]=ht[2],t[r+3]=ht[3],t[r+4]=ht[4],t[r+5]=ht[5],t[r+6]=ht[6],t[r+7]=ht[7]}function Fi(e,t){return ht[0]=e[t],ht[1]=e[t+1],ht[2]=e[t+2],ht[3]=e[t+3],ht[4]=e[t+4],ht[5]=e[t+5],ht[6]=e[t+6],ht[7]=e[t+7],_o[0]}var hu=BigInt(Number.MAX_SAFE_INTEGER),lu=BigInt(Number.MIN_SAFE_INTEGER),Et=class e{lo;hi;constructor(t,r){this.lo=t|0,this.hi=r|0}toNumber(t=!1){if(!t&&this.hi>>>31>0){let r=~this.lo+1>>>0,n=~this.hi>>>0;return r===0&&(n=n+1>>>0),-(r+n*4294967296)}return this.lo+this.hi*4294967296}toBigInt(t=!1){if(t)return BigInt(this.lo>>>0)+(BigInt(this.hi>>>0)<<32n);if(this.hi>>>31){let r=~this.lo+1>>>0,n=~this.hi>>>0;return r===0&&(n=n+1>>>0),-(BigInt(r)+(BigInt(n)<<32n))}return BigInt(this.lo>>>0)+(BigInt(this.hi>>>0)<<32n)}toString(t=!1){return this.toBigInt(t).toString()}zzEncode(){let t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this}zzDecode(){let t=-(this.lo&1);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this}length(){let t=this.lo,r=(this.lo>>>28|this.hi<<4)>>>0,n=this.hi>>>24;return n===0?r===0?t<16384?t<128?1:2:t<2097152?3:4:r<16384?r<128?5:6:r<2097152?7:8:n<128?9:10}static fromBigInt(t){if(t===0n)return he;if(t<hu&&t>lu)return this.fromNumber(Number(t));let r=t<0n;r&&(t=-t);let n=t>>32n,o=t-(n<<32n);return r&&(n=~n|0n,o=~o|0n,++o>ji&&(o=0n,++n>ji&&(n=0n))),new e(Number(o),Number(n))}static fromNumber(t){if(t===0)return he;let r=t<0;r&&(t=-t);let n=t>>>0,o=(t-n)/4294967296>>>0;return r&&(o=~o>>>0,n=~n>>>0,++n>4294967295&&(n=0,++o>4294967295&&(o=0))),new e(n,o)}static from(t){return typeof t=="number"?e.fromNumber(t):typeof t=="bigint"?e.fromBigInt(t):typeof t=="string"?e.fromBigInt(BigInt(t)):t.low!=null||t.high!=null?new e(t.low>>>0,t.high>>>0):he}},he=new Et(0,0);he.toBigInt=function(){return 0n};he.zzEncode=he.zzDecode=function(){return this};he.length=function(){return 1};var ji=4294967296n;function Zi(e){let t=0,r=0;for(let n=0;n<e.length;++n)r=e.charCodeAt(n),r<128?t+=1:r<2048?t+=2:(r&64512)===55296&&(e.charCodeAt(n+1)&64512)===56320?(++n,t+=4):t+=3;return t}function Yi(e,t,r){if(r-t<1)return"";let o,s=[],i=0,a;for(;t<r;)a=e[t++],a<128?s[i++]=a:a>191&&a<224?s[i++]=(a&31)<<6|e[t++]&63:a>239&&a<365?(a=((a&7)<<18|(e[t++]&63)<<12|(e[t++]&63)<<6|e[t++]&63)-65536,s[i++]=55296+(a>>10),s[i++]=56320+(a&1023)):s[i++]=(a&15)<<12|(e[t++]&63)<<6|e[t++]&63,i>8191&&((o??(o=[])).push(String.fromCharCode.apply(String,s)),i=0);return o!=null?(i>0&&o.push(String.fromCharCode.apply(String,s.slice(0,i))),o.join("")):String.fromCharCode.apply(String,s.slice(0,i))}function Io(e,t,r){let n=r,o,s;for(let i=0;i<e.length;++i)o=e.charCodeAt(i),o<128?t[r++]=o:o<2048?(t[r++]=o>>6|192,t[r++]=o&63|128):(o&64512)===55296&&((s=e.charCodeAt(i+1))&64512)===56320?(o=65536+((o&1023)<<10)+(s&1023),++i,t[r++]=o>>18|240,t[r++]=o>>12&63|128,t[r++]=o>>6&63|128,t[r++]=o&63|128):(t[r++]=o>>12|224,t[r++]=o>>6&63|128,t[r++]=o&63|128);return r-n}function Lt(e,t){return RangeError(`index out of range: ${e.pos} + ${t??1} > ${e.len}`)}function Yr(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}var Lo=class{buf;pos;len;_slice=Uint8Array.prototype.subarray;constructor(t){this.buf=t,this.pos=0,this.len=t.length}uint32(){let t=4294967295;if(t=(this.buf[this.pos]&127)>>>0,this.buf[this.pos++]<128||(t=(t|(this.buf[this.pos]&127)<<7)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&127)<<14)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&127)<<21)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&15)<<28)>>>0,this.buf[this.pos++]<128))return t;if((this.pos+=5)>this.len)throw this.pos=this.len,Lt(this,10);return t}int32(){return this.uint32()|0}sint32(){let t=this.uint32();return t>>>1^-(t&1)|0}bool(){return this.uint32()!==0}fixed32(){if(this.pos+4>this.len)throw Lt(this,4);return Yr(this.buf,this.pos+=4)}sfixed32(){if(this.pos+4>this.len)throw Lt(this,4);return Yr(this.buf,this.pos+=4)|0}float(){if(this.pos+4>this.len)throw Lt(this,4);let t=zi(this.buf,this.pos);return this.pos+=4,t}double(){if(this.pos+8>this.len)throw Lt(this,4);let t=Fi(this.buf,this.pos);return this.pos+=8,t}bytes(){let t=this.uint32(),r=this.pos,n=this.pos+t;if(n>this.len)throw Lt(this,t);return this.pos+=t,r===n?new Uint8Array(0):this.buf.subarray(r,n)}string(){let t=this.bytes();return Yi(t,0,t.length)}skip(t){if(typeof t=="number"){if(this.pos+t>this.len)throw Lt(this,t);this.pos+=t}else do if(this.pos>=this.len)throw Lt(this);while((this.buf[this.pos++]&128)!==0);return this}skipType(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;(t=this.uint32()&7)!==4;)this.skipType(t);break;case 5:this.skip(4);break;default:throw Error(`invalid wire type ${t} at offset ${this.pos}`)}return this}readLongVarint(){let t=new Et(0,0),r=0;if(this.len-this.pos>4){for(;r<4;++r)if(t.lo=(t.lo|(this.buf[this.pos]&127)<<r*7)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(this.buf[this.pos]&127)<<28)>>>0,t.hi=(t.hi|(this.buf[this.pos]&127)>>4)>>>0,this.buf[this.pos++]<128)return t;r=0}else{for(;r<3;++r){if(this.pos>=this.len)throw Lt(this);if(t.lo=(t.lo|(this.buf[this.pos]&127)<<r*7)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(this.buf[this.pos++]&127)<<r*7)>>>0,t}if(this.len-this.pos>4){for(;r<5;++r)if(t.hi=(t.hi|(this.buf[this.pos]&127)<<r*7+3)>>>0,this.buf[this.pos++]<128)return t}else for(;r<5;++r){if(this.pos>=this.len)throw Lt(this);if(t.hi=(t.hi|(this.buf[this.pos]&127)<<r*7+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}readFixed64(){if(this.pos+8>this.len)throw Lt(this,8);let t=Yr(this.buf,this.pos+=4),r=Yr(this.buf,this.pos+=4);return new Et(t,r)}int64(){return this.readLongVarint().toBigInt()}int64Number(){return this.readLongVarint().toNumber()}int64String(){return this.readLongVarint().toString()}uint64(){return this.readLongVarint().toBigInt(!0)}uint64Number(){let t=Ao(this.buf,this.pos);return this.pos+=wt(t),t}uint64String(){return this.readLongVarint().toString(!0)}sint64(){return this.readLongVarint().zzDecode().toBigInt()}sint64Number(){return this.readLongVarint().zzDecode().toNumber()}sint64String(){return this.readLongVarint().zzDecode().toString()}fixed64(){return this.readFixed64().toBigInt()}fixed64Number(){return this.readFixed64().toNumber()}fixed64String(){return this.readFixed64().toString()}sfixed64(){return this.readFixed64().toBigInt()}sfixed64Number(){return this.readFixed64().toNumber()}sfixed64String(){return this.readFixed64().toString()}};function To(e){return new Lo(e instanceof Uint8Array?e:e.subarray())}function le(e,t,r){let n=To(e);return t.decode(n,void 0,r)}function Ko(e){let t=e??8192,r=t>>>1,n,o=t;return function(i){if(i<1||i>r)return et(i);o+i>t&&(n=et(t),o=0);let a=n.subarray(o,o+=i);return(o&7)!==0&&(o=(o|7)+1),a}}var de=class{fn;len;next;val;constructor(t,r,n){this.fn=t,this.len=r,this.next=void 0,this.val=n}};function Co(){}var Do=class{head;tail;len;next;constructor(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}},du=Ko();function pu(e){return globalThis.Buffer!=null?et(e):du(e)}var ur=class{len;head;tail;states;constructor(){this.len=0,this.head=new de(Co,0,0),this.tail=this.head,this.states=null}_push(t,r,n){return this.tail=this.tail.next=new de(t,r,n),this.len+=r,this}uint32(t){return this.len+=(this.tail=this.tail.next=new Ro((t=t>>>0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this}int32(t){return t<0?this._push(Xr,10,Et.fromNumber(t)):this.uint32(t)}sint32(t){return this.uint32((t<<1^t>>31)>>>0)}uint64(t){let r=Et.fromBigInt(t);return this._push(Xr,r.length(),r)}uint64Number(t){return this._push(So,wt(t),t)}uint64String(t){return this.uint64(BigInt(t))}int64(t){return this.uint64(t)}int64Number(t){return this.uint64Number(t)}int64String(t){return this.uint64String(t)}sint64(t){let r=Et.fromBigInt(t).zzEncode();return this._push(Xr,r.length(),r)}sint64Number(t){let r=Et.fromNumber(t).zzEncode();return this._push(Xr,r.length(),r)}sint64String(t){return this.sint64(BigInt(t))}bool(t){return this._push(Uo,1,t?1:0)}fixed32(t){return this._push(fr,4,t>>>0)}sfixed32(t){return this.fixed32(t)}fixed64(t){let r=Et.fromBigInt(t);return this._push(fr,4,r.lo)._push(fr,4,r.hi)}fixed64Number(t){let r=Et.fromNumber(t);return this._push(fr,4,r.lo)._push(fr,4,r.hi)}fixed64String(t){return this.fixed64(BigInt(t))}sfixed64(t){return this.fixed64(t)}sfixed64Number(t){return this.fixed64Number(t)}sfixed64String(t){return this.fixed64String(t)}float(t){return this._push(Vi,4,t)}double(t){return this._push(Gi,8,t)}bytes(t){let r=t.length>>>0;return r===0?this._push(Uo,1,0):this.uint32(r)._push(mu,r,t)}string(t){let r=Zi(t);return r!==0?this.uint32(r)._push(Io,r,t):this._push(Uo,1,0)}fork(){return this.states=new Do(this),this.head=this.tail=new de(Co,0,0),this.len=0,this}reset(){return this.states!=null?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new de(Co,0,0),this.len=0),this}ldelim(){let t=this.head,r=this.tail,n=this.len;return this.reset().uint32(n),n!==0&&(this.tail.next=t.next,this.tail=r,this.len+=n),this}finish(){let t=this.head.next,r=pu(this.len),n=0;for(;t!=null;)t.fn(t.val,r,n),n+=t.len,t=t.next;return r}};function Uo(e,t,r){t[r]=e&255}function yu(e,t,r){for(;e>127;)t[r++]=e&127|128,e>>>=7;t[r]=e}var Ro=class extends de{next;constructor(t,r){super(yu,t,r),this.next=void 0}};function Xr(e,t,r){for(;e.hi!==0;)t[r++]=e.lo&127|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[r++]=e.lo&127|128,e.lo=e.lo>>>7;t[r++]=e.lo}function fr(e,t,r){t[r]=e&255,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function mu(e,t,r){t.set(e,r)}globalThis.Buffer!=null&&(ur.prototype.bytes=function(e){let t=e.length>>>0;return this.uint32(t),t>0&&this._push(xu,t,e),this},ur.prototype.string=function(e){let t=globalThis.Buffer.byteLength(e);return this.uint32(t),t>0&&this._push(gu,t,e),this});function xu(e,t,r){t.set(e,r)}function gu(e,t,r){e.length<40?Io(e,t,r):t.utf8Write!=null?t.utf8Write(e,r):t.set(Z(e),r)}function Po(){return new ur}function pe(e,t){let r=Po();return t.encode(e,r,{lengthDelimited:!1}),r.finish()}var ke;(function(e){e[e.VARINT=0]="VARINT",e[e.BIT64=1]="BIT64",e[e.LENGTH_DELIMITED=2]="LENGTH_DELIMITED",e[e.START_GROUP=3]="START_GROUP",e[e.END_GROUP=4]="END_GROUP",e[e.BIT32=5]="BIT32"})(ke||(ke={}));function Wr(e,t,r,n){return{name:e,type:t,encode:r,decode:n}}function No(e){function t(o){if(e[o.toString()]==null)throw new Error("Invalid enum value");return e[o]}let r=function(s,i){let a=t(s);i.int32(a)},n=function(s){let i=s.int32();return t(i)};return Wr("enum",ke.VARINT,r,n)}function ye(e,t){return Wr("message",ke.LENGTH_DELIMITED,e,t)}var hr=class extends Error{code="ERR_MAX_LENGTH";name="MaxLengthError"};var nt;(function(e){e.RSA="RSA",e.Ed25519="Ed25519",e.secp256k1="secp256k1",e.ECDSA="ECDSA"})(nt||(nt={}));var ko;(function(e){e[e.RSA=0]="RSA",e[e.Ed25519=1]="Ed25519",e[e.secp256k1=2]="secp256k1",e[e.ECDSA=3]="ECDSA"})(ko||(ko={}));(function(e){e.codec=()=>No(ko)})(nt||(nt={}));var Vt;(function(e){let t;e.codec=()=>(t==null&&(t=ye((r,n,o={})=>{o.lengthDelimited!==!1&&n.fork(),r.Type!=null&&(n.uint32(8),nt.codec().encode(r.Type,n)),r.Data!=null&&(n.uint32(18),n.bytes(r.Data)),o.lengthDelimited!==!1&&n.ldelim()},(r,n,o={})=>{let s={},i=n==null?r.len:r.pos+n;for(;r.pos<i;){let a=r.uint32();switch(a>>>3){case 1:{s.Type=nt.codec().decode(r);break}case 2:{s.Data=r.bytes();break}default:{r.skipType(a&7);break}}}return s})),t),e.encode=r=>pe(r,e.codec()),e.decode=(r,n)=>le(r,e.codec(),n)})(Vt||(Vt={}));var Mo;(function(e){let t;e.codec=()=>(t==null&&(t=ye((r,n,o={})=>{o.lengthDelimited!==!1&&n.fork(),r.Type!=null&&(n.uint32(8),nt.codec().encode(r.Type,n)),r.Data!=null&&(n.uint32(18),n.bytes(r.Data)),o.lengthDelimited!==!1&&n.ldelim()},(r,n,o={})=>{let s={},i=n==null?r.len:r.pos+n;for(;r.pos<i;){let a=r.uint32();switch(a>>>3){case 1:{s.Type=nt.codec().decode(r);break}case 2:{s.Data=r.bytes();break}default:{r.skipType(a&7);break}}}return s})),t),e.encode=r=>pe(r,e.codec()),e.decode=(r,n)=>le(r,e.codec(),n)})(Mo||(Mo={}));var dr={};xt(dr,{MAX_RSA_KEY_SIZE:()=>Ho,generateRSAKeyPair:()=>ta,jwkToJWKKeyPair:()=>ea,jwkToPkcs1:()=>Su,jwkToPkix:()=>zo,jwkToRSAPrivateKey:()=>Zo,pkcs1MessageToJwk:()=>qo,pkcs1MessageToRSAPrivateKey:()=>Go,pkcs1ToJwk:()=>Eu,pkcs1ToRSAPrivateKey:()=>Qi,pkixMessageToJwk:()=>Vo,pkixMessageToRSAPublicKey:()=>jo,pkixToJwk:()=>Au,pkixToRSAPublicKey:()=>Fo});var ee=Pr;var Me=class{type="RSA";jwk;_raw;_multihash;constructor(t,r){this.jwk=t,this._multihash=r}get raw(){return this._raw==null&&(this._raw=dr.jwkToPkix(this.jwk)),this._raw}toMultihash(){return this._multihash}toCID(){return ct.createV1(114,this._multihash)}toString(){return J.encode(this.toMultihash().bytes).substring(1)}equals(t){return t==null||!(t.raw instanceof Uint8Array)?!1:dt(this.raw,t.raw)}verify(t,r,n){return Ji(this.jwk,r,t,n)}},lr=class{type="RSA";jwk;_raw;publicKey;constructor(t,r){this.jwk=t,this.publicKey=r}get raw(){return this._raw==null&&(this._raw=dr.jwkToPkcs1(this.jwk)),this._raw}equals(t){return t==null||!(t.raw instanceof Uint8Array)?!1:dt(this.raw,t.raw)}sign(t,r){return $i(this.jwk,t,r)}};var Ho=8192,Oo=18,bu=1062,wu=Uint8Array.from([48,13,6,9,42,134,72,134,247,13,1,1,1,5,0]);function Eu(e){let t=Mt(e);return qo(t)}function qo(e){return{n:V(e[1],"base64url"),e:V(e[2],"base64url"),d:V(e[3],"base64url"),p:V(e[4],"base64url"),q:V(e[5],"base64url"),dp:V(e[6],"base64url"),dq:V(e[7],"base64url"),qi:V(e[8],"base64url"),kty:"RSA"}}function Su(e){if(e.n==null||e.e==null||e.d==null||e.p==null||e.q==null||e.dp==null||e.dq==null||e.qi==null)throw new Q("JWK was missing components");return Zt([gt(Uint8Array.from([0])),gt(Z(e.n,"base64url")),gt(Z(e.e,"base64url")),gt(Z(e.d,"base64url")),gt(Z(e.p,"base64url")),gt(Z(e.q,"base64url")),gt(Z(e.dp,"base64url")),gt(Z(e.dq,"base64url")),gt(Z(e.qi,"base64url"))]).subarray()}function Au(e){let t=Mt(e,{offset:0});return Vo(t)}function Vo(e){let t=Mt(e[1],{offset:0});return{kty:"RSA",n:V(t[0],"base64url"),e:V(t[1],"base64url")}}function zo(e){if(e.n==null||e.e==null)throw new Q("JWK was missing components");return Zt([wu,Cr(Zt([gt(Z(e.n,"base64url")),gt(Z(e.e,"base64url"))]))]).subarray()}function Qi(e){let t=Mt(e);return Go(t)}function Go(e){let t=qo(e);return Zo(t)}function Fo(e,t){if(e.byteLength>=bu)throw new be("Key size is too large");let r=Mt(e,{offset:0});return jo(r,e,t)}function jo(e,t,r){let n=Vo(e);if(r==null){let o=ee(Vt.encode({Type:nt.RSA,Data:t}));r=At(Oo,o)}return new Me(n,r)}function Zo(e){if(na(e)>Ho)throw new Q("Key size is too large");let t=ea(e),r=ee(Vt.encode({Type:nt.RSA,Data:zo(t.publicKey)})),n=At(Oo,r);return new lr(t.privateKey,new Me(t.publicKey,n))}async function ta(e){if(e>Ho)throw new Q("Key size is too large");let t=await ra(e),r=ee(Vt.encode({Type:nt.RSA,Data:zo(t.publicKey)})),n=At(Oo,r);return new lr(t.privateKey,new Me(t.publicKey,n))}function ea(e){if(e==null)throw new Q("Missing key parameter");return{privateKey:e,publicKey:{kty:e.kty,n:e.n,e:e.e}}}async function ra(e,t){let r=await St.get().subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:e,publicExponent:new Uint8Array([1,0,1]),hash:{name:"SHA-256"}},!0,["sign","verify"]);t?.signal?.throwIfAborted();let n=await vu(r,t);return{privateKey:n[0],publicKey:n[1]}}async function $i(e,t,r){let n=await St.get().subtle.importKey("jwk",e,{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},!1,["sign"]);r?.signal?.throwIfAborted();let o=await St.get().subtle.sign({name:"RSASSA-PKCS1-v1_5"},n,t instanceof Uint8Array?t:t.subarray());return r?.signal?.throwIfAborted(),new Uint8Array(o,0,o.byteLength)}async function Ji(e,t,r,n){let o=await St.get().subtle.importKey("jwk",e,{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},!1,["verify"]);n?.signal?.throwIfAborted();let s=await St.get().subtle.verify({name:"RSASSA-PKCS1-v1_5"},o,t,r instanceof Uint8Array?r:r.subarray());return n?.signal?.throwIfAborted(),s}async function vu(e,t){if(e.privateKey==null||e.publicKey==null)throw new Q("Private and public key are required");let r=await Promise.all([St.get().subtle.exportKey("jwk",e.privateKey),St.get().subtle.exportKey("jwk",e.publicKey)]);return t?.signal?.throwIfAborted(),r}function na(e){if(e.kty!=="RSA")throw new Q("invalid key type");if(e.n==null)throw new Q("invalid key modulus");return Z(e.n,"base64url").length*8}var $r=class extends Le{constructor(t,r){super(),this.finished=!1,this.destroyed=!1,Qe(t);let n=Xt(r);if(this.iHash=t.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;let o=this.blockLen,s=new Uint8Array(o);s.set(n.length>o?t.create().update(n).digest():n);for(let i=0;i<s.length;i++)s[i]^=54;this.iHash.update(s),this.oHash=t.create();for(let i=0;i<s.length;i++)s[i]^=106;this.oHash.update(s),vt(s)}update(t){return Ke(this),this.iHash.update(t),this}digestInto(t){Ke(this),ft(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){let t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));let{oHash:r,iHash:n,finished:o,destroyed:s,blockLen:i,outputLen:a}=this;return t=t,t.finished=o,t.destroyed=s,t.blockLen=i,t.outputLen=a,t.oHash=r._cloneInto(t.oHash),t.iHash=n._cloneInto(t.iHash),t}clone(){return this._cloneInto()}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}},He=(e,t,r)=>new $r(e,t).update(r).digest();He.create=(e,t)=>new $r(e,t);function oa(e){e.lowS!==void 0&&qt("lowS",e.lowS),e.prehash!==void 0&&qt("prehash",e.prehash)}var Yo=class extends Error{constructor(t=""){super(t)}},zt={Err:Yo,_tlv:{encode:(e,t)=>{let{Err:r}=zt;if(e<0||e>256)throw new r("tlv.encode: wrong tag");if(t.length&1)throw new r("tlv.encode: unpadded data");let n=t.length/2,o=nr(n);if(o.length/2&128)throw new r("tlv.encode: long form length too big");let s=n>127?nr(o.length/2|128):"";return nr(e)+s+o+t},decode(e,t){let{Err:r}=zt,n=0;if(e<0||e>256)throw new r("tlv.encode: wrong tag");if(t.length<2||t[n++]!==e)throw new r("tlv.decode: wrong tlv");let o=t[n++],s=!!(o&128),i=0;if(!s)i=o;else{let c=o&127;if(!c)throw new r("tlv.decode(long): indefinite length not supported");if(c>4)throw new r("tlv.decode(long): byte length is too big");let h=t.subarray(n,n+c);if(h.length!==c)throw new r("tlv.decode: length bytes not complete");if(h[0]===0)throw new r("tlv.decode(long): zero leftmost byte");for(let f of h)i=i<<8|f;if(n+=c,i<128)throw new r("tlv.decode(long): not minimal encoding")}let a=t.subarray(n,n+i);if(a.length!==i)throw new r("tlv.decode: wrong value length");return{v:a,l:t.subarray(n+i)}}},_int:{encode(e){let{Err:t}=zt;if(e<pr)throw new t("integer: negative integers are not allowed");let r=nr(e);if(Number.parseInt(r[0],16)&8&&(r="00"+r),r.length&1)throw new t("unexpected DER parsing assertion: unpadded hex");return r},decode(e){let{Err:t}=zt;if(e[0]&128)throw new t("invalid signature integer: negative");if(e[0]===0&&!(e[1]&128))throw new t("invalid signature integer: unnecessary leading zero");return Ue(e)}},toSig(e){let{Err:t,_int:r,_tlv:n}=zt,o=F("signature",e),{v:s,l:i}=n.decode(48,o);if(i.length)throw new t("invalid signature: left bytes after parsing");let{v:a,l:c}=n.decode(2,s),{v:h,l:f}=n.decode(2,c);if(f.length)throw new t("invalid signature: left bytes after parsing");return{r:r.decode(a),s:r.decode(h)}},hexFromSig(e){let{_tlv:t,_int:r}=zt,n=t.encode(2,r.encode(e.r)),o=t.encode(2,r.encode(e.s)),s=n+o;return t.encode(48,s)}},pr=BigInt(0),yr=BigInt(1),Bu=BigInt(2),Jr=BigInt(3),_u=BigInt(4);function Iu(e,t,r){function n(o){let s=e.sqr(o),i=e.mul(s,o);return e.add(e.add(i,e.mul(o,t)),r)}return n}function sa(e,t,r){let{BYTES:n}=e;function o(s){let i;if(typeof s=="bigint")i=s;else{let a=F("private key",s);if(t){if(!t.includes(a.length*2))throw new Error("invalid private key");let c=new Uint8Array(n);c.set(a,c.length-a.length),a=c}try{i=e.fromBytes(a)}catch{throw new Error(`invalid private key: expected ui8a of size ${n}, got ${typeof s}`)}}if(r&&(i=e.create(i)),!e.isValidNot0(i))throw new Error("invalid private key: out of range [1..N-1]");return i}return o}function Lu(e,t={}){let{Fp:r,Fn:n}=Or("weierstrass",e,t),{h:o,n:s}=e;Rt(t,{},{allowInfinityPoint:"boolean",clearCofactor:"function",isTorsionFree:"function",fromBytes:"function",toBytes:"function",endo:"object",wrapPrivateKey:"boolean"});let{endo:i}=t;if(i&&(!r.is0(e.a)||typeof i.beta!="bigint"||typeof i.splitScalar!="function"))throw new Error('invalid endo: expected "beta": bigint and "splitScalar": function');function a(){if(!r.isOdd)throw new Error("compression is not supported: Field does not have .isOdd()")}function c(T,d,g){let{x:p,y:b}=d.toAffine(),A=r.toBytes(p);if(qt("isCompressed",g),g){a();let B=!r.isOdd(b);return bt(ia(B),A)}else return bt(Uint8Array.of(4),A,r.toBytes(b))}function h(T){ft(T);let d=r.BYTES,g=d+1,p=2*d+1,b=T.length,A=T[0],B=T.subarray(1);if(b===g&&(A===2||A===3)){let v=r.fromBytes(B);if(!r.isValid(v))throw new Error("bad point: is not on curve, wrong x");let L=m(v),C;try{C=r.sqrt(L)}catch(M){let N=M instanceof Error?": "+M.message:"";throw new Error("bad point: is not on curve, sqrt error"+N)}a();let R=r.isOdd(C);return(A&1)===1!==R&&(C=r.neg(C)),{x:v,y:C}}else if(b===p&&A===4){let v=r.fromBytes(B.subarray(d*0,d*1)),L=r.fromBytes(B.subarray(d*1,d*2));if(!y(v,L))throw new Error("bad point: is not on curve");return{x:v,y:L}}else throw new Error(`bad point: got length ${b}, expected compressed=${g} or uncompressed=${p}`)}let f=t.toBytes||c,u=t.fromBytes||h,m=Iu(r,e.a,e.b);function y(T,d){let g=r.sqr(d),p=m(T);return r.eql(g,p)}if(!y(e.Gx,e.Gy))throw new Error("bad curve params: generator point");let w=r.mul(r.pow(e.a,Jr),_u),x=r.mul(r.sqr(e.b),BigInt(27));if(r.is0(r.add(w,x)))throw new Error("bad curve params: a or b");function l(T,d,g=!1){if(!r.isValid(d)||g&&r.is0(d))throw new Error(`bad point coordinate ${T}`);return d}function E(T){if(!(T instanceof S))throw new Error("ProjectivePoint expected")}let I=De((T,d)=>{let{px:g,py:p,pz:b}=T;if(r.eql(b,r.ONE))return{x:g,y:p};let A=T.is0();d==null&&(d=A?r.ONE:r.inv(b));let B=r.mul(g,d),v=r.mul(p,d),L=r.mul(b,d);if(A)return{x:r.ZERO,y:r.ZERO};if(!r.eql(L,r.ONE))throw new Error("invZ was invalid");return{x:B,y:v}}),_=De(T=>{if(T.is0()){if(t.allowInfinityPoint&&!r.is0(T.py))return;throw new Error("bad point: ZERO")}let{x:d,y:g}=T.toAffine();if(!r.isValid(d)||!r.isValid(g))throw new Error("bad point: x or y not field elements");if(!y(d,g))throw new Error("bad point: equation left != right");if(!T.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});function P(T,d,g,p,b){return g=new S(r.mul(g.px,T),g.py,g.pz),d=Re(p,d),g=Re(b,g),d.add(g)}class S{constructor(d,g,p){this.px=l("x",d),this.py=l("y",g,!0),this.pz=l("z",p),Object.freeze(this)}static fromAffine(d){let{x:g,y:p}=d||{};if(!d||!r.isValid(g)||!r.isValid(p))throw new Error("invalid affine point");if(d instanceof S)throw new Error("projective point not allowed");return r.is0(g)&&r.is0(p)?S.ZERO:new S(g,p,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(d){return kr(S,"pz",d)}static fromBytes(d){return ft(d),S.fromHex(d)}static fromHex(d){let g=S.fromAffine(u(F("pointHex",d)));return g.assertValidity(),g}static fromPrivateKey(d){let g=sa(n,t.allowedPrivateKeyLengths,t.wrapPrivateKey);return S.BASE.multiply(g(d))}static msm(d,g){return Hr(S,n,d,g)}precompute(d=8,g=!0){return K.setWindowSize(this,d),g||this.multiply(Jr),this}_setWindowSize(d){this.precompute(d)}assertValidity(){_(this)}hasEvenY(){let{y:d}=this.toAffine();if(!r.isOdd)throw new Error("Field doesn't support isOdd");return!r.isOdd(d)}equals(d){E(d);let{px:g,py:p,pz:b}=this,{px:A,py:B,pz:v}=d,L=r.eql(r.mul(g,v),r.mul(A,b)),C=r.eql(r.mul(p,v),r.mul(B,b));return L&&C}negate(){return new S(this.px,r.neg(this.py),this.pz)}double(){let{a:d,b:g}=e,p=r.mul(g,Jr),{px:b,py:A,pz:B}=this,v=r.ZERO,L=r.ZERO,C=r.ZERO,R=r.mul(b,b),U=r.mul(A,A),M=r.mul(B,B),N=r.mul(b,A);return N=r.add(N,N),C=r.mul(b,B),C=r.add(C,C),v=r.mul(d,C),L=r.mul(p,M),L=r.add(v,L),v=r.sub(U,L),L=r.add(U,L),L=r.mul(v,L),v=r.mul(N,v),C=r.mul(p,C),M=r.mul(d,M),N=r.sub(R,M),N=r.mul(d,N),N=r.add(N,C),C=r.add(R,R),R=r.add(C,R),R=r.add(R,M),R=r.mul(R,N),L=r.add(L,R),M=r.mul(A,B),M=r.add(M,M),R=r.mul(M,N),v=r.sub(v,R),C=r.mul(M,U),C=r.add(C,C),C=r.add(C,C),new S(v,L,C)}add(d){E(d);let{px:g,py:p,pz:b}=this,{px:A,py:B,pz:v}=d,L=r.ZERO,C=r.ZERO,R=r.ZERO,U=e.a,M=r.mul(e.b,Jr),N=r.mul(g,A),H=r.mul(p,B),q=r.mul(b,v),j=r.add(g,p),O=r.add(A,B);j=r.mul(j,O),O=r.add(N,H),j=r.sub(j,O),O=r.add(g,b);let W=r.add(A,v);return O=r.mul(O,W),W=r.add(N,q),O=r.sub(O,W),W=r.add(p,b),L=r.add(B,v),W=r.mul(W,L),L=r.add(H,q),W=r.sub(W,L),R=r.mul(U,O),L=r.mul(M,q),R=r.add(L,R),L=r.sub(H,R),R=r.add(H,R),C=r.mul(L,R),H=r.add(N,N),H=r.add(H,N),q=r.mul(U,q),O=r.mul(M,O),H=r.add(H,q),q=r.sub(N,q),q=r.mul(U,q),O=r.add(O,q),N=r.mul(H,O),C=r.add(C,N),N=r.mul(W,O),L=r.mul(j,L),L=r.sub(L,N),N=r.mul(j,H),R=r.mul(W,R),R=r.add(R,N),new S(L,C,R)}subtract(d){return this.add(d.negate())}is0(){return this.equals(S.ZERO)}multiply(d){let{endo:g}=t;if(!n.isValidNot0(d))throw new Error("invalid scalar: out of range");let p,b,A=B=>K.wNAFCached(this,B,S.normalizeZ);if(g){let{k1neg:B,k1:v,k2neg:L,k2:C}=g.splitScalar(d),{p:R,f:U}=A(v),{p:M,f:N}=A(C);b=U.add(N),p=P(g.beta,R,M,B,L)}else{let{p:B,f:v}=A(d);p=B,b=v}return S.normalizeZ([p,b])[0]}multiplyUnsafe(d){let{endo:g}=t,p=this;if(!n.isValid(d))throw new Error("invalid scalar: out of range");if(d===pr||p.is0())return S.ZERO;if(d===yr)return p;if(K.hasPrecomputes(this))return this.multiply(d);if(g){let{k1neg:b,k1:A,k2neg:B,k2:v}=g.splitScalar(d),{p1:L,p2:C}=Ki(S,p,A,v);return P(g.beta,L,C,b,B)}else return K.wNAFCachedUnsafe(p,d)}multiplyAndAddUnsafe(d,g,p){let b=this.multiplyUnsafe(g).add(d.multiplyUnsafe(p));return b.is0()?void 0:b}toAffine(d){return I(this,d)}isTorsionFree(){let{isTorsionFree:d}=t;return o===yr?!0:d?d(S,this):K.wNAFCachedUnsafe(this,s).is0()}clearCofactor(){let{clearCofactor:d}=t;return o===yr?this:d?d(S,this):this.multiplyUnsafe(o)}toBytes(d=!0){return qt("isCompressed",d),this.assertValidity(),f(S,this,d)}toRawBytes(d=!0){return this.toBytes(d)}toHex(d=!0){return Ct(this.toBytes(d))}toString(){return`<Point ${this.is0()?"ZERO":this.toHex()}>`}}S.BASE=new S(e.Gx,e.Gy,r.ONE),S.ZERO=new S(r.ZERO,r.ONE,r.ZERO),S.Fp=r,S.Fn=n;let D=n.BITS,K=Mr(S,t.endo?Math.ceil(D/2):D);return S}function ia(e){return Uint8Array.of(e?2:3)}function Tu(e,t,r={}){Rt(t,{hash:"function"},{hmac:"function",lowS:"boolean",randomBytes:"function",bits2int:"function",bits2int_modN:"function"});let n=t.randomBytes||se,o=t.hmac||((p,...b)=>He(t.hash,p,bt(...b))),{Fp:s,Fn:i}=e,{ORDER:a,BITS:c}=i;function h(p){let b=a>>yr;return p>b}function f(p){return h(p)?i.neg(p):p}function u(p,b){if(!i.isValidNot0(b))throw new Error(`invalid signature ${p}: out of range 1..CURVE.n`)}class m{constructor(b,A,B){u("r",b),u("s",A),this.r=b,this.s=A,B!=null&&(this.recovery=B),Object.freeze(this)}static fromCompact(b){let A=i.BYTES,B=F("compactSignature",b,A*2);return new m(i.fromBytes(B.subarray(0,A)),i.fromBytes(B.subarray(A,A*2)))}static fromDER(b){let{r:A,s:B}=zt.toSig(F("DER",b));return new m(A,B)}assertValidity(){}addRecoveryBit(b){return new m(this.r,this.s,b)}recoverPublicKey(b){let A=s.ORDER,{r:B,s:v,recovery:L}=this;if(L==null||![0,1,2,3].includes(L))throw new Error("recovery id invalid");if(a*Bu<A&&L>1)throw new Error("recovery id is ambiguous for h>1 curve");let R=L===2||L===3?B+a:B;if(!s.isValid(R))throw new Error("recovery id 2 or 3 invalid");let U=s.toBytes(R),M=e.fromHex(bt(ia((L&1)===0),U)),N=i.inv(R),H=_(F("msgHash",b)),q=i.create(-H*N),j=i.create(v*N),O=e.BASE.multiplyUnsafe(q).add(M.multiplyUnsafe(j));if(O.is0())throw new Error("point at infinify");return O.assertValidity(),O}hasHighS(){return h(this.s)}normalizeS(){return this.hasHighS()?new m(this.r,i.neg(this.s),this.recovery):this}toBytes(b){if(b==="compact")return bt(i.toBytes(this.r),i.toBytes(this.s));if(b==="der")return Ce(zt.hexFromSig(this));throw new Error("invalid format")}toDERRawBytes(){return this.toBytes("der")}toDERHex(){return Ct(this.toBytes("der"))}toCompactRawBytes(){return this.toBytes("compact")}toCompactHex(){return Ct(this.toBytes("compact"))}}let y=sa(i,r.allowedPrivateKeyLengths,r.wrapPrivateKey),w={isValidPrivateKey(p){try{return y(p),!0}catch{return!1}},normPrivateKeyToScalar:y,randomPrivateKey:()=>{let p=a;return vi(n(uo(p)),p)},precompute(p=8,b=e.BASE){return b.precompute(p,!1)}};function x(p,b=!0){return e.fromPrivateKey(p).toBytes(b)}function l(p){if(typeof p=="bigint")return!1;if(p instanceof e)return!0;let A=F("key",p).length,B=s.BYTES,v=B+1,L=2*B+1;if(!(r.allowedPrivateKeyLengths||i.BYTES===v))return A===v||A===L}function E(p,b,A=!0){if(l(p)===!0)throw new Error("first arg must be private key");if(l(b)===!1)throw new Error("second arg must be public key");return e.fromHex(b).multiply(y(p)).toBytes(A)}let I=t.bits2int||function(p){if(p.length>8192)throw new Error("input is too large");let b=Ue(p),A=p.length*8-c;return A>0?b>>BigInt(A):b},_=t.bits2int_modN||function(p){return i.create(I(p))},P=ce(c);function S(p){return _t("num < 2^"+c,p,pr,P),i.toBytes(p)}function D(p,b,A=K){if(["recovered","canonical"].some(j=>j in A))throw new Error("sign() legacy options not supported");let{hash:B}=t,{lowS:v,prehash:L,extraEntropy:C}=A;v==null&&(v=!0),p=F("msgHash",p),oa(A),L&&(p=F("prehashed msgHash",B(p)));let R=_(p),U=y(b),M=[S(U),S(R)];if(C!=null&&C!==!1){let j=C===!0?n(s.BYTES):C;M.push(F("extraEntropy",j))}let N=bt(...M),H=R;function q(j){let O=I(j);if(!i.isValidNot0(O))return;let W=i.inv(O),mt=e.BASE.multiply(O).toAffine(),at=i.create(mt.x);if(at===pr)return;let ot=i.create(W*i.create(H+at*U));if(ot===pr)return;let ge=(mt.x===at?0:2)|Number(mt.y&yr),Nt=ot;return v&&h(ot)&&(Nt=f(ot),ge^=1),new m(at,Nt,ge)}return{seed:N,k2sig:q}}let K={lowS:t.lowS,prehash:!1},T={lowS:t.lowS,prehash:!1};function d(p,b,A=K){let{seed:B,k2sig:v}=D(p,b,A);return pi(t.hash.outputLen,i.BYTES,o)(B,v)}e.BASE.precompute(8);function g(p,b,A,B=T){let v=p;b=F("msgHash",b),A=F("publicKey",A),oa(B);let{lowS:L,prehash:C,format:R}=B;if("strict"in B)throw new Error("options.strict was renamed to lowS");if(R!==void 0&&!["compact","der","js"].includes(R))throw new Error('format must be "compact", "der" or "js"');let U=typeof v=="string"||Te(v),M=!U&&!R&&typeof v=="object"&&v!==null&&typeof v.r=="bigint"&&typeof v.s=="bigint";if(!U&&!M)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let N,H;try{if(M)if(R===void 0||R==="js")N=new m(v.r,v.s);else throw new Error("invalid format");if(U){try{R!=="compact"&&(N=m.fromDER(v))}catch(Nt){if(!(Nt instanceof zt.Err))throw Nt}!N&&R!=="der"&&(N=m.fromCompact(v))}H=e.fromHex(A)}catch{return!1}if(!N||L&&N.hasHighS())return!1;C&&(b=t.hash(b));let{r:q,s:j}=N,O=_(b),W=i.inv(j),mt=i.create(O*W),at=i.create(q*W),ot=e.BASE.multiplyUnsafe(mt).add(H.multiplyUnsafe(at));return ot.is0()?!1:i.create(ot.x)===q}return Object.freeze({getPublicKey:x,getSharedSecret:E,sign:d,verify:g,utils:w,Point:e,Signature:m})}function Ku(e){let t={a:e.a,b:e.b,p:e.Fp.ORDER,n:e.n,h:e.h,Gx:e.Gx,Gy:e.Gy},r=e.Fp,n=It(t.n,e.nBitLength),o={Fp:r,Fn:n,allowedPrivateKeyLengths:e.allowedPrivateKeyLengths,allowInfinityPoint:e.allowInfinityPoint,endo:e.endo,wrapPrivateKey:e.wrapPrivateKey,isTorsionFree:e.isTorsionFree,clearCofactor:e.clearCofactor,fromBytes:e.fromBytes,toBytes:e.toBytes};return{CURVE:t,curveOpts:o}}function Cu(e){let{CURVE:t,curveOpts:r}=Ku(e),n={hash:e.hash,hmac:e.hmac,randomBytes:e.randomBytes,lowS:e.lowS,bits2int:e.bits2int,bits2int_modN:e.bits2int_modN};return{CURVE:t,curveOpts:r,ecdsaOpts:n}}function Uu(e,t){return Object.assign({},t,{ProjectivePoint:t.Point,CURVE:e})}function aa(e){let{CURVE:t,curveOpts:r,ecdsaOpts:n}=Cu(e),o=Lu(t,r),s=Tu(o,n,r);return Uu(e,s)}function ca(e,t){let r=n=>aa({...e,hash:n});return{...r(t),create:r}}var Qr={p:BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),n:BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),h:BigInt(1),a:BigInt(0),b:BigInt(7),Gx:BigInt("0x79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798"),Gy:BigInt("0x483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8")},T0=BigInt(0),Du=BigInt(1),Xo=BigInt(2),fa=(e,t)=>(e+t/Xo)/t;function Ru(e){let t=Qr.p,r=BigInt(3),n=BigInt(6),o=BigInt(11),s=BigInt(22),i=BigInt(23),a=BigInt(44),c=BigInt(88),h=e*e*e%t,f=h*h*e%t,u=X(f,r,t)*f%t,m=X(u,r,t)*f%t,y=X(m,Xo,t)*h%t,w=X(y,o,t)*y%t,x=X(w,s,t)*w%t,l=X(x,a,t)*x%t,E=X(l,c,t)*l%t,I=X(E,a,t)*x%t,_=X(I,r,t)*f%t,P=X(_,i,t)*w%t,S=X(P,n,t)*h%t,D=X(S,Xo,t);if(!Wo.eql(Wo.sqr(D),e))throw new Error("Cannot find square root");return D}var Wo=It(Qr.p,void 0,void 0,{sqrt:Ru}),Oe=ca({...Qr,Fp:Wo,lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:e=>{let t=Qr.n,r=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),n=-Du*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),o=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),s=r,i=BigInt("0x100000000000000000000000000000000"),a=fa(s*e,t),c=fa(-n*e,t),h=Y(e-a*r-c*o,t),f=Y(-a*n-c*s,t),u=h>i,m=f>i;if(u&&(h=t-h),m&&(f=t-f),h>i||f>i)throw new Error("splitScalar: Endomorphism failed, k="+e);return{k1neg:u,k1:h,k2neg:m,k2:f}}}},Pr);function ua(e,t,r,n){let o=Tr.digest(r instanceof Uint8Array?r:r.subarray());if(Gr(o))return o.then(({digest:s})=>(n?.signal?.throwIfAborted(),Oe.verify(t,s,e))).catch(s=>{throw s.name==="AbortError"?s:new cr(String(s))});try{return n?.signal?.throwIfAborted(),Oe.verify(t,o.digest,e)}catch(s){throw new cr(String(s))}}var tn=class{type="secp256k1";raw;_key;constructor(t){this._key=la(t),this.raw=ha(this._key)}toMultihash(){return Kt.digest(Yt(this))}toCID(){return ct.createV1(114,this.toMultihash())}toString(){return J.encode(this.toMultihash().bytes).substring(1)}equals(t){return t==null||!(t.raw instanceof Uint8Array)?!1:dt(this.raw,t.raw)}verify(t,r,n){return ua(this._key,r,t,n)}};function da(e){return new tn(e)}function ha(e){return Oe.ProjectivePoint.fromHex(e).toRawBytes(!0)}function la(e){try{return Oe.ProjectivePoint.fromHex(e),e}catch(t){throw new be(String(t))}}function mr(e,t){let{Type:r,Data:n}=Vt.decode(e),o=n??new Uint8Array;switch(r){case nt.RSA:return Fo(o,t);case nt.Ed25519:return qi(o);case nt.secp256k1:return da(o);case nt.ECDSA:return js(o);default:throw new we}}function Yt(e){return Vt.encode({Type:nt[e.type],Data:e.raw})}var pa=Symbol.for("nodejs.util.inspect.custom"),Pu=114,xr=class{type;multihash;publicKey;string;constructor(t){this.type=t.type,this.multihash=t.multihash,Object.defineProperty(this,"string",{enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return`PeerId(${this.toString()})`}[In]=!0;toString(){return this.string==null&&(this.string=J.encode(this.multihash.bytes).slice(1)),this.string}toMultihash(){return this.multihash}toCID(){return ct.createV1(Pu,this.multihash)}toJSON(){return this.toString()}equals(t){if(t==null)return!1;if(t instanceof Uint8Array)return dt(this.multihash.bytes,t);if(typeof t=="string")return this.toString()===t;if(t?.toMultihash()?.bytes!=null)return dt(this.multihash.bytes,t.toMultihash().bytes);throw new Error("not valid Id")}[pa](){return`PeerId(${this.toString()})`}},en=class extends xr{type="RSA";publicKey;constructor(t){super({...t,type:"RSA"}),this.publicKey=t.publicKey}},rn=class extends xr{type="Ed25519";publicKey;constructor(t){super({...t,type:"Ed25519"}),this.publicKey=t.publicKey}},nn=class extends xr{type="secp256k1";publicKey;constructor(t){super({...t,type:"secp256k1"}),this.publicKey=t.publicKey}},Nu=2336,$o=class{type="url";multihash;publicKey;url;constructor(t){this.url=t.toString(),this.multihash=Kt.digest(Z(this.url))}[pa](){return`PeerId(${this.url})`}[In]=!0;toString(){return this.toCID().toString()}toMultihash(){return this.multihash}toCID(){return ct.createV1(Nu,this.toMultihash())}toJSON(){return this.toString()}equals(t){return t==null?!1:(t instanceof Uint8Array&&(t=V(t)),t.toString()===this.toString())}};function Jo(e){if(e.type==="Ed25519")return new rn({multihash:e.toCID().multihash,publicKey:e});if(e.type==="secp256k1")return new nn({multihash:e.toCID().multihash,publicKey:e});if(e.type==="RSA")return new en({multihash:e.toCID().multihash,publicKey:e});throw new we}var on=class extends Error{name="InvalidMessageLengthError";code="ERR_INVALID_MSG_LENGTH"},qe=class extends Error{name="InvalidDataLengthError";code="ERR_MSG_DATA_TOO_LONG"},sn=class extends Error{name="InvalidDataLengthLengthError";code="ERR_MSG_LENGTH_TOO_LONG"},gr=class extends Error{name="UnexpectedEOFError";code="ERR_UNEXPECTED_EOF"};function an(e){return e[Symbol.asyncIterator]!=null}function ya(e,t){if(e.byteLength>t)throw new qe("Message length too long")}var fn=e=>{let t=wt(e),r=et(t);return jr(e,r),fn.bytes=t,r};fn.bytes=0;function ma(e,t){t=t??{};let r=t.lengthEncoder??fn,n=t?.maxDataLength??4194304;function*o(s){ya(s,n);let i=r(s.byteLength);i instanceof Uint8Array?yield i:yield*i,s instanceof Uint8Array?yield s:yield*s}return an(e)?async function*(){for await(let s of e)yield*o(s)}():function*(){for(let s of e)yield*o(s)}()}ma.single=(e,t)=>{t=t??{};let r=t.lengthEncoder??fn,n=t?.maxDataLength??4194304;return ya(e,n),new z(r(e.byteLength),e)};var me;(function(e){e[e.LENGTH=0]="LENGTH",e[e.DATA=1]="DATA"})(me||(me={}));var Qo=e=>{let t=Zr(e);return Qo.bytes=wt(t),t};Qo.bytes=0;function br(e,t){let r=new z,n=me.LENGTH,o=-1,s=t?.lengthDecoder??Qo,i=t?.maxLengthLength??8,a=t?.maxDataLength??4194304;function*c(){for(;r.byteLength>0;){if(n===me.LENGTH)try{if(o=s(r),o<0)throw new on("Invalid message length");if(o>a)throw new qe("Message length too long");let h=s.bytes;r.consume(h),t?.onLength!=null&&t.onLength(o),n=me.DATA}catch(h){if(h instanceof RangeError){if(r.byteLength>i)throw new sn("Message length length too long");break}throw h}if(n===me.DATA){if(r.byteLength<o)break;let h=r.sublist(0,o);r.consume(o),t?.onData!=null&&t.onData(h),yield h,n=me.LENGTH}}}return an(e)?async function*(){for await(let h of e)r.append(h),yield*c();if(r.byteLength>0)throw new gr("Unexpected end of input")}():function*(){for(let h of e)r.append(h),yield*c();if(r.byteLength>0)throw new gr("Unexpected end of input")}()}br.fromReader=(e,t)=>{let r=1,n=async function*(){for(;;)try{let{done:s,value:i}=await e.next(r);if(s===!0)return;i!=null&&(yield i)}catch(s){if(s.code==="ERR_UNDER_READ")return{done:!0,value:null};throw s}finally{r=1}}();return br(n,{...t??{},onLength:s=>{r=s}})};function Tt(){let e={};return e.promise=new Promise((t,r)=>{e.resolve=t,e.reject=r}),e}var un=class extends Error{type;code;constructor(t,r,n){super(t??"The operation was aborted"),this.type="aborted",this.name=n??"AbortError",this.code=r??"ABORT_ERR"}};async function wr(e,t,r){if(t==null)return e;if(t.aborted)return e.catch(()=>{}),Promise.reject(new un(r?.errorMessage,r?.errorCode,r?.errorName));let n,o=new un(r?.errorMessage,r?.errorCode,r?.errorName);try{return await Promise.race([e,new Promise((s,i)=>{n=()=>{i(o)},t.addEventListener("abort",n)})])}finally{n!=null&&t.removeEventListener("abort",n)}}var ts=class{readNext;haveNext;ended;nextResult;error;constructor(){this.ended=!1,this.readNext=Tt(),this.haveNext=Tt()}[Symbol.asyncIterator](){return this}async next(){if(this.nextResult==null&&await this.haveNext.promise,this.nextResult==null)throw new Error("HaveNext promise resolved but nextResult was undefined");let t=this.nextResult;return this.nextResult=void 0,this.readNext.resolve(),this.readNext=Tt(),t}async throw(t){return this.ended=!0,this.error=t,t!=null&&(this.haveNext.promise.catch(()=>{}),this.haveNext.reject(t)),{done:!0,value:void 0}}async return(){let t={done:!0,value:void 0};return this.ended=!0,this.nextResult=t,this.haveNext.resolve(),t}async push(t,r){await this._push(t,r)}async end(t,r){t!=null?await this.throw(t):await this._push(void 0,r)}async _push(t,r){if(t!=null&&this.ended)throw this.error??new Error("Cannot push value onto an ended pushable");for(;this.nextResult!=null;)await this.readNext.promise;t!=null?this.nextResult={done:!1,value:t}:(this.ended=!0,this.nextResult={done:!0,value:void 0}),this.haveNext.resolve(),this.haveNext=Tt(),await wr(this.readNext.promise,r?.signal,r)}};function hn(){return new ts}var ln=class extends Error{name="UnexpectedEOFError";code="ERR_UNEXPECTED_EOF"};function xa(e,t){let r=hn();e.sink(r).catch(async i=>{await r.end(i)}),e.sink=async i=>{for await(let a of i)await r.push(a);await r.end()};let n=e.source;e.source[Symbol.iterator]!=null?n=e.source[Symbol.iterator]():e.source[Symbol.asyncIterator]!=null&&(n=e.source[Symbol.asyncIterator]());let o=new z;return{read:async i=>{if(i?.signal?.throwIfAborted(),i?.bytes==null){let{done:c,value:h}=await wr(n.next(),i?.signal);return c===!0?null:h}for(;o.byteLength<i.bytes;){let{value:c,done:h}=await wr(n.next(),i?.signal);if(h===!0)throw new ln("unexpected end of input");o.append(c)}let a=o.sublist(0,i.bytes);return o.consume(i.bytes),a},write:async(i,a)=>{a?.signal?.throwIfAborted(),i instanceof Uint8Array?await r.push(i,a):await r.push(i.subarray(),a)},unwrap:()=>{if(o.byteLength>0){let i=e.source;e.source=async function*(){t?.yieldBytes===!1?yield o:yield*o,yield*i}()}return e}}}var dn=class extends Error{name="InvalidMessageLengthError";code="ERR_INVALID_MSG_LENGTH"},pn=class extends Error{name="InvalidDataLengthError";code="ERR_MSG_DATA_TOO_LONG"},yn=class extends Error{name="InvalidDataLengthLengthError";code="ERR_MSG_LENGTH_TOO_LONG"};function es(e,t={}){let r=xa(e,t);t.maxDataLength!=null&&t.maxLengthLength==null&&(t.maxLengthLength=wt(t.maxDataLength));let n=t?.lengthDecoder??Zr,o=t?.lengthEncoder??jr;return{read:async i=>{let a=-1,c=new z;for(;;){c.append(await r.read({...i,bytes:1}));try{a=n(c)}catch(h){if(h instanceof RangeError)continue;throw h}if(a<0)throw new dn("Invalid message length");if(t?.maxLengthLength!=null&&c.byteLength>t.maxLengthLength)throw new yn("message length length too long");if(a>-1)break}if(t?.maxDataLength!=null&&a>t.maxDataLength)throw new pn("message length too long");return r.read({...i,bytes:a})},write:async(i,a)=>{await r.write(new z(o(i.byteLength),i),a)},writeV:async(i,a)=>{let c=new z(...i.flatMap(h=>[o(h.byteLength),h]));await r.write(c,a)},unwrap:()=>r.unwrap()}}function rs(){let e=Tt(),t=!1;return{sink:async r=>{if(t)throw new Error("already piped");t=!0,e.resolve(r)},source:async function*(){yield*await e.promise}()}}function ga(){let e=rs(),t=rs();return[{source:e.source,sink:t.sink},{source:t.source,sink:e.sink}]}var mn=class{buffer;mask;top;btm;next;constructor(t){if(!(t>0)||(t-1&t)!==0)throw new Error("Max size for a FixedFIFO should be a power of two");this.buffer=new Array(t),this.mask=t-1,this.top=0,this.btm=0,this.next=null}push(t){return this.buffer[this.top]!==void 0?!1:(this.buffer[this.top]=t,this.top=this.top+1&this.mask,!0)}shift(){let t=this.buffer[this.btm];if(t!==void 0)return this.buffer[this.btm]=void 0,this.btm=this.btm+1&this.mask,t}isEmpty(){return this.buffer[this.btm]===void 0}},Ve=class{size;hwm;head;tail;constructor(t={}){this.hwm=t.splitLimit??16,this.head=new mn(this.hwm),this.tail=this.head,this.size=0}calculateSize(t){return t?.byteLength!=null?t.byteLength:1}push(t){if(t?.value!=null&&(this.size+=this.calculateSize(t.value)),!this.head.push(t)){let r=this.head;this.head=r.next=new mn(2*this.head.buffer.length),this.head.push(t)}}shift(){let t=this.tail.shift();if(t===void 0&&this.tail.next!=null){let r=this.tail.next;this.tail.next=null,this.tail=r,t=this.tail.shift()}return t?.value!=null&&(this.size-=this.calculateSize(t.value)),t}isEmpty(){return this.head.isEmpty()}};var ns=class extends Error{type;code;constructor(t,r){super(t??"The operation was aborted"),this.type="aborted",this.code=r??"ABORT_ERR"}};function ba(e={}){return Mu(r=>{let n=r.shift();if(n==null)return{done:!0};if(n.error!=null)throw n.error;return{done:n.done===!0,value:n.value}},e)}function Mu(e,t){t=t??{};let r=t.onEnd,n=new Ve,o,s,i,a=Tt(),c=async()=>{try{return n.isEmpty()?i?{done:!0}:await new Promise((l,E)=>{s=I=>{s=null,n.push(I);try{l(e(n))}catch(_){E(_)}return o}}):e(n)}finally{n.isEmpty()&&queueMicrotask(()=>{a.resolve(),a=Tt()})}},h=l=>s!=null?s(l):(n.push(l),o),f=l=>(n=new Ve,s!=null?s({error:l}):(n.push({error:l}),o)),u=l=>{if(i)return o;if(t?.objectMode!==!0&&l?.byteLength==null)throw new Error("objectMode was not true but tried to push non-Uint8Array value");return h({done:!1,value:l})},m=l=>i?o:(i=!0,l!=null?f(l):h({done:!0})),y=()=>(n=new Ve,m(),{done:!0}),w=l=>(m(l),{done:!0});if(o={[Symbol.asyncIterator](){return this},next:c,return:y,throw:w,push:u,end:m,get readableLength(){return n.size},onEmpty:async l=>{let E=l?.signal;if(E?.throwIfAborted(),n.isEmpty())return;let I,_;E!=null&&(I=new Promise((P,S)=>{_=()=>{S(new ns)},E.addEventListener("abort",_)}));try{await Promise.race([a.promise,I])}finally{_!=null&&E!=null&&E?.removeEventListener("abort",_)}}},r==null)return o;let x=o;return o={[Symbol.asyncIterator](){return this},next(){return x.next()},throw(l){return x.throw(l),r!=null&&(r(l),r=void 0),{done:!0}},return(){return x.return(),r!=null&&(r(),r=void 0),{done:!0}},push:u,end(l){return x.end(l),r!=null&&(r(l),r=void 0),o},get readableLength(){return x.readableLength},onEmpty:l=>x.onEmpty(l)},o}function Hu(e){return e[Symbol.asyncIterator]!=null}async function Ou(e,t,r){try{await Promise.all(e.map(async n=>{for await(let o of n)await t.push(o,{signal:r}),r.throwIfAborted()})),await t.end(void 0,{signal:r})}catch(n){await t.end(n,{signal:r}).catch(()=>{})}}async function*qu(e){let t=new AbortController,r=hn();Ou(e,r,t.signal).catch(()=>{});try{yield*r}finally{t.abort()}}function*Vu(e){for(let t of e)yield*t}function zu(...e){let t=[];for(let r of e)Hu(r)||t.push(r);return t.length===e.length?Vu(t):qu(e)}var wa=zu;function Ea(e,...t){if(e==null)throw new Error("Empty pipeline");if(os(e)){let n=e;e=()=>n.source}else if(Aa(e)||Sa(e)){let n=e;e=()=>n}let r=[e,...t];if(r.length>1&&os(r[r.length-1])&&(r[r.length-1]=r[r.length-1].sink),r.length>2)for(let n=1;n<r.length-1;n++)os(r[n])&&(r[n]=Fu(r[n]));return Gu(...r)}var Gu=(...e)=>{let t;for(;e.length>0;)t=e.shift()(t);return t},Sa=e=>e?.[Symbol.asyncIterator]!=null,Aa=e=>e?.[Symbol.iterator]!=null,os=e=>e==null?!1:e.sink!=null&&e.source!=null,Fu=e=>t=>{let r=e.sink(t);if(r?.then!=null){let n=ba({objectMode:!0});r.then(()=>{n.end()},i=>{n.end(i)});let o,s=e.source;if(Sa(s))o=async function*(){yield*s,n.end()};else if(Aa(s))o=function*(){yield*s,n.end()};else throw new Error("Unknown duplex source type - must be Iterable or AsyncIterable");return wa(n,o())}return e.source};var ze=!!globalThis.process?.env?.DUMP_SESSION_KEYS;function Ba(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function xn(e){if(typeof e!="boolean")throw new Error(`boolean expected, not ${e}`)}function gn(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function lt(e,...t){if(!Ba(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function ss(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function _a(e,t){lt(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}function Gt(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function Ft(...e){for(let t=0;t<e.length;t++)e[t].fill(0)}function ju(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}var Zu=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Yu(e){if(typeof e!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(e))}function bn(e){if(typeof e=="string")e=Yu(e);else if(Ba(e))e=wn(e);else throw new Error("Uint8Array expected, got "+typeof e);return e}function Ia(e,t){if(t==null||typeof t!="object")throw new Error("options must be defined");return Object.assign(e,t)}function La(e,t){if(e.length!==t.length)return!1;let r=0;for(let n=0;n<e.length;n++)r|=e[n]^t[n];return r===0}var is=(e,t)=>{function r(n,...o){if(lt(n),!Zu)throw new Error("Non little-endian hardware is not yet supported");if(e.nonceLength!==void 0){let f=o[0];if(!f)throw new Error("nonce / iv required");e.varSizeNonce?lt(f):lt(f,e.nonceLength)}let s=e.tagLength;s&&o[1]!==void 0&&lt(o[1]);let i=t(n,...o),a=(f,u)=>{if(u!==void 0){if(f!==2)throw new Error("cipher output not supported");lt(u)}},c=!1;return{encrypt(f,u){if(c)throw new Error("cannot encrypt() twice with same key + nonce");return c=!0,lt(f),a(i.encrypt.length,u),i.encrypt(f,u)},decrypt(f,u){if(lt(f),s&&f.length<s)throw new Error("invalid ciphertext length: smaller than tagLength="+s);return a(i.decrypt.length,u),i.decrypt(f,u)}}}return Object.assign(r,e),r};function as(e,t,r=!0){if(t===void 0)return new Uint8Array(e);if(t.length!==e)throw new Error("invalid output length, expected "+e+", got: "+t.length);if(r&&!Xu(t))throw new Error("invalid output, must be aligned");return t}function va(e,t,r,n){if(typeof e.setBigUint64=="function")return e.setBigUint64(t,r,n);let o=BigInt(32),s=BigInt(4294967295),i=Number(r>>o&s),a=Number(r&s),c=n?4:0,h=n?0:4;e.setUint32(t+c,i,n),e.setUint32(t+h,a,n)}function Ta(e,t,r){xn(r);let n=new Uint8Array(16),o=ju(n);return va(o,0,BigInt(t),r),va(o,8,BigInt(e),r),n}function Xu(e){return e.byteOffset%4===0}function wn(e){return Uint8Array.from(e)}var Ca=e=>Uint8Array.from(e.split("").map(t=>t.charCodeAt(0))),Wu=Ca("expand 16-byte k"),$u=Ca("expand 32-byte k"),Ju=Gt(Wu),Qu=Gt($u);function k(e,t){return e<<t|e>>>32-t}function cs(e){return e.byteOffset%4===0}var En=64,th=16,Ua=2**32-1,Ka=new Uint32Array;function eh(e,t,r,n,o,s,i,a){let c=o.length,h=new Uint8Array(En),f=Gt(h),u=cs(o)&&cs(s),m=u?Gt(o):Ka,y=u?Gt(s):Ka;for(let w=0;w<c;i++){if(e(t,r,n,f,i,a),i>=Ua)throw new Error("arx: counter overflow");let x=Math.min(En,c-w);if(u&&x===En){let l=w/4;if(w%4!==0)throw new Error("arx: invalid block position");for(let E=0,I;E<th;E++)I=l+E,y[I]=m[I]^f[E];w+=En;continue}for(let l=0,E;l<x;l++)E=w+l,s[E]=o[E]^h[l];w+=x}}function fs(e,t){let{allowShortKeys:r,extendNonceFn:n,counterLength:o,counterRight:s,rounds:i}=Ia({allowShortKeys:!1,counterLength:8,counterRight:!1,rounds:20},t);if(typeof e!="function")throw new Error("core must be a function");return gn(o),gn(i),xn(s),xn(r),(a,c,h,f,u=0)=>{lt(a),lt(c),lt(h);let m=h.length;if(f===void 0&&(f=new Uint8Array(m)),lt(f),gn(u),u<0||u>=Ua)throw new Error("arx: counter overflow");if(f.length<m)throw new Error(`arx: output (${f.length}) is shorter than data (${m})`);let y=[],w=a.length,x,l;if(w===32)y.push(x=wn(a)),l=Qu;else if(w===16&&r)x=new Uint8Array(32),x.set(a),x.set(a,16),l=Ju,y.push(x);else throw new Error(`arx: invalid 32-byte key, got length=${w}`);cs(c)||y.push(c=wn(c));let E=Gt(x);if(n){if(c.length!==24)throw new Error("arx: extended nonce must be 24 bytes");n(l,E,Gt(c.subarray(0,16)),E),c=c.subarray(16)}let I=16-o;if(I!==c.length)throw new Error(`arx: nonce must be ${I} or 16 bytes`);if(I!==12){let P=new Uint8Array(12);P.set(c,s?0:12-c.length),c=P,y.push(c)}let _=Gt(c);return eh(e,l,E,_,h,f,u,i),Ft(...y),f}}var it=(e,t)=>e[t++]&255|(e[t++]&255)<<8,us=class{constructor(t){this.blockLen=16,this.outputLen=16,this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.pos=0,this.finished=!1,t=bn(t),lt(t,32);let r=it(t,0),n=it(t,2),o=it(t,4),s=it(t,6),i=it(t,8),a=it(t,10),c=it(t,12),h=it(t,14);this.r[0]=r&8191,this.r[1]=(r>>>13|n<<3)&8191,this.r[2]=(n>>>10|o<<6)&7939,this.r[3]=(o>>>7|s<<9)&8191,this.r[4]=(s>>>4|i<<12)&255,this.r[5]=i>>>1&8190,this.r[6]=(i>>>14|a<<2)&8191,this.r[7]=(a>>>11|c<<5)&8065,this.r[8]=(c>>>8|h<<8)&8191,this.r[9]=h>>>5&127;for(let f=0;f<8;f++)this.pad[f]=it(t,16+2*f)}process(t,r,n=!1){let o=n?0:2048,{h:s,r:i}=this,a=i[0],c=i[1],h=i[2],f=i[3],u=i[4],m=i[5],y=i[6],w=i[7],x=i[8],l=i[9],E=it(t,r+0),I=it(t,r+2),_=it(t,r+4),P=it(t,r+6),S=it(t,r+8),D=it(t,r+10),K=it(t,r+12),T=it(t,r+14),d=s[0]+(E&8191),g=s[1]+((E>>>13|I<<3)&8191),p=s[2]+((I>>>10|_<<6)&8191),b=s[3]+((_>>>7|P<<9)&8191),A=s[4]+((P>>>4|S<<12)&8191),B=s[5]+(S>>>1&8191),v=s[6]+((S>>>14|D<<2)&8191),L=s[7]+((D>>>11|K<<5)&8191),C=s[8]+((K>>>8|T<<8)&8191),R=s[9]+(T>>>5|o),U=0,M=U+d*a+g*(5*l)+p*(5*x)+b*(5*w)+A*(5*y);U=M>>>13,M&=8191,M+=B*(5*m)+v*(5*u)+L*(5*f)+C*(5*h)+R*(5*c),U+=M>>>13,M&=8191;let N=U+d*c+g*a+p*(5*l)+b*(5*x)+A*(5*w);U=N>>>13,N&=8191,N+=B*(5*y)+v*(5*m)+L*(5*u)+C*(5*f)+R*(5*h),U+=N>>>13,N&=8191;let H=U+d*h+g*c+p*a+b*(5*l)+A*(5*x);U=H>>>13,H&=8191,H+=B*(5*w)+v*(5*y)+L*(5*m)+C*(5*u)+R*(5*f),U+=H>>>13,H&=8191;let q=U+d*f+g*h+p*c+b*a+A*(5*l);U=q>>>13,q&=8191,q+=B*(5*x)+v*(5*w)+L*(5*y)+C*(5*m)+R*(5*u),U+=q>>>13,q&=8191;let j=U+d*u+g*f+p*h+b*c+A*a;U=j>>>13,j&=8191,j+=B*(5*l)+v*(5*x)+L*(5*w)+C*(5*y)+R*(5*m),U+=j>>>13,j&=8191;let O=U+d*m+g*u+p*f+b*h+A*c;U=O>>>13,O&=8191,O+=B*a+v*(5*l)+L*(5*x)+C*(5*w)+R*(5*y),U+=O>>>13,O&=8191;let W=U+d*y+g*m+p*u+b*f+A*h;U=W>>>13,W&=8191,W+=B*c+v*a+L*(5*l)+C*(5*x)+R*(5*w),U+=W>>>13,W&=8191;let mt=U+d*w+g*y+p*m+b*u+A*f;U=mt>>>13,mt&=8191,mt+=B*h+v*c+L*a+C*(5*l)+R*(5*x),U+=mt>>>13,mt&=8191;let at=U+d*x+g*w+p*y+b*m+A*u;U=at>>>13,at&=8191,at+=B*f+v*h+L*c+C*a+R*(5*l),U+=at>>>13,at&=8191;let ot=U+d*l+g*x+p*w+b*y+A*m;U=ot>>>13,ot&=8191,ot+=B*u+v*f+L*h+C*c+R*a,U+=ot>>>13,ot&=8191,U=(U<<2)+U|0,U=U+M|0,M=U&8191,U=U>>>13,N+=U,s[0]=M,s[1]=N,s[2]=H,s[3]=q,s[4]=j,s[5]=O,s[6]=W,s[7]=mt,s[8]=at,s[9]=ot}finalize(){let{h:t,pad:r}=this,n=new Uint16Array(10),o=t[1]>>>13;t[1]&=8191;for(let a=2;a<10;a++)t[a]+=o,o=t[a]>>>13,t[a]&=8191;t[0]+=o*5,o=t[0]>>>13,t[0]&=8191,t[1]+=o,o=t[1]>>>13,t[1]&=8191,t[2]+=o,n[0]=t[0]+5,o=n[0]>>>13,n[0]&=8191;for(let a=1;a<10;a++)n[a]=t[a]+o,o=n[a]>>>13,n[a]&=8191;n[9]-=8192;let s=(o^1)-1;for(let a=0;a<10;a++)n[a]&=s;s=~s;for(let a=0;a<10;a++)t[a]=t[a]&s|n[a];t[0]=(t[0]|t[1]<<13)&65535,t[1]=(t[1]>>>3|t[2]<<10)&65535,t[2]=(t[2]>>>6|t[3]<<7)&65535,t[3]=(t[3]>>>9|t[4]<<4)&65535,t[4]=(t[4]>>>12|t[5]<<1|t[6]<<14)&65535,t[5]=(t[6]>>>2|t[7]<<11)&65535,t[6]=(t[7]>>>5|t[8]<<8)&65535,t[7]=(t[8]>>>8|t[9]<<5)&65535;let i=t[0]+r[0];t[0]=i&65535;for(let a=1;a<8;a++)i=(t[a]+r[a]|0)+(i>>>16)|0,t[a]=i&65535;Ft(n)}update(t){ss(this),t=bn(t),lt(t);let{buffer:r,blockLen:n}=this,o=t.length;for(let s=0;s<o;){let i=Math.min(n-this.pos,o-s);if(i===n){for(;n<=o-s;s+=n)this.process(t,s);continue}r.set(t.subarray(s,s+i),this.pos),this.pos+=i,s+=i,this.pos===n&&(this.process(r,0,!1),this.pos=0)}return this}destroy(){Ft(this.h,this.r,this.buffer,this.pad)}digestInto(t){ss(this),_a(t,this),this.finished=!0;let{buffer:r,h:n}=this,{pos:o}=this;if(o){for(r[o++]=1;o<16;o++)r[o]=0;this.process(r,0,!0)}this.finalize();let s=0;for(let i=0;i<8;i++)t[s++]=n[i]>>>0,t[s++]=n[i]>>>8;return t}digest(){let{buffer:t,outputLen:r}=this;this.digestInto(t);let n=t.slice(0,r);return this.destroy(),n}};function rh(e){let t=(n,o)=>e(o).update(bn(n)).digest(),r=e(new Uint8Array(32));return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}var Da=rh(e=>new us(e));function Na(e,t,r,n,o,s=20){let i=e[0],a=e[1],c=e[2],h=e[3],f=t[0],u=t[1],m=t[2],y=t[3],w=t[4],x=t[5],l=t[6],E=t[7],I=o,_=r[0],P=r[1],S=r[2],D=i,K=a,T=c,d=h,g=f,p=u,b=m,A=y,B=w,v=x,L=l,C=E,R=I,U=_,M=P,N=S;for(let q=0;q<s;q+=2)D=D+g|0,R=k(R^D,16),B=B+R|0,g=k(g^B,12),D=D+g|0,R=k(R^D,8),B=B+R|0,g=k(g^B,7),K=K+p|0,U=k(U^K,16),v=v+U|0,p=k(p^v,12),K=K+p|0,U=k(U^K,8),v=v+U|0,p=k(p^v,7),T=T+b|0,M=k(M^T,16),L=L+M|0,b=k(b^L,12),T=T+b|0,M=k(M^T,8),L=L+M|0,b=k(b^L,7),d=d+A|0,N=k(N^d,16),C=C+N|0,A=k(A^C,12),d=d+A|0,N=k(N^d,8),C=C+N|0,A=k(A^C,7),D=D+p|0,N=k(N^D,16),L=L+N|0,p=k(p^L,12),D=D+p|0,N=k(N^D,8),L=L+N|0,p=k(p^L,7),K=K+b|0,R=k(R^K,16),C=C+R|0,b=k(b^C,12),K=K+b|0,R=k(R^K,8),C=C+R|0,b=k(b^C,7),T=T+A|0,U=k(U^T,16),B=B+U|0,A=k(A^B,12),T=T+A|0,U=k(U^T,8),B=B+U|0,A=k(A^B,7),d=d+g|0,M=k(M^d,16),v=v+M|0,g=k(g^v,12),d=d+g|0,M=k(M^d,8),v=v+M|0,g=k(g^v,7);let H=0;n[H++]=i+D|0,n[H++]=a+K|0,n[H++]=c+T|0,n[H++]=h+d|0,n[H++]=f+g|0,n[H++]=u+p|0,n[H++]=m+b|0,n[H++]=y+A|0,n[H++]=w+B|0,n[H++]=x+v|0,n[H++]=l+L|0,n[H++]=E+C|0,n[H++]=I+R|0,n[H++]=_+U|0,n[H++]=P+M|0,n[H++]=S+N|0}function nh(e,t,r,n){let o=e[0],s=e[1],i=e[2],a=e[3],c=t[0],h=t[1],f=t[2],u=t[3],m=t[4],y=t[5],w=t[6],x=t[7],l=r[0],E=r[1],I=r[2],_=r[3];for(let S=0;S<20;S+=2)o=o+c|0,l=k(l^o,16),m=m+l|0,c=k(c^m,12),o=o+c|0,l=k(l^o,8),m=m+l|0,c=k(c^m,7),s=s+h|0,E=k(E^s,16),y=y+E|0,h=k(h^y,12),s=s+h|0,E=k(E^s,8),y=y+E|0,h=k(h^y,7),i=i+f|0,I=k(I^i,16),w=w+I|0,f=k(f^w,12),i=i+f|0,I=k(I^i,8),w=w+I|0,f=k(f^w,7),a=a+u|0,_=k(_^a,16),x=x+_|0,u=k(u^x,12),a=a+u|0,_=k(_^a,8),x=x+_|0,u=k(u^x,7),o=o+h|0,_=k(_^o,16),w=w+_|0,h=k(h^w,12),o=o+h|0,_=k(_^o,8),w=w+_|0,h=k(h^w,7),s=s+f|0,l=k(l^s,16),x=x+l|0,f=k(f^x,12),s=s+f|0,l=k(l^s,8),x=x+l|0,f=k(f^x,7),i=i+u|0,E=k(E^i,16),m=m+E|0,u=k(u^m,12),i=i+u|0,E=k(E^i,8),m=m+E|0,u=k(u^m,7),a=a+c|0,I=k(I^a,16),y=y+I|0,c=k(c^y,12),a=a+c|0,I=k(I^a,8),y=y+I|0,c=k(c^y,7);let P=0;n[P++]=o,n[P++]=s,n[P++]=i,n[P++]=a,n[P++]=l,n[P++]=E,n[P++]=I,n[P++]=_}var oh=fs(Na,{counterRight:!1,counterLength:4,allowShortKeys:!1}),sh=fs(Na,{counterRight:!1,counterLength:8,extendNonceFn:nh,allowShortKeys:!1});var ih=new Uint8Array(16),Ra=(e,t)=>{e.update(t);let r=t.length%16;r&&e.update(ih.subarray(r))},ah=new Uint8Array(32);function Pa(e,t,r,n,o){let s=e(t,r,ah),i=Da.create(s);o&&Ra(i,o),Ra(i,n);let a=Ta(n.length,o?o.length:0,!0);i.update(a);let c=i.digest();return Ft(s,a),c}var ka=e=>(t,r,n)=>({encrypt(s,i){let a=s.length;i=as(a+16,i,!1),i.set(s);let c=i.subarray(0,-16);e(t,r,c,c,1);let h=Pa(e,t,r,c,n);return i.set(h,a),Ft(h),i},decrypt(s,i){i=as(s.length-16,i,!1);let a=s.subarray(0,-16),c=s.subarray(-16),h=Pa(e,t,r,a,n);if(!La(c,h))throw new Error("invalid tag");return i.set(s.subarray(0,-16)),e(t,r,i,i,1),Ft(h),i}}),hs=is({blockSize:64,nonceLength:12,tagLength:16},ka(oh)),Rm=is({blockSize:64,nonceLength:24,tagLength:16},ka(sh));function Ha(e,t,r){return Qe(e),r===void 0&&(r=new Uint8Array(e.outputLen)),He(e,Xt(r),Xt(t))}var ls=Uint8Array.from([0]),Ma=Uint8Array.of();function Oa(e,t,r,n=32){Qe(e),oe(n);let o=e.outputLen;if(n>255*o)throw new Error("Length should be <= 255*HashLen");let s=Math.ceil(n/o);r===void 0&&(r=Ma);let i=new Uint8Array(s*o),a=He.create(e,t),c=a._cloneInto(),h=new Uint8Array(a.outputLen);for(let f=0;f<s;f++)ls[0]=f+1,c.update(f===0?Ma:h).update(r).update(ls).digestInto(h),i.set(h,o*f),a._cloneInto(c);return a.destroy(),c.destroy(),vt(h,ls),i.slice(0,n)}var Sn={hashSHA256(e){return ee(e.subarray())},getHKDF(e,t){let r=Ha(ee,t,e),o=Oa(ee,r,void 0,96),s=o.subarray(0,32),i=o.subarray(32,64),a=o.subarray(64,96);return[s,i,a]},generateX25519KeyPair(){let e=ar.utils.randomPrivateKey();return{publicKey:ar.getPublicKey(e),privateKey:e}},generateX25519KeyPairFromSeed(e){return{publicKey:ar.getPublicKey(e),privateKey:e}},generateX25519SharedKey(e,t){return ar.getSharedSecret(e.subarray(),t.subarray())},chaCha20Poly1305Encrypt(e,t,r,n){return hs(n,t,r).encrypt(e.subarray())},chaCha20Poly1305Decrypt(e,t,r,n,o){return hs(n,t,r).decrypt(e.subarray(),o)}};var qa=Sn;function Va(e){return{generateKeypair:e.generateX25519KeyPair,dh:(t,r)=>e.generateX25519SharedKey(t.privateKey,r).subarray(0,32),encrypt:e.chaCha20Poly1305Encrypt,decrypt:e.chaCha20Poly1305Decrypt,hash:e.hashSHA256,hkdf:e.getHKDF}}var Ge=e=>{let t=et(2);return t[0]=e>>8,t[1]=e,t};Ge.bytes=2;var Er=e=>{if(e.length<2)throw RangeError("Could not decode int16BE");if(e instanceof Uint8Array){let t=0;return t+=e[0]<<8,t+=e[1],t}return e.getUint16(0)};Er.bytes=2;function za(e){return{xxHandshakeSuccesses:e.registerCounter("libp2p_noise_xxhandshake_successes_total",{help:"Total count of noise xxHandshakes successes_"}),xxHandshakeErrors:e.registerCounter("libp2p_noise_xxhandshake_error_total",{help:"Total count of noise xxHandshakes errors"}),encryptedPackets:e.registerCounter("libp2p_noise_encrypted_packets_total",{help:"Total count of noise encrypted packets successfully"}),decryptedPackets:e.registerCounter("libp2p_noise_decrypted_packets_total",{help:"Total count of noise decrypted packets"}),decryptErrors:e.registerCounter("libp2p_noise_decrypt_errors_total",{help:"Total count of noise decrypt errors"})}}function ds(e,t){!t.enabled||!ze||(e?(t(`LOCAL_STATIC_PUBLIC_KEY ${V(e.publicKey,"hex")}`),t(`LOCAL_STATIC_PRIVATE_KEY ${V(e.privateKey,"hex")}`)):t("Missing local static keys."))}function ps(e,t){!t.enabled||!ze||(e?(t(`LOCAL_PUBLIC_EPHEMERAL_KEY ${V(e.publicKey,"hex")}`),t(`LOCAL_PRIVATE_EPHEMERAL_KEY ${V(e.privateKey,"hex")}`)):t("Missing local ephemeral keys."))}function Ga(e,t){!t.enabled||!ze||t(e?`REMOTE_STATIC_PUBLIC_KEY ${V(e.subarray(),"hex")}`:"Missing remote static public key.")}function ys(e,t){!t.enabled||!ze||t(e?`REMOTE_EPHEMERAL_PUBLIC_KEY ${V(e.subarray(),"hex")}`:"Missing remote ephemeral keys.")}function ms(e,t,r){!r.enabled||!ze||(r(`CIPHER_STATE_1 ${e.n.getUint64()} ${e.k&&V(e.k,"hex")}`),r(`CIPHER_STATE_2 ${t.n.getUint64()} ${t.k&&V(t.k,"hex")}`))}var Fe=class e extends Error{code;constructor(t="Invalid crypto exchange"){super(t),this.code=e.code}static code="ERR_INVALID_CRYPTO_EXCHANGE"};var ch=0,fh=4294967295,uh="Cipherstate has reached maximum n, a new handshake must be performed",An=class{n;bytes;view;constructor(t=ch){this.n=t,this.bytes=tt(12),this.view=new DataView(this.bytes.buffer,this.bytes.byteOffset,this.bytes.byteLength),this.view.setUint32(4,t,!0)}increment(){this.n++,this.view.setUint32(4,this.n,!0)}getBytes(){return this.bytes}getUint64(){return this.n}assertValue(){if(this.n>fh)throw new Error(uh)}};var xe=tt(0),je=class{k;n;crypto;constructor(t,r=void 0,n=0){this.crypto=t,this.k=r,this.n=new An(n)}hasKey(){return!!this.k}encryptWithAd(t,r){if(!this.hasKey())return r;this.n.assertValue();let n=this.crypto.encrypt(r,this.n.getBytes(),t,this.k);return this.n.increment(),n}decryptWithAd(t,r,n){if(!this.hasKey())return r;this.n.assertValue();let o=this.crypto.decrypt(r,this.n.getBytes(),t,this.k,n);return this.n.increment(),o}},xs=class{cs;ck;h;crypto;constructor(t,r){this.crypto=t;let n=Z(r,"utf-8");this.h=hh(t,n),this.ck=this.h,this.cs=new je(t)}mixKey(t){let[r,n]=this.crypto.hkdf(this.ck,t);this.ck=r,this.cs=new je(this.crypto,n)}mixHash(t){this.h=this.crypto.hash(new z(this.h,t))}encryptAndHash(t){let r=this.cs.encryptWithAd(this.h,t);return this.mixHash(r),r}decryptAndHash(t){let r=this.cs.decryptWithAd(this.h,t);return this.mixHash(t),r}split(){let[t,r]=this.crypto.hkdf(this.ck,xe);return[new je(this.crypto,t),new je(this.crypto,r)]}},gs=class{ss;s;e;rs;re;initiator;crypto;constructor(t){let{crypto:r,protocolName:n,prologue:o,initiator:s,s:i,e:a,rs:c,re:h}=t;this.crypto=r,this.ss=new xs(r,n),this.ss.mixHash(o),this.initiator=s,this.s=i,this.e=a,this.rs=c,this.re=h}writeE(){if(this.e)throw new Error("ephemeral keypair is already set");let t=this.crypto.generateKeypair();return this.ss.mixHash(t.publicKey),this.e=t,t.publicKey}writeS(){if(!this.s)throw new Error("static keypair is not set");return this.ss.encryptAndHash(this.s.publicKey)}writeEE(){if(!this.e)throw new Error("ephemeral keypair is not set");if(!this.re)throw new Error("remote ephemeral public key is not set");this.ss.mixKey(this.crypto.dh(this.e,this.re))}writeES(){if(this.initiator){if(!this.e)throw new Error("ephemeral keypair is not set");if(!this.rs)throw new Error("remote static public key is not set");this.ss.mixKey(this.crypto.dh(this.e,this.rs))}else{if(!this.s)throw new Error("static keypair is not set");if(!this.re)throw new Error("remote ephemeral public key is not set");this.ss.mixKey(this.crypto.dh(this.s,this.re))}}writeSE(){if(this.initiator){if(!this.s)throw new Error("static keypair is not set");if(!this.re)throw new Error("remote ephemeral public key is not set");this.ss.mixKey(this.crypto.dh(this.s,this.re))}else{if(!this.e)throw new Error("ephemeral keypair is not set");if(!this.rs)throw new Error("remote static public key is not set");this.ss.mixKey(this.crypto.dh(this.e,this.rs))}}readE(t,r=0){if(this.re)throw new Error("remote ephemeral public key is already set");if(t.byteLength<r+32)throw new Error("message is not long enough");this.re=t.sublist(r,r+32),this.ss.mixHash(this.re)}readS(t,r=0){if(this.rs)throw new Error("remote static public key is already set");let n=32+(this.ss.cs.hasKey()?16:0);if(t.byteLength<r+n)throw new Error("message is not long enough");let o=t.sublist(r,r+n);return this.rs=this.ss.decryptAndHash(o),n}readEE(){this.writeEE()}readES(){this.writeES()}readSE(){this.writeSE()}},Sr=class extends gs{writeMessageA(t){return new z(this.writeE(),this.ss.encryptAndHash(t))}writeMessageB(t){let r=this.writeE();this.writeEE();let n=this.writeS();return this.writeES(),new z(r,n,this.ss.encryptAndHash(t))}writeMessageC(t){let r=this.writeS();return this.writeSE(),new z(r,this.ss.encryptAndHash(t))}readMessageA(t){try{return this.readE(t),this.ss.decryptAndHash(t.sublist(32))}catch(r){throw new Fe(`handshake stage 0 validation fail: ${r.message}`)}}readMessageB(t){try{this.readE(t),this.readEE();let r=this.readS(t,32);return this.readES(),this.ss.decryptAndHash(t.sublist(32+r))}catch(r){throw new Fe(`handshake stage 1 validation fail: ${r.message}`)}}readMessageC(t){try{let r=this.readS(t);return this.readSE(),this.ss.decryptAndHash(t.sublist(r))}catch(r){throw new Fe(`handshake stage 2 validation fail: ${r.message}`)}}};function hh(e,t){if(t.length<=32){let r=tt(32);return r.set(t),r}else return e.hash(t)}var vn;(function(e){let t;e.codec=()=>(t==null&&(t=ye((r,n,o={})=>{if(o.lengthDelimited!==!1&&n.fork(),r.webtransportCerthashes!=null)for(let s of r.webtransportCerthashes)n.uint32(10),n.bytes(s);if(r.streamMuxers!=null)for(let s of r.streamMuxers)n.uint32(18),n.string(s);o.lengthDelimited!==!1&&n.ldelim()},(r,n,o={})=>{let s={webtransportCerthashes:[],streamMuxers:[]},i=n==null?r.len:r.pos+n;for(;r.pos<i;){let a=r.uint32();switch(a>>>3){case 1:{if(o.limits?.webtransportCerthashes!=null&&s.webtransportCerthashes.length===o.limits.webtransportCerthashes)throw new hr('Decode error - map field "webtransportCerthashes" had too many elements');s.webtransportCerthashes.push(r.bytes());break}case 2:{if(o.limits?.streamMuxers!=null&&s.streamMuxers.length===o.limits.streamMuxers)throw new hr('Decode error - map field "streamMuxers" had too many elements');s.streamMuxers.push(r.string());break}default:{r.skipType(a&7);break}}}return s})),t),e.encode=r=>pe(r,e.codec()),e.decode=(r,n)=>le(r,e.codec(),n)})(vn||(vn={}));var Ar;(function(e){let t;e.codec=()=>(t==null&&(t=ye((r,n,o={})=>{o.lengthDelimited!==!1&&n.fork(),r.identityKey!=null&&r.identityKey.byteLength>0&&(n.uint32(10),n.bytes(r.identityKey)),r.identitySig!=null&&r.identitySig.byteLength>0&&(n.uint32(18),n.bytes(r.identitySig)),r.extensions!=null&&(n.uint32(34),vn.codec().encode(r.extensions,n)),o.lengthDelimited!==!1&&n.ldelim()},(r,n,o={})=>{let s={identityKey:tt(0),identitySig:tt(0)},i=n==null?r.len:r.pos+n;for(;r.pos<i;){let a=r.uint32();switch(a>>>3){case 1:{s.identityKey=r.bytes();break}case 2:{s.identitySig=r.bytes();break}case 4:{s.extensions=vn.codec().decode(r,r.uint32(),{limits:o.limits?.extensions});break}default:{r.skipType(a&7);break}}}return s})),t),e.encode=r=>pe(r,e.codec()),e.decode=(r,n)=>le(r,e.codec(),n)})(Ar||(Ar={}));async function bs(e,t,r){let n=await e.sign(Fa(t));return Ar.encode({identityKey:Yt(e.publicKey),identitySig:n,extensions:r})}async function ws(e,t,r){try{let n=Ar.decode(e),o=mr(n.identityKey);if(r?.equals(o)===!1)throw new Error(`Payload identity key ${o} does not match expected remote identity key ${r}`);if(!t)throw new Error("Remote static does not exist");let s=Fa(t);if(!await o.verify(s,n.identitySig))throw new Error("Invalid payload signature");return n}catch(n){throw new Br(n.message)}}function Fa(e){let t=Z("noise-libp2p-static-key:");return e instanceof Uint8Array?re([t,e],t.length+e.length):(e.prepend(t),e)}async function ja(e,t){let{log:r,connection:n,crypto:o,privateKey:s,prologue:i,s:a,remoteIdentityKey:c,extensions:h}=e,f=await bs(s,a.publicKey,h),u=new Sr({crypto:o,protocolName:"Noise_XX_25519_ChaChaPoly_SHA256",initiator:!0,prologue:i,s:a});ds(u.s,r),r.trace("Stage 0 - Initiator starting to send first message."),await n.write(u.writeMessageA(xe),t),r.trace("Stage 0 - Initiator finished sending first message."),ps(u.e,r),r.trace("Stage 1 - Initiator waiting to receive first message from responder...");let m=u.readMessageB(await n.read(t));r.trace("Stage 1 - Initiator received the message."),ys(u.re,r),Ga(u.rs,r),r.trace("Initiator going to check remote's signature...");let y=await ws(m,u.rs,c);r.trace("All good with the signature!"),r.trace("Stage 2 - Initiator sending third handshake message."),await n.write(u.writeMessageC(f),t),r.trace("Stage 2 - Initiator sent message with signed payload.");let[w,x]=u.ss.split();return ms(w,x,r),{payload:y,encrypt:l=>w.encryptWithAd(xe,l),decrypt:(l,E)=>x.decryptWithAd(xe,l,E)}}async function Za(e,t){let{log:r,connection:n,crypto:o,privateKey:s,prologue:i,s:a,remoteIdentityKey:c,extensions:h}=e,f=await bs(s,a.publicKey,h),u=new Sr({crypto:o,protocolName:"Noise_XX_25519_ChaChaPoly_SHA256",initiator:!1,prologue:i,s:a});ds(u.s,r),r.trace("Stage 0 - Responder waiting to receive first message."),u.readMessageA(await n.read(t)),r.trace("Stage 0 - Responder received first message."),ys(u.re,r),r.trace("Stage 1 - Responder sending out first message with signed payload and static key."),await n.write(u.writeMessageB(f),t),r.trace("Stage 1 - Responder sent the second handshake message with signed payload."),ps(u.e,r),r.trace("Stage 2 - Responder waiting for third handshake message...");let m=u.readMessageC(await n.read(t));r.trace("Stage 2 - Responder received the message, finished handshake.");let y=await ws(m,u.rs,c),[w,x]=u.ss.split();return ms(w,x,r),{payload:y,encrypt:l=>x.encryptWithAd(xe,l),decrypt:(l,E)=>w.decryptWithAd(xe,l,E)}}var Xa=16;function Wa(e,t){return async function*(r){for await(let n of r)for(let o=0;o<n.length;o+=65519){let s=o+65519;s>n.length&&(s=n.length);let i;n instanceof Uint8Array?i=e.encrypt(n.subarray(o,s)):i=e.encrypt(n.sublist(o,s)),t?.encryptedPackets.increment(),yield new z(Ge(i.byteLength),i)}}}function $a(e,t){return async function*(r){for await(let n of r)for(let o=0;o<n.length;o+=65535){let s=o+65535;if(s>n.length&&(s=n.length),s-Xa<o)throw new Error("Invalid chunk");let i=n.sublist(o,s),a=n.subarray(o,s-Xa);try{let c=e.decrypt(i,a);t?.decryptedPackets.increment(),yield c}catch(c){throw t?.decryptErrors.increment(),c}}}}var Bn=class{protocol="/noise";crypto;prologue;staticKey;extensions;metrics;components;constructor(t,r={}){let{staticNoiseKey:n,extensions:o,crypto:s,prologueBytes:i}=r,{metrics:a}=t;this.components=t;let c=s??qa;this.crypto=Va(c),this.extensions={webtransportCerthashes:[],...o},this.metrics=a?za(a):void 0,n?this.staticKey=c.generateX25519KeyPairFromSeed(n):this.staticKey=c.generateX25519KeyPair(),this.prologue=i??tt(0)}[Symbol.toStringTag]="@chainsafe/libp2p-noise";[Es]=["@libp2p/connection-encryption","@chainsafe/libp2p-noise"];async secureOutbound(t,r){let n=es(t,{lengthEncoder:Ge,lengthDecoder:Er,maxDataLength:65535}),o=await this.performHandshakeInitiator(n,this.components.privateKey,r?.remotePeer?.publicKey,r),s=await this.createSecureConnection(n,o);t.source=s.source,t.sink=s.sink;let i=mr(o.payload.identityKey);return{conn:t,remoteExtensions:o.payload.extensions,remotePeer:Jo(i),streamMuxer:r?.skipStreamMuxerNegotiation===!0?void 0:this.getStreamMuxer(o.payload.extensions?.streamMuxers)}}getStreamMuxer(t){if(t==null||t.length===0)return;let r=this.components.upgrader.getStreamMuxers();if(r!=null)for(let n of t){let o=r.get(n);if(o!=null)return o}if(t.length)throw new _r("Early muxer negotiation was requested but the initiator and responder had no common muxers")}async secureInbound(t,r){let n=es(t,{lengthEncoder:Ge,lengthDecoder:Er,maxDataLength:65535}),o=await this.performHandshakeResponder(n,this.components.privateKey,r?.remotePeer?.publicKey,r),s=await this.createSecureConnection(n,o);t.source=s.source,t.sink=s.sink;let i=mr(o.payload.identityKey);return{conn:t,remoteExtensions:o.payload.extensions,remotePeer:Jo(i),streamMuxer:r?.skipStreamMuxerNegotiation===!0?void 0:this.getStreamMuxer(o.payload.extensions?.streamMuxers)}}async performHandshakeInitiator(t,r,n,o){let s,i=o?.skipStreamMuxerNegotiation===!0?[]:[...this.components.upgrader.getStreamMuxers().keys()];try{s=await ja({connection:t,privateKey:r,remoteIdentityKey:n,log:this.components.logger.forComponent("libp2p:noise:xxhandshake"),crypto:this.crypto,prologue:this.prologue,s:this.staticKey,extensions:{streamMuxers:i,webtransportCerthashes:[],...this.extensions}},o),this.metrics?.xxHandshakeSuccesses.increment()}catch(a){throw this.metrics?.xxHandshakeErrors.increment(),a}return s}async performHandshakeResponder(t,r,n,o){let s,i=o?.skipStreamMuxerNegotiation===!0?[]:[...this.components.upgrader.getStreamMuxers().keys()];try{s=await Za({connection:t,privateKey:r,remoteIdentityKey:n,log:this.components.logger.forComponent("libp2p:noise:xxhandshake"),crypto:this.crypto,prologue:this.prologue,s:this.staticKey,extensions:{streamMuxers:i,webtransportCerthashes:[],...this.extensions}},o),this.metrics?.xxHandshakeSuccesses.increment()}catch(a){throw this.metrics?.xxHandshakeErrors.increment(),a}return s}async createSecureConnection(t,r){let[n,o]=ga(),s=t.unwrap();return await Ea(n,Wa(r,this.metrics),s,i=>br(i,{lengthDecoder:Er}),$a(r,this.metrics),n),o}};function lh(e={}){return t=>new Bn(t,e)}return rc(dh);})();
/*! Bundled license information:

@noble/hashes/esm/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/esm/utils.js:
@noble/curves/esm/abstract/modular.js:
@noble/curves/esm/abstract/curve.js:
@noble/curves/esm/abstract/edwards.js:
@noble/curves/esm/abstract/montgomery.js:
@noble/curves/esm/ed25519.js:
@noble/curves/esm/abstract/weierstrass.js:
@noble/curves/esm/_shortw_utils.js:
@noble/curves/esm/secp256k1.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/ciphers/esm/utils.js:
  (*! noble-ciphers - MIT License (c) 2023 Paul Miller (paulmillr.com) *)
*/
return ChainsafeLibp2PNoise}));
//# sourceMappingURL=index.min.js.map
