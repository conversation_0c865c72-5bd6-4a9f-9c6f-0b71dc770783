"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.allocUnsafe = allocUnsafe;
/**
 * Where possible returns a Uint8Array of the requested size that references
 * uninitialized memory. Only use if you are certain you will immediately
 * overwrite every value in the returned `Uint8Array`.
 */
function allocUnsafe(size = 0) {
    const out = Buffer.allocUnsafe(size);
    return new Uint8Array(out.buffer, out.byteOffset, out.byteLength);
}
//# sourceMappingURL=alloc.node.js.map