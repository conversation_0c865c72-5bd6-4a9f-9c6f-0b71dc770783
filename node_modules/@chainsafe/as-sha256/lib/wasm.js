import { wasmCode } from "./wasmCode.js";
import { wasmSimdCode } from "./wasmSimdCode.js";
const importObj = {
    env: {
        // modified from https://github.com/AssemblyScript/assemblyscript/blob/v0.9.2/lib/loader/index.js#L70
        abort: (msg, file, line, col) => {
            throw Error(`abort: ${msg}:${file}:${line}:${col}`);
        },
    },
};
export function newInstance(useSimd) {
    const enableSimd = useSimd !== undefined ? useSimd : WebAssembly.validate(wasmSimdCode);
    return enableSimd
        ? new WebAssembly.Instance(new WebAssembly.Module(wasmSimdCode), importObj).exports
        : new WebAssembly.Instance(new WebAssembly.Module(wasmCode), importObj).exports;
}
//# sourceMappingURL=wasm.js.map