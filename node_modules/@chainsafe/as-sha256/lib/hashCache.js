"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setHash = exports.setHashObjectItems = exports.setHashObject = exports.getHashObject = exports.getHash = exports.cloneHashId = exports.freeHashId = exports.allocHashId = exports.newHashId = exports.incrementNext = exports.getCacheOffset = exports.getCache = exports.allocHashCache = exports.CACHE_BYTE_SIZE = exports.CACHE_HASH_SIZE = exports.HASH_SIZE = void 0;
exports.HASH_SIZE = 32;
exports.CACHE_HASH_SIZE = 32768;
exports.CACHE_BYTE_SIZE = exports.CACHE_HASH_SIZE * exports.HASH_SIZE;
function toHashId(cacheIndex, hashIndex) {
    return (cacheIndex << 16) | hashIndex;
}
function fromHashId(id) {
    return [id >> 16, id & 0xffff];
}
function getCacheIndex(id) {
    return id >> 16;
}
function getHashIndex(id) {
    return id & 0xffff;
}
const hashCaches = [];
function allocHashCache() {
    const cache = new Uint8Array(exports.CACHE_BYTE_SIZE);
    const used = new Set();
    const next = 0;
    const out = { cache, used, next };
    hashCaches.push(out);
    return out;
}
exports.allocHashCache = allocHashCache;
function getCache(id) {
    return hashCaches[getCacheIndex(id)];
}
exports.getCache = getCache;
function getCacheOffset(id) {
    return getHashIndex(id) * exports.HASH_SIZE;
}
exports.getCacheOffset = getCacheOffset;
function incrementNext(cache) {
    const out = cache.next;
    cache.used.add(out);
    // eslint-disable-next-line no-empty
    while (cache.used.has(++cache.next)) { }
    return out;
}
exports.incrementNext = incrementNext;
function newHashId(cacheIndex, cache) {
    const hashIndex = incrementNext(cache);
    return toHashId(cacheIndex, hashIndex);
}
exports.newHashId = newHashId;
function allocHashId() {
    const cachesLength = hashCaches.length;
    for (let i = 0; i < cachesLength; i++) {
        const cache = hashCaches[i];
        if (cache.next < exports.CACHE_HASH_SIZE) {
            return newHashId(i, cache);
        }
    }
    const cache = allocHashCache();
    return newHashId(cachesLength, cache);
}
exports.allocHashId = allocHashId;
function freeHashId(id) {
    const [cacheIndex, hashIndex] = fromHashId(id);
    hashCaches[cacheIndex].used.delete(hashIndex);
    if (hashCaches[cacheIndex].next > hashIndex) {
        hashCaches[cacheIndex].next = hashIndex;
    }
}
exports.freeHashId = freeHashId;
function cloneHashId(source, target) {
    const { cache: cacheSource } = getCache(source);
    let offsetSource = getCacheOffset(source);
    const { cache: cacheTarget } = getCache(target);
    let offsetTarget = getCacheOffset(target);
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
    cacheTarget[offsetTarget++] = cacheSource[offsetSource++];
}
exports.cloneHashId = cloneHashId;
function getHash(id) {
    const [cacheIndex, hashIndex] = fromHashId(id);
    const cache = hashCaches[cacheIndex];
    const offset = hashIndex * exports.HASH_SIZE;
    return cache.cache.subarray(offset, offset + exports.HASH_SIZE);
}
exports.getHash = getHash;
function getHashObject(id) {
    const { cache } = getCache(id);
    let offset = getCacheOffset(id);
    return {
        h0: cache[offset++] + (cache[offset++] << 8) + (cache[offset++] << 16) + (cache[offset++] << 24),
        h1: cache[offset++] + (cache[offset++] << 8) + (cache[offset++] << 16) + (cache[offset++] << 24),
        h2: cache[offset++] + (cache[offset++] << 8) + (cache[offset++] << 16) + (cache[offset++] << 24),
        h3: cache[offset++] + (cache[offset++] << 8) + (cache[offset++] << 16) + (cache[offset++] << 24),
        h4: cache[offset++] + (cache[offset++] << 8) + (cache[offset++] << 16) + (cache[offset++] << 24),
        h5: cache[offset++] + (cache[offset++] << 8) + (cache[offset++] << 16) + (cache[offset++] << 24),
        h6: cache[offset++] + (cache[offset++] << 8) + (cache[offset++] << 16) + (cache[offset++] << 24),
        h7: cache[offset++] + (cache[offset++] << 8) + (cache[offset++] << 16) + (cache[offset++] << 24),
    };
}
exports.getHashObject = getHashObject;
function setHashObject(id, obj) {
    const { cache } = getCache(id);
    let offset = getCacheOffset(id);
    cache[offset++] = obj.h0 & 0xff;
    cache[offset++] = (obj.h0 >> 8) & 0xff;
    cache[offset++] = (obj.h0 >> 16) & 0xff;
    cache[offset++] = (obj.h0 >> 24) & 0xff;
    cache[offset++] = obj.h1 & 0xff;
    cache[offset++] = (obj.h1 >> 8) & 0xff;
    cache[offset++] = (obj.h1 >> 16) & 0xff;
    cache[offset++] = (obj.h1 >> 24) & 0xff;
    cache[offset++] = obj.h2 & 0xff;
    cache[offset++] = (obj.h2 >> 8) & 0xff;
    cache[offset++] = (obj.h2 >> 16) & 0xff;
    cache[offset++] = (obj.h2 >> 24) & 0xff;
    cache[offset++] = obj.h3 & 0xff;
    cache[offset++] = (obj.h3 >> 8) & 0xff;
    cache[offset++] = (obj.h3 >> 16) & 0xff;
    cache[offset++] = (obj.h3 >> 24) & 0xff;
    cache[offset++] = obj.h4 & 0xff;
    cache[offset++] = (obj.h4 >> 8) & 0xff;
    cache[offset++] = (obj.h4 >> 16) & 0xff;
    cache[offset++] = (obj.h4 >> 24) & 0xff;
    cache[offset++] = obj.h5 & 0xff;
    cache[offset++] = (obj.h5 >> 8) & 0xff;
    cache[offset++] = (obj.h5 >> 16) & 0xff;
    cache[offset++] = (obj.h5 >> 24) & 0xff;
    cache[offset++] = obj.h6 & 0xff;
    cache[offset++] = (obj.h6 >> 8) & 0xff;
    cache[offset++] = (obj.h6 >> 16) & 0xff;
    cache[offset++] = (obj.h6 >> 24) & 0xff;
    cache[offset++] = obj.h7 & 0xff;
    cache[offset++] = (obj.h7 >> 8) & 0xff;
    cache[offset++] = (obj.h7 >> 16) & 0xff;
    cache[offset++] = (obj.h7 >> 24) & 0xff;
}
exports.setHashObject = setHashObject;
function setHashObjectItems(id, h0, h1, h2, h3, h4, h5, h6, h7) {
    const { cache } = getCache(id);
    let offset = getCacheOffset(id);
    cache[offset++] = h0 & 0xff;
    cache[offset++] = (h0 >> 8) & 0xff;
    cache[offset++] = (h0 >> 16) & 0xff;
    cache[offset++] = (h0 >> 24) & 0xff;
    cache[offset++] = h1 & 0xff;
    cache[offset++] = (h1 >> 8) & 0xff;
    cache[offset++] = (h1 >> 16) & 0xff;
    cache[offset++] = (h1 >> 24) & 0xff;
    cache[offset++] = h2 & 0xff;
    cache[offset++] = (h2 >> 8) & 0xff;
    cache[offset++] = (h2 >> 16) & 0xff;
    cache[offset++] = (h2 >> 24) & 0xff;
    cache[offset++] = h3 & 0xff;
    cache[offset++] = (h3 >> 8) & 0xff;
    cache[offset++] = (h3 >> 16) & 0xff;
    cache[offset++] = (h3 >> 24) & 0xff;
    cache[offset++] = h4 & 0xff;
    cache[offset++] = (h4 >> 8) & 0xff;
    cache[offset++] = (h4 >> 16) & 0xff;
    cache[offset++] = (h4 >> 24) & 0xff;
    cache[offset++] = h5 & 0xff;
    cache[offset++] = (h5 >> 8) & 0xff;
    cache[offset++] = (h5 >> 16) & 0xff;
    cache[offset++] = (h5 >> 24) & 0xff;
    cache[offset++] = h6 & 0xff;
    cache[offset++] = (h6 >> 8) & 0xff;
    cache[offset++] = (h6 >> 16) & 0xff;
    cache[offset++] = (h6 >> 24) & 0xff;
    cache[offset++] = h7 & 0xff;
    cache[offset++] = (h7 >> 8) & 0xff;
    cache[offset++] = (h7 >> 16) & 0xff;
    cache[offset++] = (h7 >> 24) & 0xff;
}
exports.setHashObjectItems = setHashObjectItems;
function setHash(id, hash) {
    const { cache } = getCache(id);
    const offset = getCacheOffset(id);
    cache.set(hash, offset);
}
exports.setHash = setHash;
//# sourceMappingURL=hashCache.js.map