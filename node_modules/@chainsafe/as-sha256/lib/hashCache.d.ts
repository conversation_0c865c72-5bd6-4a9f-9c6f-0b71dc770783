import { HashObject } from "./hashObject";
export declare const HASH_SIZE = 32;
export declare const CACHE_HASH_SIZE = 32768;
export declare const CACHE_BYTE_SIZE: number;
export declare type HashCache = {
    cache: Uint8Array;
    used: Set<number>;
    next: number;
};
/**
 * A unique identifier for a hash in a cache.
 *
 *
 * The `cacheIndex` is the index of the cache in the `hashCaches` array.
 * The `hashIndex` is the index of the hash in the cache.
 */
export declare type HashId = number;
export declare function allocHashCache(): HashCache;
export declare function getCache(id: HashId): HashCache;
export declare function getCacheOffset(id: HashId): number;
export declare function incrementNext(cache: HashCache): number;
export declare function newHashId(cacheIndex: number, cache: HashCache): HashId;
export declare function allocHashId(): HashId;
export declare function freeHashId(id: HashId): void;
export declare function cloneHashId(source: HashId, target: HashId): void;
export declare function getHash(id: HashId): Uint8Array;
export declare function getHashObject(id: HashId): HashObject;
export declare function setHashObject(id: HashId, obj: HashObject): void;
export declare function setHashObjectItems(id: HashId, h0: number, h1: number, h2: number, h3: number, h4: number, h5: number, h6: number, h7: number): void;
export declare function setHash(id: HashId, hash: Uint8Array): void;
//# sourceMappingURL=hashCache.d.ts.map