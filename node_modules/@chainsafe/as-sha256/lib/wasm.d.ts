export interface WasmContext {
    readonly HAS_SIMD: boolean;
    readonly PARALLEL_FACTOR: number;
    readonly INPUT_LENGTH: number;
    memory: {
        buffer: ArrayBuffer;
    };
    input: {
        value: number;
    };
    output: {
        value: number;
    };
    init(): void;
    update(dataPtr: number, dataLength: number): void;
    final(outPtr: number): void;
    digest(length: number): void;
    digest64(inPtr: number, outPtr: number): void;
    batchHash4UintArray64s(outPtr: number): void;
    batchHash4HashObjectInputs(outPtr: number): void;
}
export declare function newInstance(useSimd?: boolean): WasmContext;
