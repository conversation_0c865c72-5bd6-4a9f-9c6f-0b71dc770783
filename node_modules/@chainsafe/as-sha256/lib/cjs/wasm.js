"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.newInstance = void 0;
const wasmCode_js_1 = require("./wasmCode.js");
const wasmSimdCode_js_1 = require("./wasmSimdCode.js");
const importObj = {
    env: {
        // modified from https://github.com/AssemblyScript/assemblyscript/blob/v0.9.2/lib/loader/index.js#L70
        abort: function (msg, file, line, col) {
            throw Error(`abort: ${msg}:${file}:${line}:${col}`);
        },
    },
};
function newInstance(useSimd) {
    const enableSimd = useSimd !== undefined ? useSimd : WebAssembly.validate(wasmSimdCode_js_1.wasmSimdCode);
    return enableSimd
        ? new WebAssembly.Instance(new WebAssembly.Module(wasmSimdCode_js_1.wasmSimdCode), importObj).exports
        : new WebAssembly.Instance(new WebAssembly.Module(wasmCode_js_1.wasmCode), importObj).exports;
}
exports.newInstance = newInstance;
//# sourceMappingURL=wasm.js.map