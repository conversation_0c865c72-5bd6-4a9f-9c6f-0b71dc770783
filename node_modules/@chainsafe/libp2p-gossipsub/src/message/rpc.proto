syntax = "proto3";

message RPC {
  repeated SubOpts subscriptions = 1;
  repeated Message messages = 2;
  optional ControlMessage control = 3;

  message SubOpts {
    optional bool subscribe = 1; // subscribe or unsubcribe
    optional string topic = 2;
  }

  message Message {
    optional bytes from = 1;
    optional bytes data = 2;
    optional bytes seqno = 3;
    string topic = 4;
    optional bytes signature = 5;
    optional bytes key = 6;
  }

  message ControlMessage {
    repeated ControlIHave ihave = 1;
    repeated ControlIWant iwant = 2;
    repeated ControlGraft graft = 3;
    repeated ControlPrune prune = 4;
    repeated ControlIDontWant idontwant = 5;
  }

  message ControlIHave {
    optional string topicID = 1;
    repeated bytes messageIDs = 2;
  }

  message ControlIWant {
    repeated bytes messageIDs = 1;
  }

  message ControlGraft {
    optional string topicID = 1;
  }

  message ControlPrune {
    optional string topicID = 1;
    repeated PeerInfo peers = 2;
    optional uint64 backoff = 3 [jstype = JS_NUMBER];
  }

  message PeerInfo {
    optional bytes peerID = 1;
    optional bytes signedPeerRecord = 2;
  }

  message ControlIDontWant {
    repeated bytes messageIDs = 1;
  }

}