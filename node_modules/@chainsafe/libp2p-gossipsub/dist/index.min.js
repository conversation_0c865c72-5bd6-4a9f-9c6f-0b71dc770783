(function (root, factory) {(typeof module === 'object' && module.exports) ? module.exports = factory() : root.ChainsafeLibp2PGossipsub = factory()}(typeof self !== 'undefined' ? self : this, function () {
"use strict";var ChainsafeLibp2PGossipsub=(()=>{var Ll=Object.create;var nr=Object.defineProperty;var Ul=Object.getOwnPropertyDescriptor;var Fl=Object.getOwnPropertyNames;var Vl=Object.getPrototypeOf,Hl=Object.prototype.hasOwnProperty;var Po=(s,e)=>()=>(e||s((e={exports:{}}).exports,e),e.exports),ve=(s,e)=>{for(var t in e)nr(s,t,{get:e[t],enumerable:!0})},Do=(s,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Fl(e))!Hl.call(s,n)&&n!==t&&nr(s,n,{get:()=>e[n],enumerable:!(r=Ul(e,n))||r.enumerable});return s};var Co=(s,e,t)=>(t=s!=null?Ll(Vl(s)):{},Do(e||!s||!s.__esModule?nr(t,"default",{value:s,enumerable:!0}):t,s)),Ol=s=>Do(nr({},"__esModule",{value:!0}),s);var Na=Po(ds=>{"use strict";var df="[object ArrayBuffer]",ut=class s{static isArrayBuffer(e){return Object.prototype.toString.call(e)===df}static toArrayBuffer(e){return this.isArrayBuffer(e)?e:e.byteLength===e.buffer.byteLength||e.byteOffset===0&&e.byteLength===e.buffer.byteLength?e.buffer:this.toUint8Array(e.buffer).slice(e.byteOffset,e.byteOffset+e.byteLength).buffer}static toUint8Array(e){return this.toView(e,Uint8Array)}static toView(e,t){if(e.constructor===t)return e;if(this.isArrayBuffer(e))return new t(e);if(this.isArrayBufferView(e))return new t(e.buffer,e.byteOffset,e.byteLength);throw new TypeError("The provided value is not of type '(ArrayBuffer or ArrayBufferView)'")}static isBufferSource(e){return this.isArrayBufferView(e)||this.isArrayBuffer(e)}static isArrayBufferView(e){return ArrayBuffer.isView(e)||e&&this.isArrayBuffer(e.buffer)}static isEqual(e,t){let r=s.toUint8Array(e),n=s.toUint8Array(t);if(r.length!==n.byteLength)return!1;for(let i=0;i<r.length;i++)if(r[i]!==n[i])return!1;return!0}static concat(...e){let t;Array.isArray(e[0])&&!(e[1]instanceof Function)||Array.isArray(e[0])&&e[1]instanceof Function?t=e[0]:e[e.length-1]instanceof Function?t=e.slice(0,e.length-1):t=e;let r=0;for(let o of t)r+=o.byteLength;let n=new Uint8Array(r),i=0;for(let o of t){let a=this.toUint8Array(o);n.set(a,i),i+=a.length}return e[e.length-1]instanceof Function?this.toView(n,e[e.length-1]):n.buffer}},Yi="string",pf=/^[0-9a-f]+$/i,gf=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/,mf=/^[a-zA-Z0-9-_]+$/,Sr=class{static fromString(e){let t=unescape(encodeURIComponent(e)),r=new Uint8Array(t.length);for(let n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r.buffer}static toString(e){let t=ut.toUint8Array(e),r="";for(let i=0;i<t.length;i++)r+=String.fromCharCode(t[i]);return decodeURIComponent(escape(r))}},Ve=class{static toString(e,t=!1){let r=ut.toArrayBuffer(e),n=new DataView(r),i="";for(let o=0;o<r.byteLength;o+=2){let a=n.getUint16(o,t);i+=String.fromCharCode(a)}return i}static fromString(e,t=!1){let r=new ArrayBuffer(e.length*2),n=new DataView(r);for(let i=0;i<e.length;i++)n.setUint16(i*2,e.charCodeAt(i),t);return r}},Br=class s{static isHex(e){return typeof e===Yi&&pf.test(e)}static isBase64(e){return typeof e===Yi&&gf.test(e)}static isBase64Url(e){return typeof e===Yi&&mf.test(e)}static ToString(e,t="utf8"){let r=ut.toUint8Array(e);switch(t.toLowerCase()){case"utf8":return this.ToUtf8String(r);case"binary":return this.ToBinary(r);case"hex":return this.ToHex(r);case"base64":return this.ToBase64(r);case"base64url":return this.ToBase64Url(r);case"utf16le":return Ve.toString(r,!0);case"utf16":case"utf16be":return Ve.toString(r);default:throw new Error(`Unknown type of encoding '${t}'`)}}static FromString(e,t="utf8"){if(!e)return new ArrayBuffer(0);switch(t.toLowerCase()){case"utf8":return this.FromUtf8String(e);case"binary":return this.FromBinary(e);case"hex":return this.FromHex(e);case"base64":return this.FromBase64(e);case"base64url":return this.FromBase64Url(e);case"utf16le":return Ve.fromString(e,!0);case"utf16":case"utf16be":return Ve.fromString(e);default:throw new Error(`Unknown type of encoding '${t}'`)}}static ToBase64(e){let t=ut.toUint8Array(e);if(typeof btoa<"u"){let r=this.ToString(t,"binary");return btoa(r)}else return Buffer.from(t).toString("base64")}static FromBase64(e){let t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!s.isBase64(t))throw new TypeError("Argument 'base64Text' is not Base64 encoded");return typeof atob<"u"?this.FromBinary(atob(t)):new Uint8Array(Buffer.from(t,"base64")).buffer}static FromBase64Url(e){let t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!s.isBase64Url(t))throw new TypeError("Argument 'base64url' is not Base64Url encoded");return this.FromBase64(this.Base64Padding(t.replace(/\-/g,"+").replace(/\_/g,"/")))}static ToBase64Url(e){return this.ToBase64(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/\=/g,"")}static FromUtf8String(e,t=s.DEFAULT_UTF8_ENCODING){switch(t){case"ascii":return this.FromBinary(e);case"utf8":return Sr.fromString(e);case"utf16":case"utf16be":return Ve.fromString(e);case"utf16le":case"usc2":return Ve.fromString(e,!0);default:throw new Error(`Unknown type of encoding '${t}'`)}}static ToUtf8String(e,t=s.DEFAULT_UTF8_ENCODING){switch(t){case"ascii":return this.ToBinary(e);case"utf8":return Sr.toString(e);case"utf16":case"utf16be":return Ve.toString(e);case"utf16le":case"usc2":return Ve.toString(e,!0);default:throw new Error(`Unknown type of encoding '${t}'`)}}static FromBinary(e){let t=e.length,r=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);return r.buffer}static ToBinary(e){let t=ut.toUint8Array(e),r="";for(let n=0;n<t.length;n++)r+=String.fromCharCode(t[n]);return r}static ToHex(e){let t=ut.toUint8Array(e),r="",n=t.length;for(let i=0;i<n;i++){let o=t[i];o<16&&(r+="0"),r+=o.toString(16)}return r}static FromHex(e){let t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!s.isHex(t))throw new TypeError("Argument 'hexString' is not HEX encoded");t.length%2&&(t=`0${t}`);let r=new Uint8Array(t.length/2);for(let n=0;n<t.length;n=n+2){let i=t.slice(n,n+2);r[n/2]=parseInt(i,16)}return r.buffer}static ToUtf16String(e,t=!1){return Ve.toString(e,t)}static FromUtf16String(e,t=!1){return Ve.fromString(e,t)}static Base64Padding(e){let t=4-e.length%4;if(t<4)for(let r=0;r<t;r++)e+="=";return e}static formatString(e){return e?.replace(/[\n\r\t ]/g,"")||""}};Br.DEFAULT_UTF8_ENCODING="utf8";function bf(s,...e){let t=arguments[0];for(let r=1;r<arguments.length;r++){let n=arguments[r];for(let i in n)t[i]=n[i]}return t}function wf(...s){let e=s.map(n=>n.byteLength).reduce((n,i)=>n+i),t=new Uint8Array(e),r=0;return s.map(n=>new Uint8Array(n)).forEach(n=>{for(let i of n)t[r++]=i}),t.buffer}function yf(s,e){if(!(s&&e)||s.byteLength!==e.byteLength)return!1;let t=new Uint8Array(s),r=new Uint8Array(e);for(let n=0;n<s.byteLength;n++)if(t[n]!==r[n])return!1;return!0}ds.BufferSourceConverter=ut;ds.Convert=Br;ds.assign=bf;ds.combine=wf;ds.isEqual=yf});var cl=Po((Tw,al)=>{"use strict";function Y(s,t){var t=t||{};this._capacity=t.capacity,this._head=0,this._tail=0,Array.isArray(s)?this._fromArray(s):(this._capacityMask=3,this._list=new Array(4))}Y.prototype.peekAt=function(e){var t=e;if(t===(t|0)){var r=this.size();if(!(t>=r||t<-r))return t<0&&(t+=r),t=this._head+t&this._capacityMask,this._list[t]}};Y.prototype.get=function(e){return this.peekAt(e)};Y.prototype.peek=function(){if(this._head!==this._tail)return this._list[this._head]};Y.prototype.peekFront=function(){return this.peek()};Y.prototype.peekBack=function(){return this.peekAt(-1)};Object.defineProperty(Y.prototype,"length",{get:function(){return this.size()}});Y.prototype.size=function(){return this._head===this._tail?0:this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)};Y.prototype.unshift=function(e){if(arguments.length===0)return this.size();var t=this._list.length;return this._head=this._head-1+t&this._capacityMask,this._list[this._head]=e,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.pop(),this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)};Y.prototype.shift=function(){var e=this._head;if(e!==this._tail){var t=this._list[e];return this._list[e]=void 0,this._head=e+1&this._capacityMask,e<2&&this._tail>1e4&&this._tail<=this._list.length>>>2&&this._shrinkArray(),t}};Y.prototype.push=function(e){if(arguments.length===0)return this.size();var t=this._tail;return this._list[t]=e,this._tail=t+1&this._capacityMask,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.shift(),this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)};Y.prototype.pop=function(){var e=this._tail;if(e!==this._head){var t=this._list.length;this._tail=e-1+t&this._capacityMask;var r=this._list[this._tail];return this._list[this._tail]=void 0,this._head<2&&e>1e4&&e<=t>>>2&&this._shrinkArray(),r}};Y.prototype.removeOne=function(e){var t=e;if(t===(t|0)&&this._head!==this._tail){var r=this.size(),n=this._list.length;if(!(t>=r||t<-r)){t<0&&(t+=r),t=this._head+t&this._capacityMask;var i=this._list[t],o;if(e<r/2){for(o=e;o>0;o--)this._list[t]=this._list[t=t-1+n&this._capacityMask];this._list[t]=void 0,this._head=this._head+1+n&this._capacityMask}else{for(o=r-1-e;o>0;o--)this._list[t]=this._list[t=t+1+n&this._capacityMask];this._list[t]=void 0,this._tail=this._tail-1+n&this._capacityMask}return i}}};Y.prototype.remove=function(e,t){var r=e,n,i=t;if(r===(r|0)&&this._head!==this._tail){var o=this.size(),a=this._list.length;if(!(r>=o||r<-o||t<1)){if(r<0&&(r+=o),t===1||!t)return n=new Array(1),n[0]=this.removeOne(r),n;if(r===0&&r+t>=o)return n=this.toArray(),this.clear(),n;r+t>o&&(t=o-r);var c;for(n=new Array(t),c=0;c<t;c++)n[c]=this._list[this._head+r+c&this._capacityMask];if(r=this._head+r&this._capacityMask,e+t===o){for(this._tail=this._tail-t+a&this._capacityMask,c=t;c>0;c--)this._list[r=r+1+a&this._capacityMask]=void 0;return n}if(e===0){for(this._head=this._head+t+a&this._capacityMask,c=t-1;c>0;c--)this._list[r=r+1+a&this._capacityMask]=void 0;return n}if(r<o/2){for(this._head=this._head+e+t+a&this._capacityMask,c=e;c>0;c--)this.unshift(this._list[r=r-1+a&this._capacityMask]);for(r=this._head-1+a&this._capacityMask;i>0;)this._list[r=r-1+a&this._capacityMask]=void 0,i--;e<0&&(this._tail=r)}else{for(this._tail=r,r=r+t+a&this._capacityMask,c=o-(t+e);c>0;c--)this.push(this._list[r++]);for(r=this._tail;i>0;)this._list[r=r+1+a&this._capacityMask]=void 0,i--}return this._head<2&&this._tail>1e4&&this._tail<=a>>>2&&this._shrinkArray(),n}}};Y.prototype.splice=function(e,t){var r=e;if(r===(r|0)){var n=this.size();if(r<0&&(r+=n),!(r>n))if(arguments.length>2){var i,o,a,c=arguments.length,h=this._list.length,f=2;if(!n||r<n/2){for(o=new Array(r),i=0;i<r;i++)o[i]=this._list[this._head+i&this._capacityMask];for(t===0?(a=[],r>0&&(this._head=this._head+r+h&this._capacityMask)):(a=this.remove(r,t),this._head=this._head+r+h&this._capacityMask);c>f;)this.unshift(arguments[--c]);for(i=r;i>0;i--)this.unshift(o[i-1])}else{o=new Array(n-(r+t));var l=o.length;for(i=0;i<l;i++)o[i]=this._list[this._head+r+t+i&this._capacityMask];for(t===0?(a=[],r!=n&&(this._tail=this._head+r+h&this._capacityMask)):(a=this.remove(r,t),this._tail=this._tail-l+h&this._capacityMask);f<c;)this.push(arguments[f++]);for(i=0;i<l;i++)this.push(o[i])}return a}else return this.remove(r,t)}};Y.prototype.clear=function(){this._list=new Array(this._list.length),this._head=0,this._tail=0};Y.prototype.isEmpty=function(){return this._head===this._tail};Y.prototype.toArray=function(){return this._copyArray(!1)};Y.prototype._fromArray=function(e){var t=e.length,r=this._nextPowerOf2(t);this._list=new Array(r),this._capacityMask=r-1,this._tail=t;for(var n=0;n<t;n++)this._list[n]=e[n]};Y.prototype._copyArray=function(e,t){var r=this._list,n=r.length,i=this.length;if(t=t|i,t==i&&this._head<this._tail)return this._list.slice(this._head,this._tail);var o=new Array(t),a=0,c;if(e||this._head>this._tail){for(c=this._head;c<n;c++)o[a++]=r[c];for(c=0;c<this._tail;c++)o[a++]=r[c]}else for(c=this._head;c<this._tail;c++)o[a++]=r[c];return o};Y.prototype._growArray=function(){if(this._head!=0){var e=this._copyArray(!0,this._list.length<<1);this._tail=this._list.length,this._head=0,this._list=e}else this._tail=this._list.length,this._list.length<<=1;this._capacityMask=this._capacityMask<<1|1};Y.prototype._shrinkArray=function(){this._list.length>>>=1,this._capacityMask>>>=1};Y.prototype._nextPowerOf2=function(e){var t=Math.log(e)/Math.log(2),r=1<<t+1;return Math.max(r,4)};al.exports=Y});var zd={};ve(zd,{GossipSub:()=>On,gossipsub:()=>Od,multicodec:()=>Hd});var Gn=Symbol.for("@libp2p/peer-id");var Ut="StrictSign",Xt="StrictNoSign",xe;(function(s){s.Accept="accept",s.Ignore="ignore",s.Reject="reject"})(xe||(xe={}));var ce=class extends Error{static name="InvalidParametersError";constructor(e="Invalid parameters"){super(e),this.name="InvalidParametersError"}},Yt=class extends Error{static name="InvalidPublicKeyError";constructor(e="Invalid public key"){super(e),this.name="InvalidPublicKeyError"}};var ir=class extends Error{static name="InvalidMultihashError";constructor(e="Invalid Multihash"){super(e),this.name="InvalidMultihashError"}};var Qt=class extends Error{static name="UnsupportedKeyTypeError";constructor(e="Unsupported key type"){super(e),this.name="UnsupportedKeyTypeError"}};var No=(s,...e)=>{try{[...e]}catch{}};var or=class extends EventTarget{#e=new Map;constructor(){super(),No(1/0,this)}listenerCount(e){let t=this.#e.get(e);return t==null?0:t.length}addEventListener(e,t,r){super.addEventListener(e,t,r);let n=this.#e.get(e);n==null&&(n=[],this.#e.set(e,n)),n.push({callback:t,once:(r!==!0&&r!==!1&&r?.once)??!1})}removeEventListener(e,t,r){super.removeEventListener(e.toString(),t??null,r);let n=this.#e.get(e);n!=null&&(n=n.filter(({callback:i})=>i!==t),this.#e.set(e,n))}dispatchEvent(e){let t=super.dispatchEvent(e),r=this.#e.get(e.type);return r==null||(r=r.filter(({once:n})=>!n),this.#e.set(e.type,r)),t}safeDispatchEvent(e,t={}){return this.dispatchEvent(new CustomEvent(e,t))}};var Ro=Symbol.for("@libp2p/service-capabilities"),Lo=Symbol.for("@libp2p/service-dependencies");var jn={};ve(jn,{base58btc:()=>te,base58flickr:()=>$l});var yp=new Uint8Array(0);function Uo(s,e){if(s===e)return!0;if(s.byteLength!==e.byteLength)return!1;for(let t=0;t<s.byteLength;t++)if(s[t]!==e[t])return!1;return!0}function rt(s){if(s instanceof Uint8Array&&s.constructor.name==="Uint8Array")return s;if(s instanceof ArrayBuffer)return new Uint8Array(s);if(ArrayBuffer.isView(s))return new Uint8Array(s.buffer,s.byteOffset,s.byteLength);throw new Error("Unknown type, must be binary type")}function Fo(s){return new TextEncoder().encode(s)}function Vo(s){return new TextDecoder().decode(s)}function zl(s,e){if(s.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),r=0;r<t.length;r++)t[r]=255;for(var n=0;n<s.length;n++){var i=s.charAt(n),o=i.charCodeAt(0);if(t[o]!==255)throw new TypeError(i+" is ambiguous");t[o]=n}var a=s.length,c=s.charAt(0),h=Math.log(a)/Math.log(256),f=Math.log(256)/Math.log(a);function l(d){if(d instanceof Uint8Array||(ArrayBuffer.isView(d)?d=new Uint8Array(d.buffer,d.byteOffset,d.byteLength):Array.isArray(d)&&(d=Uint8Array.from(d))),!(d instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(d.length===0)return"";for(var m=0,p=0,b=0,y=d.length;b!==y&&d[b]===0;)b++,m++;for(var w=(y-b)*f+1>>>0,I=new Uint8Array(w);b!==y;){for(var x=d[b],B=0,E=w-1;(x!==0||B<p)&&E!==-1;E--,B++)x+=256*I[E]>>>0,I[E]=x%a>>>0,x=x/a>>>0;if(x!==0)throw new Error("Non-zero carry");p=B,b++}for(var _=w-p;_!==w&&I[_]===0;)_++;for(var k=c.repeat(m);_<w;++_)k+=s.charAt(I[_]);return k}function g(d){if(typeof d!="string")throw new TypeError("Expected String");if(d.length===0)return new Uint8Array;var m=0;if(d[m]!==" "){for(var p=0,b=0;d[m]===c;)p++,m++;for(var y=(d.length-m)*h+1>>>0,w=new Uint8Array(y);d[m];){var I=t[d.charCodeAt(m)];if(I===255)return;for(var x=0,B=y-1;(I!==0||x<b)&&B!==-1;B--,x++)I+=a*w[B]>>>0,w[B]=I%256>>>0,I=I/256>>>0;if(I!==0)throw new Error("Non-zero carry");b=x,m++}if(d[m]!==" "){for(var E=y-b;E!==y&&w[E]===0;)E++;for(var _=new Uint8Array(p+(y-E)),k=p;E!==y;)_[k++]=w[E++];return _}}}function u(d){var m=g(d);if(m)return m;throw new Error(`Non-${e} character`)}return{encode:l,decodeUnsafe:g,decode:u}}var Gl=zl,ql=Gl,Oo=ql;var qn=class{name;prefix;baseEncode;constructor(e,t,r){this.name=e,this.prefix=t,this.baseEncode=r}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}},Kn=class{name;prefix;baseDecode;prefixCodePoint;constructor(e,t,r){if(this.name=e,this.prefix=t,t.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=t.codePointAt(0),this.baseDecode=r}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return zo(this,e)}},Wn=class{decoders;constructor(e){this.decoders=e}or(e){return zo(this,e)}decode(e){let t=e[0],r=this.decoders[t];if(r!=null)return r.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}};function zo(s,e){return new Wn({...s.decoders??{[s.prefix]:s},...e.decoders??{[e.prefix]:e}})}var $n=class{name;prefix;baseEncode;baseDecode;encoder;decoder;constructor(e,t,r,n){this.name=e,this.prefix=t,this.baseEncode=r,this.baseDecode=n,this.encoder=new qn(e,t,r),this.decoder=new Kn(e,t,n)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}};function es({name:s,prefix:e,encode:t,decode:r}){return new $n(s,e,t,r)}function vt({name:s,prefix:e,alphabet:t}){let{encode:r,decode:n}=Oo(t,s);return es({prefix:e,name:s,encode:r,decode:i=>rt(n(i))})}function Kl(s,e,t,r){let n={};for(let f=0;f<e.length;++f)n[e[f]]=f;let i=s.length;for(;s[i-1]==="=";)--i;let o=new Uint8Array(i*t/8|0),a=0,c=0,h=0;for(let f=0;f<i;++f){let l=n[s[f]];if(l===void 0)throw new SyntaxError(`Non-${r} character`);c=c<<t|l,a+=t,a>=8&&(a-=8,o[h++]=255&c>>a)}if(a>=t||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o}function Wl(s,e,t){let r=e[e.length-1]==="=",n=(1<<t)-1,i="",o=0,a=0;for(let c=0;c<s.length;++c)for(a=a<<8|s[c],o+=8;o>t;)o-=t,i+=e[n&a>>o];if(o!==0&&(i+=e[n&a<<t-o]),r)for(;i.length*t&7;)i+="=";return i}function Q({name:s,prefix:e,bitsPerChar:t,alphabet:r}){return es({prefix:e,name:s,encode(n){return Wl(n,r,t)},decode(n){return Kl(n,r,t,s)}})}var te=vt({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),$l=vt({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var Zn={};ve(Zn,{base32:()=>Ft,base32hex:()=>Xl,base32hexpad:()=>Ql,base32hexpadupper:()=>eu,base32hexupper:()=>Yl,base32pad:()=>Zl,base32padupper:()=>Jl,base32upper:()=>jl,base32z:()=>tu});var Ft=Q({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),jl=Q({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),Zl=Q({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),Jl=Q({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),Xl=Q({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),Yl=Q({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),Ql=Q({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),eu=Q({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),tu=Q({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var su=Ko,Go=128,ru=127,nu=~ru,iu=Math.pow(2,31);function Ko(s,e,t){e=e||[],t=t||0;for(var r=t;s>=iu;)e[t++]=s&255|Go,s/=128;for(;s&nu;)e[t++]=s&255|Go,s>>>=7;return e[t]=s|0,Ko.bytes=t-r+1,e}var ou=Jn,au=128,qo=127;function Jn(s,r){var t=0,r=r||0,n=0,i=r,o,a=s.length;do{if(i>=a)throw Jn.bytes=0,new RangeError("Could not decode varint");o=s[i++],t+=n<28?(o&qo)<<n:(o&qo)*Math.pow(2,n),n+=7}while(o>=au);return Jn.bytes=i-r,t}var cu=Math.pow(2,7),lu=Math.pow(2,14),uu=Math.pow(2,21),hu=Math.pow(2,28),fu=Math.pow(2,35),du=Math.pow(2,42),pu=Math.pow(2,49),gu=Math.pow(2,56),mu=Math.pow(2,63),bu=function(s){return s<cu?1:s<lu?2:s<uu?3:s<hu?4:s<fu?5:s<du?6:s<pu?7:s<gu?8:s<mu?9:10},wu={encode:su,decode:ou,encodingLength:bu},yu=wu,Ms=yu;function Ps(s,e=0){return[Ms.decode(s,e),Ms.decode.bytes]}function ts(s,e,t=0){return Ms.encode(s,e,t),e}function ss(s){return Ms.encodingLength(s)}function He(s,e){let t=e.byteLength,r=ss(s),n=r+ss(t),i=new Uint8Array(n+t);return ts(s,i,0),ts(t,i,r),i.set(e,n),new rs(s,t,e,i)}function Oe(s){let e=rt(s),[t,r]=Ps(e),[n,i]=Ps(e.subarray(r)),o=e.subarray(r+i);if(o.byteLength!==n)throw new Error("Incorrect length");return new rs(t,n,o,e)}function Wo(s,e){if(s===e)return!0;{let t=e;return s.code===t.code&&s.size===t.size&&t.bytes instanceof Uint8Array&&Uo(s.bytes,t.bytes)}}var rs=class{code;size;digest;bytes;constructor(e,t,r,n){this.code=e,this.size=t,this.digest=r,this.bytes=n}};function $o(s,e){let{bytes:t,version:r}=s;switch(r){case 0:return xu(t,Xn(s),e??te.encoder);default:return Eu(t,Xn(s),e??Ft.encoder)}}var jo=new WeakMap;function Xn(s){let e=jo.get(s);if(e==null){let t=new Map;return jo.set(s,t),t}return e}var Pe=class s{code;version;multihash;bytes;"/";constructor(e,t,r,n){this.code=t,this.version=e,this.multihash=r,this.bytes=n,this["/"]=n}get asCID(){return this}get byteOffset(){return this.bytes.byteOffset}get byteLength(){return this.bytes.byteLength}toV0(){switch(this.version){case 0:return this;case 1:{let{code:e,multihash:t}=this;if(e!==Ds)throw new Error("Cannot convert a non dag-pb CID to CIDv0");if(t.code!==Su)throw new Error("Cannot convert non sha2-256 multihash CID to CIDv0");return s.createV0(t)}default:throw Error(`Can not convert CID version ${this.version} to version 0. This is a bug please report`)}}toV1(){switch(this.version){case 0:{let{code:e,digest:t}=this.multihash,r=He(e,t);return s.createV1(this.code,r)}case 1:return this;default:throw Error(`Can not convert CID version ${this.version} to version 1. This is a bug please report`)}}equals(e){return s.equals(this,e)}static equals(e,t){let r=t;return r!=null&&e.code===r.code&&e.version===r.version&&Wo(e.multihash,r.multihash)}toString(e){return $o(this,e)}toJSON(){return{"/":$o(this)}}link(){return this}[Symbol.toStringTag]="CID";[Symbol.for("nodejs.util.inspect.custom")](){return`CID(${this.toString()})`}static asCID(e){if(e==null)return null;let t=e;if(t instanceof s)return t;if(t["/"]!=null&&t["/"]===t.bytes||t.asCID===t){let{version:r,code:n,multihash:i,bytes:o}=t;return new s(r,n,i,o??Zo(r,n,i.bytes))}else if(t[Bu]===!0){let{version:r,multihash:n,code:i}=t,o=Oe(n);return s.create(r,i,o)}else return null}static create(e,t,r){if(typeof t!="number")throw new Error("String codecs are no longer supported");if(!(r.bytes instanceof Uint8Array))throw new Error("Invalid digest");switch(e){case 0:{if(t!==Ds)throw new Error(`Version 0 CID must use dag-pb (code: ${Ds}) block encoding`);return new s(e,t,r,r.bytes)}case 1:{let n=Zo(e,t,r.bytes);return new s(e,t,r,n)}default:throw new Error("Invalid version")}}static createV0(e){return s.create(0,Ds,e)}static createV1(e,t){return s.create(1,e,t)}static decode(e){let[t,r]=s.decodeFirst(e);if(r.length!==0)throw new Error("Incorrect length");return t}static decodeFirst(e){let t=s.inspectBytes(e),r=t.size-t.multihashSize,n=rt(e.subarray(r,r+t.multihashSize));if(n.byteLength!==t.multihashSize)throw new Error("Incorrect length");let i=n.subarray(t.multihashSize-t.digestSize),o=new rs(t.multihashCode,t.digestSize,i,n);return[t.version===0?s.createV0(o):s.createV1(t.codec,o),e.subarray(t.size)]}static inspectBytes(e){let t=0,r=()=>{let[l,g]=Ps(e.subarray(t));return t+=g,l},n=r(),i=Ds;if(n===18?(n=0,t=0):i=r(),n!==0&&n!==1)throw new RangeError(`Invalid CID version ${n}`);let o=t,a=r(),c=r(),h=t+c,f=h-o;return{version:n,codec:i,multihashCode:a,digestSize:c,multihashSize:f,size:h}}static parse(e,t){let[r,n]=vu(e,t),i=s.decode(n);if(i.version===0&&e[0]!=="Q")throw Error("Version 0 CID string must not include multibase prefix");return Xn(i).set(r,e),i}};function vu(s,e){switch(s[0]){case"Q":{let t=e??te;return[te.prefix,t.decode(`${te.prefix}${s}`)]}case te.prefix:{let t=e??te;return[te.prefix,t.decode(s)]}case Ft.prefix:{let t=e??Ft;return[Ft.prefix,t.decode(s)]}default:{if(e==null)throw Error("To parse non base32 or base58btc encoded CID multibase decoder must be provided");return[s[0],e.decode(s)]}}}function xu(s,e,t){let{prefix:r}=t;if(r!==te.prefix)throw Error(`Cannot string encode V0 in ${t.name} encoding`);let n=e.get(r);if(n==null){let i=t.encode(s).slice(1);return e.set(r,i),i}else return n}function Eu(s,e,t){let{prefix:r}=t,n=e.get(r);if(n==null){let i=t.encode(s);return e.set(r,i),i}else return n}var Ds=112,Su=18;function Zo(s,e,t){let r=ss(s),n=r+ss(e),i=new Uint8Array(n+t.byteLength);return ts(s,i,0),ts(e,i,r),i.set(t,n),i}var Bu=Symbol.for("@ipld/js-cid/CID");var Yn={};ve(Yn,{identity:()=>nt});var Jo=0,Iu="identity",Xo=rt;function _u(s){return He(Jo,Xo(s))}var nt={code:Jo,name:Iu,encode:Xo,digest:_u};function Fe(s,e){if(s===e)return!0;if(s.byteLength!==e.byteLength)return!1;for(let t=0;t<s.byteLength;t++)if(s[t]!==e[t])return!1;return!0}function Yo(s){if(!Number.isSafeInteger(s)||s<0)throw new Error(`positive integer expected, not ${s}`)}function Au(s){return s instanceof Uint8Array||s!=null&&typeof s=="object"&&s.constructor.name==="Uint8Array"}function ns(s,...e){if(!Au(s))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(s.length))throw new Error(`Uint8Array expected of length ${e}, not of length=${s.length}`)}function Qo(s){if(typeof s!="function"||typeof s.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Yo(s.outputLen),Yo(s.blockLen)}function is(s,e=!0){if(s.destroyed)throw new Error("Hash instance has been destroyed");if(e&&s.finished)throw new Error("Hash#digest() has already been called")}function ea(s,e){ns(s);let t=e.outputLen;if(s.length<t)throw new Error(`digestInto() expects output buffer of length at least ${t}`)}var Vt=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;var cr=s=>new DataView(s.buffer,s.byteOffset,s.byteLength),ze=(s,e)=>s<<32-e|s>>>e;var Hp=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function ta(s){if(typeof s!="string")throw new Error(`utf8ToBytes expected string, got ${typeof s}`);return new Uint8Array(new TextEncoder().encode(s))}function Cs(s){return typeof s=="string"&&(s=ta(s)),ns(s),s}function Qn(...s){let e=0;for(let r=0;r<s.length;r++){let n=s[r];ns(n),e+=n.length}let t=new Uint8Array(e);for(let r=0,n=0;r<s.length;r++){let i=s[r];t.set(i,n),n+=i.length}return t}var os=class{clone(){return this._cloneInto()}},Op={}.toString;function lr(s){let e=r=>s().update(Cs(r)).digest(),t=s();return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=()=>s(),e}function as(s=32){if(Vt&&typeof Vt.getRandomValues=="function")return Vt.getRandomValues(new Uint8Array(s));if(Vt&&typeof Vt.randomBytes=="function")return Vt.randomBytes(s);throw new Error("crypto.getRandomValues must be defined")}function ku(s,e,t,r){if(typeof s.setBigUint64=="function")return s.setBigUint64(e,t,r);let n=BigInt(32),i=BigInt(4294967295),o=Number(t>>n&i),a=Number(t&i),c=r?4:0,h=r?0:4;s.setUint32(e+c,o,r),s.setUint32(e+h,a,r)}var sa=(s,e,t)=>s&e^~s&t,ra=(s,e,t)=>s&e^s&t^e&t,cs=class extends os{constructor(e,t,r,n){super(),this.blockLen=e,this.outputLen=t,this.padOffset=r,this.isLE=n,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=cr(this.buffer)}update(e){is(this);let{view:t,buffer:r,blockLen:n}=this;e=Cs(e);let i=e.length;for(let o=0;o<i;){let a=Math.min(n-this.pos,i-o);if(a===n){let c=cr(e);for(;n<=i-o;o+=n)this.process(c,o);continue}r.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===n&&(this.process(t,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){is(this),ea(e,this),this.finished=!0;let{buffer:t,view:r,blockLen:n,isLE:i}=this,{pos:o}=this;t[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>n-o&&(this.process(r,0),o=0);for(let l=o;l<n;l++)t[l]=0;ku(r,n-8,BigInt(this.length*8),i),this.process(r,0);let a=cr(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");let h=c/4,f=this.get();if(h>f.length)throw new Error("_sha2: outputLen bigger than state");for(let l=0;l<h;l++)a.setUint32(4*l,f[l],i)}digest(){let{buffer:e,outputLen:t}=this;this.digestInto(e);let r=e.slice(0,t);return this.destroy(),r}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());let{blockLen:t,buffer:r,length:n,finished:i,destroyed:o,pos:a}=this;return e.length=n,e.pos=a,e.finished=i,e.destroyed=o,n%t&&e.buffer.set(r),e}};var ur=BigInt(4294967295),ei=BigInt(32);function na(s,e=!1){return e?{h:Number(s&ur),l:Number(s>>ei&ur)}:{h:Number(s>>ei&ur)|0,l:Number(s&ur)|0}}function Tu(s,e=!1){let t=new Uint32Array(s.length),r=new Uint32Array(s.length);for(let n=0;n<s.length;n++){let{h:i,l:o}=na(s[n],e);[t[n],r[n]]=[i,o]}return[t,r]}var Mu=(s,e)=>BigInt(s>>>0)<<ei|BigInt(e>>>0),Pu=(s,e,t)=>s>>>t,Du=(s,e,t)=>s<<32-t|e>>>t,Cu=(s,e,t)=>s>>>t|e<<32-t,Nu=(s,e,t)=>s<<32-t|e>>>t,Ru=(s,e,t)=>s<<64-t|e>>>t-32,Lu=(s,e,t)=>s>>>t-32|e<<64-t,Uu=(s,e)=>e,Fu=(s,e)=>s,Vu=(s,e,t)=>s<<t|e>>>32-t,Hu=(s,e,t)=>e<<t|s>>>32-t,Ou=(s,e,t)=>e<<t-32|s>>>64-t,zu=(s,e,t)=>s<<t-32|e>>>64-t;function Gu(s,e,t,r){let n=(e>>>0)+(r>>>0);return{h:s+t+(n/2**32|0)|0,l:n|0}}var qu=(s,e,t)=>(s>>>0)+(e>>>0)+(t>>>0),Ku=(s,e,t,r)=>e+t+r+(s/2**32|0)|0,Wu=(s,e,t,r)=>(s>>>0)+(e>>>0)+(t>>>0)+(r>>>0),$u=(s,e,t,r,n)=>e+t+r+n+(s/2**32|0)|0,ju=(s,e,t,r,n)=>(s>>>0)+(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0),Zu=(s,e,t,r,n,i)=>e+t+r+n+i+(s/2**32|0)|0;var Ju={fromBig:na,split:Tu,toBig:Mu,shrSH:Pu,shrSL:Du,rotrSH:Cu,rotrSL:Nu,rotrBH:Ru,rotrBL:Lu,rotr32H:Uu,rotr32L:Fu,rotlSH:Vu,rotlSL:Hu,rotlBH:Ou,rotlBL:zu,add:Gu,add3L:qu,add3H:Ku,add4L:Wu,add4H:$u,add5H:Zu,add5L:ju},R=Ju;var[Xu,Yu]=R.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(s=>BigInt(s))),xt=new Uint32Array(80),Et=new Uint32Array(80),ti=class extends cs{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){let{Ah:e,Al:t,Bh:r,Bl:n,Ch:i,Cl:o,Dh:a,Dl:c,Eh:h,El:f,Fh:l,Fl:g,Gh:u,Gl:d,Hh:m,Hl:p}=this;return[e,t,r,n,i,o,a,c,h,f,l,g,u,d,m,p]}set(e,t,r,n,i,o,a,c,h,f,l,g,u,d,m,p){this.Ah=e|0,this.Al=t|0,this.Bh=r|0,this.Bl=n|0,this.Ch=i|0,this.Cl=o|0,this.Dh=a|0,this.Dl=c|0,this.Eh=h|0,this.El=f|0,this.Fh=l|0,this.Fl=g|0,this.Gh=u|0,this.Gl=d|0,this.Hh=m|0,this.Hl=p|0}process(e,t){for(let w=0;w<16;w++,t+=4)xt[w]=e.getUint32(t),Et[w]=e.getUint32(t+=4);for(let w=16;w<80;w++){let I=xt[w-15]|0,x=Et[w-15]|0,B=R.rotrSH(I,x,1)^R.rotrSH(I,x,8)^R.shrSH(I,x,7),E=R.rotrSL(I,x,1)^R.rotrSL(I,x,8)^R.shrSL(I,x,7),_=xt[w-2]|0,k=Et[w-2]|0,z=R.rotrSH(_,k,19)^R.rotrBH(_,k,61)^R.shrSH(_,k,6),L=R.rotrSL(_,k,19)^R.rotrBL(_,k,61)^R.shrSL(_,k,6),U=R.add4L(E,L,Et[w-7],Et[w-16]),se=R.add4H(U,B,z,xt[w-7],xt[w-16]);xt[w]=se|0,Et[w]=U|0}let{Ah:r,Al:n,Bh:i,Bl:o,Ch:a,Cl:c,Dh:h,Dl:f,Eh:l,El:g,Fh:u,Fl:d,Gh:m,Gl:p,Hh:b,Hl:y}=this;for(let w=0;w<80;w++){let I=R.rotrSH(l,g,14)^R.rotrSH(l,g,18)^R.rotrBH(l,g,41),x=R.rotrSL(l,g,14)^R.rotrSL(l,g,18)^R.rotrBL(l,g,41),B=l&u^~l&m,E=g&d^~g&p,_=R.add5L(y,x,E,Yu[w],Et[w]),k=R.add5H(_,b,I,B,Xu[w],xt[w]),z=_|0,L=R.rotrSH(r,n,28)^R.rotrBH(r,n,34)^R.rotrBH(r,n,39),U=R.rotrSL(r,n,28)^R.rotrBL(r,n,34)^R.rotrBL(r,n,39),se=r&i^r&a^i&a,T=n&o^n&c^o&c;b=m|0,y=p|0,m=u|0,p=d|0,u=l|0,d=g|0,{h:l,l:g}=R.add(h|0,f|0,k|0,z|0),h=a|0,f=c|0,a=i|0,c=o|0,i=r|0,o=n|0;let P=R.add3L(z,U,T);r=R.add3H(P,k,L,se),n=P|0}({h:r,l:n}=R.add(this.Ah|0,this.Al|0,r|0,n|0)),{h:i,l:o}=R.add(this.Bh|0,this.Bl|0,i|0,o|0),{h:a,l:c}=R.add(this.Ch|0,this.Cl|0,a|0,c|0),{h,l:f}=R.add(this.Dh|0,this.Dl|0,h|0,f|0),{h:l,l:g}=R.add(this.Eh|0,this.El|0,l|0,g|0),{h:u,l:d}=R.add(this.Fh|0,this.Fl|0,u|0,d|0),{h:m,l:p}=R.add(this.Gh|0,this.Gl|0,m|0,p|0),{h:b,l:y}=R.add(this.Hh|0,this.Hl|0,b|0,y|0),this.set(r,n,i,o,a,c,h,f,l,g,u,d,m,p,b,y)}roundClean(){xt.fill(0),Et.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}};var ia=lr(()=>new ti);var fr={};ve(fr,{aInRange:()=>De,abool:()=>Ge,abytes:()=>ls,bitGet:()=>nh,bitLen:()=>oi,bitMask:()=>Rs,bitSet:()=>ih,bytesToHex:()=>ot,bytesToNumberBE:()=>at,bytesToNumberLE:()=>Bt,concatBytes:()=>ct,createHmacDrbg:()=>ai,ensureBytes:()=>ee,equalBytes:()=>sh,hexToBytes:()=>Ot,hexToNumber:()=>ii,inRange:()=>Ns,isBytes:()=>St,memoized:()=>Gt,notImplemented:()=>ah,numberToBytesBE:()=>It,numberToBytesLE:()=>zt,numberToHexUnpadded:()=>Ht,numberToVarBytesBE:()=>th,utf8ToBytes:()=>rh,validateObject:()=>Je});var ni=BigInt(0),hr=BigInt(1),Qu=BigInt(2);function St(s){return s instanceof Uint8Array||s!=null&&typeof s=="object"&&s.constructor.name==="Uint8Array"}function ls(s){if(!St(s))throw new Error("Uint8Array expected")}function Ge(s,e){if(typeof e!="boolean")throw new Error(`${s} must be valid boolean, got "${e}".`)}var eh=Array.from({length:256},(s,e)=>e.toString(16).padStart(2,"0"));function ot(s){ls(s);let e="";for(let t=0;t<s.length;t++)e+=eh[s[t]];return e}function Ht(s){let e=s.toString(16);return e.length&1?`0${e}`:e}function ii(s){if(typeof s!="string")throw new Error("hex string expected, got "+typeof s);return BigInt(s===""?"0":`0x${s}`)}var it={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function oa(s){if(s>=it._0&&s<=it._9)return s-it._0;if(s>=it._A&&s<=it._F)return s-(it._A-10);if(s>=it._a&&s<=it._f)return s-(it._a-10)}function Ot(s){if(typeof s!="string")throw new Error("hex string expected, got "+typeof s);let e=s.length,t=e/2;if(e%2)throw new Error("padded hex string expected, got unpadded hex of length "+e);let r=new Uint8Array(t);for(let n=0,i=0;n<t;n++,i+=2){let o=oa(s.charCodeAt(i)),a=oa(s.charCodeAt(i+1));if(o===void 0||a===void 0){let c=s[i]+s[i+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+i)}r[n]=o*16+a}return r}function at(s){return ii(ot(s))}function Bt(s){return ls(s),ii(ot(Uint8Array.from(s).reverse()))}function It(s,e){return Ot(s.toString(16).padStart(e*2,"0"))}function zt(s,e){return It(s,e).reverse()}function th(s){return Ot(Ht(s))}function ee(s,e,t){let r;if(typeof e=="string")try{r=Ot(e)}catch(i){throw new Error(`${s} must be valid hex string, got "${e}". Cause: ${i}`)}else if(St(e))r=Uint8Array.from(e);else throw new Error(`${s} must be hex string or Uint8Array`);let n=r.length;if(typeof t=="number"&&n!==t)throw new Error(`${s} expected ${t} bytes, got ${n}`);return r}function ct(...s){let e=0;for(let r=0;r<s.length;r++){let n=s[r];ls(n),e+=n.length}let t=new Uint8Array(e);for(let r=0,n=0;r<s.length;r++){let i=s[r];t.set(i,n),n+=i.length}return t}function sh(s,e){if(s.length!==e.length)return!1;let t=0;for(let r=0;r<s.length;r++)t|=s[r]^e[r];return t===0}function rh(s){if(typeof s!="string")throw new Error(`utf8ToBytes expected string, got ${typeof s}`);return new Uint8Array(new TextEncoder().encode(s))}var si=s=>typeof s=="bigint"&&ni<=s;function Ns(s,e,t){return si(s)&&si(e)&&si(t)&&e<=s&&s<t}function De(s,e,t,r){if(!Ns(e,t,r))throw new Error(`expected valid ${s}: ${t} <= n < ${r}, got ${typeof e} ${e}`)}function oi(s){let e;for(e=0;s>ni;s>>=hr,e+=1);return e}function nh(s,e){return s>>BigInt(e)&hr}function ih(s,e,t){return s|(t?hr:ni)<<BigInt(e)}var Rs=s=>(Qu<<BigInt(s-1))-hr,ri=s=>new Uint8Array(s),aa=s=>Uint8Array.from(s);function ai(s,e,t){if(typeof s!="number"||s<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof t!="function")throw new Error("hmacFn must be a function");let r=ri(s),n=ri(s),i=0,o=()=>{r.fill(1),n.fill(0),i=0},a=(...l)=>t(n,r,...l),c=(l=ri())=>{n=a(aa([0]),l),r=a(),l.length!==0&&(n=a(aa([1]),l),r=a())},h=()=>{if(i++>=1e3)throw new Error("drbg: tried 1000 values");let l=0,g=[];for(;l<e;){r=a();let u=r.slice();g.push(u),l+=r.length}return ct(...g)};return(l,g)=>{o(),c(l);let u;for(;!(u=g(h()));)c();return o(),u}}var oh={bigint:s=>typeof s=="bigint",function:s=>typeof s=="function",boolean:s=>typeof s=="boolean",string:s=>typeof s=="string",stringOrUint8Array:s=>typeof s=="string"||St(s),isSafeInteger:s=>Number.isSafeInteger(s),array:s=>Array.isArray(s),field:(s,e)=>e.Fp.isValid(s),hash:s=>typeof s=="function"&&Number.isSafeInteger(s.outputLen)};function Je(s,e,t={}){let r=(n,i,o)=>{let a=oh[i];if(typeof a!="function")throw new Error(`Invalid validator "${i}", expected function`);let c=s[n];if(!(o&&c===void 0)&&!a(c,s))throw new Error(`Invalid param ${String(n)}=${c} (${typeof c}), expected ${i}`)};for(let[n,i]of Object.entries(e))r(n,i,!1);for(let[n,i]of Object.entries(t))r(n,i,!0);return s}var ah=()=>{throw new Error("not implemented")};function Gt(s){let e=new WeakMap;return(t,...r)=>{let n=e.get(t);if(n!==void 0)return n;let i=s(t,...r);return e.set(t,i),i}}var ne=BigInt(0),Z=BigInt(1),qt=BigInt(2),ch=BigInt(3),ci=BigInt(4),ca=BigInt(5),la=BigInt(8),lh=BigInt(9),uh=BigInt(16);function W(s,e){let t=s%e;return t>=ne?t:e+t}function hh(s,e,t){if(t<=ne||e<ne)throw new Error("Expected power/modulo > 0");if(t===Z)return ne;let r=Z;for(;e>ne;)e&Z&&(r=r*s%t),s=s*s%t,e>>=Z;return r}function J(s,e,t){let r=s;for(;e-- >ne;)r*=r,r%=t;return r}function dr(s,e){if(s===ne||e<=ne)throw new Error(`invert: expected positive integers, got n=${s} mod=${e}`);let t=W(s,e),r=e,n=ne,i=Z,o=Z,a=ne;for(;t!==ne;){let h=r/t,f=r%t,l=n-o*h,g=i-a*h;r=t,t=f,n=o,i=a,o=l,a=g}if(r!==Z)throw new Error("invert: does not exist");return W(n,e)}function fh(s){let e=(s-Z)/qt,t,r,n;for(t=s-Z,r=0;t%qt===ne;t/=qt,r++);for(n=qt;n<s&&hh(n,e,s)!==s-Z;n++);if(r===1){let o=(s+Z)/ci;return function(c,h){let f=c.pow(h,o);if(!c.eql(c.sqr(f),h))throw new Error("Cannot find square root");return f}}let i=(t+Z)/qt;return function(a,c){if(a.pow(c,e)===a.neg(a.ONE))throw new Error("Cannot find square root");let h=r,f=a.pow(a.mul(a.ONE,n),t),l=a.pow(c,i),g=a.pow(c,t);for(;!a.eql(g,a.ONE);){if(a.eql(g,a.ZERO))return a.ZERO;let u=1;for(let m=a.sqr(g);u<h&&!a.eql(m,a.ONE);u++)m=a.sqr(m);let d=a.pow(f,Z<<BigInt(h-u-1));f=a.sqr(d),l=a.mul(l,d),g=a.mul(g,f),h=u}return l}}function dh(s){if(s%ci===ch){let e=(s+Z)/ci;return function(r,n){let i=r.pow(n,e);if(!r.eql(r.sqr(i),n))throw new Error("Cannot find square root");return i}}if(s%la===ca){let e=(s-ca)/la;return function(r,n){let i=r.mul(n,qt),o=r.pow(i,e),a=r.mul(n,o),c=r.mul(r.mul(a,qt),o),h=r.mul(a,r.sub(c,r.ONE));if(!r.eql(r.sqr(h),n))throw new Error("Cannot find square root");return h}}return s%uh,fh(s)}var ua=(s,e)=>(W(s,e)&Z)===Z,ph=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function li(s){let e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},t=ph.reduce((r,n)=>(r[n]="function",r),e);return Je(s,t)}function gh(s,e,t){if(t<ne)throw new Error("Expected power > 0");if(t===ne)return s.ONE;if(t===Z)return e;let r=s.ONE,n=e;for(;t>ne;)t&Z&&(r=s.mul(r,n)),n=s.sqr(n),t>>=Z;return r}function mh(s,e){let t=new Array(e.length),r=e.reduce((i,o,a)=>s.is0(o)?i:(t[a]=i,s.mul(i,o)),s.ONE),n=s.inv(r);return e.reduceRight((i,o,a)=>s.is0(o)?i:(t[a]=s.mul(i,t[a]),s.mul(i,o)),n),t}function ui(s,e){let t=e!==void 0?e:s.toString(2).length,r=Math.ceil(t/8);return{nBitLength:t,nByteLength:r}}function _t(s,e,t=!1,r={}){if(s<=ne)throw new Error(`Expected Field ORDER > 0, got ${s}`);let{nBitLength:n,nByteLength:i}=ui(s,e);if(i>2048)throw new Error("Field lengths over 2048 bytes are not supported");let o=dh(s),a=Object.freeze({ORDER:s,BITS:n,BYTES:i,MASK:Rs(n),ZERO:ne,ONE:Z,create:c=>W(c,s),isValid:c=>{if(typeof c!="bigint")throw new Error(`Invalid field element: expected bigint, got ${typeof c}`);return ne<=c&&c<s},is0:c=>c===ne,isOdd:c=>(c&Z)===Z,neg:c=>W(-c,s),eql:(c,h)=>c===h,sqr:c=>W(c*c,s),add:(c,h)=>W(c+h,s),sub:(c,h)=>W(c-h,s),mul:(c,h)=>W(c*h,s),pow:(c,h)=>gh(a,c,h),div:(c,h)=>W(c*dr(h,s),s),sqrN:c=>c*c,addN:(c,h)=>c+h,subN:(c,h)=>c-h,mulN:(c,h)=>c*h,inv:c=>dr(c,s),sqrt:r.sqrt||(c=>o(a,c)),invertBatch:c=>mh(a,c),cmov:(c,h,f)=>f?h:c,toBytes:c=>t?zt(c,i):It(c,i),fromBytes:c=>{if(c.length!==i)throw new Error(`Fp.fromBytes: expected ${i}, got ${c.length}`);return t?Bt(c):at(c)}});return Object.freeze(a)}function ha(s){if(typeof s!="bigint")throw new Error("field order must be bigint");let e=s.toString(2).length;return Math.ceil(e/8)}function hi(s){let e=ha(s);return e+Math.ceil(e/2)}function fa(s,e,t=!1){let r=s.length,n=ha(e),i=hi(e);if(r<16||r<i||r>1024)throw new Error(`expected ${i}-1024 bytes of input, got ${r}`);let o=t?at(s):Bt(s),a=W(o,e-Z)+Z;return t?zt(a,n):It(a,n)}var wh=BigInt(0),fi=BigInt(1),di=new WeakMap,da=new WeakMap;function pr(s,e){let t=(i,o)=>{let a=o.negate();return i?a:o},r=i=>{if(!Number.isSafeInteger(i)||i<=0||i>e)throw new Error(`Wrong window size=${i}, should be [1..${e}]`)},n=i=>{r(i);let o=Math.ceil(e/i)+1,a=2**(i-1);return{windows:o,windowSize:a}};return{constTimeNegate:t,unsafeLadder(i,o){let a=s.ZERO,c=i;for(;o>wh;)o&fi&&(a=a.add(c)),c=c.double(),o>>=fi;return a},precomputeWindow(i,o){let{windows:a,windowSize:c}=n(o),h=[],f=i,l=f;for(let g=0;g<a;g++){l=f,h.push(l);for(let u=1;u<c;u++)l=l.add(f),h.push(l);f=l.double()}return h},wNAF(i,o,a){let{windows:c,windowSize:h}=n(i),f=s.ZERO,l=s.BASE,g=BigInt(2**i-1),u=2**i,d=BigInt(i);for(let m=0;m<c;m++){let p=m*h,b=Number(a&g);a>>=d,b>h&&(b-=u,a+=fi);let y=p,w=p+Math.abs(b)-1,I=m%2!==0,x=b<0;b===0?l=l.add(t(I,o[y])):f=f.add(t(x,o[w]))}return{p:f,f:l}},wNAFCached(i,o,a){let c=da.get(i)||1,h=di.get(i);return h||(h=this.precomputeWindow(i,c),c!==1&&di.set(i,a(h))),this.wNAF(c,h,o)},setWindowSize(i,o){r(o),da.set(i,o),di.delete(i)}}}function gr(s,e,t,r){if(!Array.isArray(t)||!Array.isArray(r)||r.length!==t.length)throw new Error("arrays of points and scalars must have equal length");r.forEach((f,l)=>{if(!e.isValid(f))throw new Error(`wrong scalar at index ${l}`)}),t.forEach((f,l)=>{if(!(f instanceof s))throw new Error(`wrong point at index ${l}`)});let n=oi(BigInt(t.length)),i=n>12?n-3:n>4?n-2:n?2:1,o=(1<<i)-1,a=new Array(o+1).fill(s.ZERO),c=Math.floor((e.BITS-1)/i)*i,h=s.ZERO;for(let f=c;f>=0;f-=i){a.fill(s.ZERO);for(let g=0;g<r.length;g++){let u=r[g],d=Number(u>>BigInt(f)&BigInt(o));a[d]=a[d].add(t[g])}let l=s.ZERO;for(let g=a.length-1,u=s.ZERO;g>0;g--)u=u.add(a[g]),l=l.add(u);if(h=h.add(l),f!==0)for(let g=0;g<i;g++)h=h.double()}return h}function Ls(s){return li(s.Fp),Je(s,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...ui(s.n,s.nBitLength),...s,p:s.Fp.ORDER})}var qe=BigInt(0),Ce=BigInt(1),mr=BigInt(2),yh=BigInt(8),vh={zip215:!0};function xh(s){let e=Ls(s);return Je(s,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...e})}function pa(s){let e=xh(s),{Fp:t,n:r,prehash:n,hash:i,randomBytes:o,nByteLength:a,h:c}=e,h=mr<<BigInt(a*8)-Ce,f=t.create,l=_t(e.n,e.nBitLength),g=e.uvRatio||((S,v)=>{try{return{isValid:!0,value:t.sqrt(S*t.inv(v))}}catch{return{isValid:!1,value:qe}}}),u=e.adjustScalarBytes||(S=>S),d=e.domain||((S,v,A)=>{if(Ge("phflag",A),v.length||A)throw new Error("Contexts/pre-hash are not supported");return S});function m(S,v){De("coordinate "+S,v,qe,h)}function p(S){if(!(S instanceof w))throw new Error("ExtendedPoint expected")}let b=Gt((S,v)=>{let{ex:A,ey:D,ez:C}=S,N=S.is0();v==null&&(v=N?yh:t.inv(C));let F=f(A*v),O=f(D*v),V=f(C*v);if(N)return{x:qe,y:Ce};if(V!==Ce)throw new Error("invZ was invalid");return{x:F,y:O}}),y=Gt(S=>{let{a:v,d:A}=e;if(S.is0())throw new Error("bad point: ZERO");let{ex:D,ey:C,ez:N,et:F}=S,O=f(D*D),V=f(C*C),K=f(N*N),j=f(K*K),oe=f(O*v),ae=f(K*f(oe+V)),ue=f(j+f(A*f(O*V)));if(ae!==ue)throw new Error("bad point: equation left != right (1)");let ye=f(D*C),re=f(N*F);if(ye!==re)throw new Error("bad point: equation left != right (2)");return!0});class w{constructor(v,A,D,C){this.ex=v,this.ey=A,this.ez=D,this.et=C,m("x",v),m("y",A),m("z",D),m("t",C),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(v){if(v instanceof w)throw new Error("extended point not allowed");let{x:A,y:D}=v||{};return m("x",A),m("y",D),new w(A,D,Ce,f(A*D))}static normalizeZ(v){let A=t.invertBatch(v.map(D=>D.ez));return v.map((D,C)=>D.toAffine(A[C])).map(w.fromAffine)}static msm(v,A){return gr(w,l,v,A)}_setWindowSize(v){B.setWindowSize(this,v)}assertValidity(){y(this)}equals(v){p(v);let{ex:A,ey:D,ez:C}=this,{ex:N,ey:F,ez:O}=v,V=f(A*O),K=f(N*C),j=f(D*O),oe=f(F*C);return V===K&&j===oe}is0(){return this.equals(w.ZERO)}negate(){return new w(f(-this.ex),this.ey,this.ez,f(-this.et))}double(){let{a:v}=e,{ex:A,ey:D,ez:C}=this,N=f(A*A),F=f(D*D),O=f(mr*f(C*C)),V=f(v*N),K=A+D,j=f(f(K*K)-N-F),oe=V+F,ae=oe-O,ue=V-F,ye=f(j*ae),re=f(oe*ue),Me=f(j*ue),st=f(ae*oe);return new w(ye,re,st,Me)}add(v){p(v);let{a:A,d:D}=e,{ex:C,ey:N,ez:F,et:O}=this,{ex:V,ey:K,ez:j,et:oe}=v;if(A===BigInt(-1)){let Bo=f((N-C)*(K+V)),Io=f((N+C)*(K-V)),zn=f(Io-Bo);if(zn===qe)return this.double();let _o=f(F*mr*oe),Ao=f(O*mr*j),ko=Ao+_o,To=Io+Bo,Mo=Ao-_o,Dl=f(ko*zn),Cl=f(To*Mo),Nl=f(ko*Mo),Rl=f(zn*To);return new w(Dl,Cl,Rl,Nl)}let ae=f(C*V),ue=f(N*K),ye=f(O*D*oe),re=f(F*j),Me=f((C+N)*(V+K)-ae-ue),st=re-ye,ks=re+ye,Ts=f(ue-A*ae),kl=f(Me*st),Tl=f(ks*Ts),Ml=f(Me*Ts),Pl=f(st*ks);return new w(kl,Tl,Pl,Ml)}subtract(v){return this.add(v.negate())}wNAF(v){return B.wNAFCached(this,v,w.normalizeZ)}multiply(v){let A=v;De("scalar",A,Ce,r);let{p:D,f:C}=this.wNAF(A);return w.normalizeZ([D,C])[0]}multiplyUnsafe(v){let A=v;return De("scalar",A,qe,r),A===qe?x:this.equals(x)||A===Ce?this:this.equals(I)?this.wNAF(A).p:B.unsafeLadder(this,A)}isSmallOrder(){return this.multiplyUnsafe(c).is0()}isTorsionFree(){return B.unsafeLadder(this,r).is0()}toAffine(v){return b(this,v)}clearCofactor(){let{h:v}=e;return v===Ce?this:this.multiplyUnsafe(v)}static fromHex(v,A=!1){let{d:D,a:C}=e,N=t.BYTES;v=ee("pointHex",v,N),Ge("zip215",A);let F=v.slice(),O=v[N-1];F[N-1]=O&-129;let V=Bt(F),K=A?h:t.ORDER;De("pointHex.y",V,qe,K);let j=f(V*V),oe=f(j-Ce),ae=f(D*j-C),{isValid:ue,value:ye}=g(oe,ae);if(!ue)throw new Error("Point.fromHex: invalid y coordinate");let re=(ye&Ce)===Ce,Me=(O&128)!==0;if(!A&&ye===qe&&Me)throw new Error("Point.fromHex: x=0 and x_0=1");return Me!==re&&(ye=f(-ye)),w.fromAffine({x:ye,y:V})}static fromPrivateKey(v){return k(v).point}toRawBytes(){let{x:v,y:A}=this.toAffine(),D=zt(A,t.BYTES);return D[D.length-1]|=v&Ce?128:0,D}toHex(){return ot(this.toRawBytes())}}w.BASE=new w(e.Gx,e.Gy,Ce,f(e.Gx*e.Gy)),w.ZERO=new w(qe,Ce,Ce,qe);let{BASE:I,ZERO:x}=w,B=pr(w,a*8);function E(S){return W(S,r)}function _(S){return E(Bt(S))}function k(S){let v=a;S=ee("private key",S,v);let A=ee("hashed private key",i(S),2*v),D=u(A.slice(0,v)),C=A.slice(v,2*v),N=_(D),F=I.multiply(N),O=F.toRawBytes();return{head:D,prefix:C,scalar:N,point:F,pointBytes:O}}function z(S){return k(S).pointBytes}function L(S=new Uint8Array,...v){let A=ct(...v);return _(i(d(A,ee("context",S),!!n)))}function U(S,v,A={}){S=ee("message",S),n&&(S=n(S));let{prefix:D,scalar:C,pointBytes:N}=k(v),F=L(A.context,D,S),O=I.multiply(F).toRawBytes(),V=L(A.context,O,N,S),K=E(F+V*C);De("signature.s",K,qe,r);let j=ct(O,zt(K,t.BYTES));return ee("result",j,a*2)}let se=vh;function T(S,v,A,D=se){let{context:C,zip215:N}=D,F=t.BYTES;S=ee("signature",S,2*F),v=ee("message",v),N!==void 0&&Ge("zip215",N),n&&(v=n(v));let O=Bt(S.slice(F,2*F)),V,K,j;try{V=w.fromHex(A,N),K=w.fromHex(S.slice(0,F),N),j=I.multiplyUnsafe(O)}catch{return!1}if(!N&&V.isSmallOrder())return!1;let oe=L(C,K.toRawBytes(),V.toRawBytes(),v);return K.add(V.multiplyUnsafe(oe)).subtract(j).clearCofactor().equals(w.ZERO)}return I._setWindowSize(8),{CURVE:e,getPublicKey:z,sign:U,verify:T,ExtendedPoint:w,utils:{getExtendedPublicKey:k,randomPrivateKey:()=>o(t.BYTES),precompute(S=8,v=w.BASE){return v._setWindowSize(S),v.multiply(BigInt(3)),v}}}}var pi=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),ga=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752"),ug=BigInt(0),Eh=BigInt(1),ma=BigInt(2),hg=BigInt(3),Sh=BigInt(5),Bh=BigInt(8);function Ih(s){let e=BigInt(10),t=BigInt(20),r=BigInt(40),n=BigInt(80),i=pi,a=s*s%i*s%i,c=J(a,ma,i)*a%i,h=J(c,Eh,i)*s%i,f=J(h,Sh,i)*h%i,l=J(f,e,i)*f%i,g=J(l,t,i)*l%i,u=J(g,r,i)*g%i,d=J(u,n,i)*u%i,m=J(d,n,i)*u%i,p=J(m,e,i)*f%i;return{pow_p_5_8:J(p,ma,i)*s%i,b2:a}}function _h(s){return s[0]&=248,s[31]&=127,s[31]|=64,s}function Ah(s,e){let t=pi,r=W(e*e*e,t),n=W(r*r*e,t),i=Ih(s*n).pow_p_5_8,o=W(s*r*i,t),a=W(e*o*o,t),c=o,h=W(o*ga,t),f=a===s,l=a===W(-s,t),g=a===W(-s*ga,t);return f&&(o=c),(l||g)&&(o=h),ua(o,t)&&(o=W(-o,t)),{isValid:f||l,value:o}}var kh=_t(pi,void 0,!0),Th={a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:kh,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:Bh,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:ia,randomBytes:as,adjustScalarBytes:_h,uvRatio:Ah},ba=pa(Th);var br=32;function wa(s,e,t){return ba.verify(e,t instanceof Uint8Array?t:t.subarray(),s)}var wr=class{type="Ed25519";raw;constructor(e){this.raw=gi(e,br)}toMultihash(){return nt.digest(At(this))}toCID(){return Pe.createV1(114,this.toMultihash())}toString(){return te.encode(this.toMultihash().bytes).substring(1)}equals(e){return e==null||!(e.raw instanceof Uint8Array)?!1:Fe(this.raw,e.raw)}verify(e,t){return wa(this.raw,t,e)}};function mi(s){return s=gi(s,br),new wr(s)}function gi(s,e){if(s=Uint8Array.from(s??[]),s.length!==e)throw new ce(`Key must be a Uint8Array of length ${e}, got ${s.length}`);return s}function lt(s=0){return new Uint8Array(s)}function de(s=0){return new Uint8Array(s)}var Ph=Math.pow(2,7),Dh=Math.pow(2,14),Ch=Math.pow(2,21),bi=Math.pow(2,28),wi=Math.pow(2,35),yi=Math.pow(2,42),vi=Math.pow(2,49),G=128,pe=127;function Ee(s){if(s<Ph)return 1;if(s<Dh)return 2;if(s<Ch)return 3;if(s<bi)return 4;if(s<wi)return 5;if(s<yi)return 6;if(s<vi)return 7;if(Number.MAX_SAFE_INTEGER!=null&&s>Number.MAX_SAFE_INTEGER)throw new RangeError("Could not encode varint");return 8}function xi(s,e,t=0){switch(Ee(s)){case 8:e[t++]=s&255|G,s/=128;case 7:e[t++]=s&255|G,s/=128;case 6:e[t++]=s&255|G,s/=128;case 5:e[t++]=s&255|G,s/=128;case 4:e[t++]=s&255|G,s>>>=7;case 3:e[t++]=s&255|G,s>>>=7;case 2:e[t++]=s&255|G,s>>>=7;case 1:{e[t++]=s&255,s>>>=7;break}default:throw new Error("unreachable")}return e}function Nh(s,e,t=0){switch(Ee(s)){case 8:e.set(t++,s&255|G),s/=128;case 7:e.set(t++,s&255|G),s/=128;case 6:e.set(t++,s&255|G),s/=128;case 5:e.set(t++,s&255|G),s/=128;case 4:e.set(t++,s&255|G),s>>>=7;case 3:e.set(t++,s&255|G),s>>>=7;case 2:e.set(t++,s&255|G),s>>>=7;case 1:{e.set(t++,s&255),s>>>=7;break}default:throw new Error("unreachable")}return e}function Ei(s,e){let t=s[e],r=0;if(r+=t&pe,t<G||(t=s[e+1],r+=(t&pe)<<7,t<G)||(t=s[e+2],r+=(t&pe)<<14,t<G)||(t=s[e+3],r+=(t&pe)<<21,t<G)||(t=s[e+4],r+=(t&pe)*bi,t<G)||(t=s[e+5],r+=(t&pe)*wi,t<G)||(t=s[e+6],r+=(t&pe)*yi,t<G)||(t=s[e+7],r+=(t&pe)*vi,t<G))return r;throw new RangeError("Could not decode varint")}function Rh(s,e){let t=s.get(e),r=0;if(r+=t&pe,t<G||(t=s.get(e+1),r+=(t&pe)<<7,t<G)||(t=s.get(e+2),r+=(t&pe)<<14,t<G)||(t=s.get(e+3),r+=(t&pe)<<21,t<G)||(t=s.get(e+4),r+=(t&pe)*bi,t<G)||(t=s.get(e+5),r+=(t&pe)*wi,t<G)||(t=s.get(e+6),r+=(t&pe)*yi,t<G)||(t=s.get(e+7),r+=(t&pe)*vi,t<G))return r;throw new RangeError("Could not decode varint")}function Si(s,e,t=0){return e==null&&(e=de(Ee(s))),e instanceof Uint8Array?xi(s,e,t):Nh(s,e,t)}function us(s,e=0){return s instanceof Uint8Array?Ei(s,e):Rh(s,e)}var Ii=new Float32Array([-0]),kt=new Uint8Array(Ii.buffer);function va(s,e,t){Ii[0]=s,e[t]=kt[0],e[t+1]=kt[1],e[t+2]=kt[2],e[t+3]=kt[3]}function xa(s,e){return kt[0]=s[e],kt[1]=s[e+1],kt[2]=s[e+2],kt[3]=s[e+3],Ii[0]}var _i=new Float64Array([-0]),ge=new Uint8Array(_i.buffer);function Ea(s,e,t){_i[0]=s,e[t]=ge[0],e[t+1]=ge[1],e[t+2]=ge[2],e[t+3]=ge[3],e[t+4]=ge[4],e[t+5]=ge[5],e[t+6]=ge[6],e[t+7]=ge[7]}function Sa(s,e){return ge[0]=s[e],ge[1]=s[e+1],ge[2]=s[e+2],ge[3]=s[e+3],ge[4]=s[e+4],ge[5]=s[e+5],ge[6]=s[e+6],ge[7]=s[e+7],_i[0]}var Lh=BigInt(Number.MAX_SAFE_INTEGER),Uh=BigInt(Number.MIN_SAFE_INTEGER),Le=class s{lo;hi;constructor(e,t){this.lo=e|0,this.hi=t|0}toNumber(e=!1){if(!e&&this.hi>>>31>0){let t=~this.lo+1>>>0,r=~this.hi>>>0;return t===0&&(r=r+1>>>0),-(t+r*4294967296)}return this.lo+this.hi*4294967296}toBigInt(e=!1){if(e)return BigInt(this.lo>>>0)+(BigInt(this.hi>>>0)<<32n);if(this.hi>>>31){let t=~this.lo+1>>>0,r=~this.hi>>>0;return t===0&&(r=r+1>>>0),-(BigInt(t)+(BigInt(r)<<32n))}return BigInt(this.lo>>>0)+(BigInt(this.hi>>>0)<<32n)}toString(e=!1){return this.toBigInt(e).toString()}zzEncode(){let e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this}zzDecode(){let e=-(this.lo&1);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this}length(){let e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return r===0?t===0?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:r<128?9:10}static fromBigInt(e){if(e===0n)return Kt;if(e<Lh&&e>Uh)return this.fromNumber(Number(e));let t=e<0n;t&&(e=-e);let r=e>>32n,n=e-(r<<32n);return t&&(r=~r|0n,n=~n|0n,++n>Ba&&(n=0n,++r>Ba&&(r=0n))),new s(Number(n),Number(r))}static fromNumber(e){if(e===0)return Kt;let t=e<0;t&&(e=-e);let r=e>>>0,n=(e-r)/4294967296>>>0;return t&&(n=~n>>>0,r=~r>>>0,++r>4294967295&&(r=0,++n>4294967295&&(n=0))),new s(r,n)}static from(e){return typeof e=="number"?s.fromNumber(e):typeof e=="bigint"?s.fromBigInt(e):typeof e=="string"?s.fromBigInt(BigInt(e)):e.low!=null||e.high!=null?new s(e.low>>>0,e.high>>>0):Kt}},Kt=new Le(0,0);Kt.toBigInt=function(){return 0n};Kt.zzEncode=Kt.zzDecode=function(){return this};Kt.length=function(){return 1};var Ba=4294967296n;function Ia(s){let e=0,t=0;for(let r=0;r<s.length;++r)t=s.charCodeAt(r),t<128?e+=1:t<2048?e+=2:(t&64512)===55296&&(s.charCodeAt(r+1)&64512)===56320?(++r,e+=4):e+=3;return e}function _a(s,e,t){if(t-e<1)return"";let n,i=[],o=0,a;for(;e<t;)a=s[e++],a<128?i[o++]=a:a>191&&a<224?i[o++]=(a&31)<<6|s[e++]&63:a>239&&a<365?(a=((a&7)<<18|(s[e++]&63)<<12|(s[e++]&63)<<6|s[e++]&63)-65536,i[o++]=55296+(a>>10),i[o++]=56320+(a&1023)):i[o++]=(a&15)<<12|(s[e++]&63)<<6|s[e++]&63,o>8191&&((n??(n=[])).push(String.fromCharCode.apply(String,i)),o=0);return n!=null?(o>0&&n.push(String.fromCharCode.apply(String,i.slice(0,o))),n.join("")):String.fromCharCode.apply(String,i.slice(0,o))}function Ai(s,e,t){let r=t,n,i;for(let o=0;o<s.length;++o)n=s.charCodeAt(o),n<128?e[t++]=n:n<2048?(e[t++]=n>>6|192,e[t++]=n&63|128):(n&64512)===55296&&((i=s.charCodeAt(o+1))&64512)===56320?(n=65536+((n&1023)<<10)+(i&1023),++o,e[t++]=n>>18|240,e[t++]=n>>12&63|128,e[t++]=n>>6&63|128,e[t++]=n&63|128):(e[t++]=n>>12|224,e[t++]=n>>6&63|128,e[t++]=n&63|128);return t-r}function Ke(s,e){return RangeError(`index out of range: ${s.pos} + ${e??1} > ${s.len}`)}function yr(s,e){return(s[e-4]|s[e-3]<<8|s[e-2]<<16|s[e-1]<<24)>>>0}var ki=class{buf;pos;len;_slice=Uint8Array.prototype.subarray;constructor(e){this.buf=e,this.pos=0,this.len=e.length}uint32(){let e=4294967295;if(e=(this.buf[this.pos]&127)>>>0,this.buf[this.pos++]<128||(e=(e|(this.buf[this.pos]&127)<<7)>>>0,this.buf[this.pos++]<128)||(e=(e|(this.buf[this.pos]&127)<<14)>>>0,this.buf[this.pos++]<128)||(e=(e|(this.buf[this.pos]&127)<<21)>>>0,this.buf[this.pos++]<128)||(e=(e|(this.buf[this.pos]&15)<<28)>>>0,this.buf[this.pos++]<128))return e;if((this.pos+=5)>this.len)throw this.pos=this.len,Ke(this,10);return e}int32(){return this.uint32()|0}sint32(){let e=this.uint32();return e>>>1^-(e&1)|0}bool(){return this.uint32()!==0}fixed32(){if(this.pos+4>this.len)throw Ke(this,4);return yr(this.buf,this.pos+=4)}sfixed32(){if(this.pos+4>this.len)throw Ke(this,4);return yr(this.buf,this.pos+=4)|0}float(){if(this.pos+4>this.len)throw Ke(this,4);let e=xa(this.buf,this.pos);return this.pos+=4,e}double(){if(this.pos+8>this.len)throw Ke(this,4);let e=Sa(this.buf,this.pos);return this.pos+=8,e}bytes(){let e=this.uint32(),t=this.pos,r=this.pos+e;if(r>this.len)throw Ke(this,e);return this.pos+=e,t===r?new Uint8Array(0):this.buf.subarray(t,r)}string(){let e=this.bytes();return _a(e,0,e.length)}skip(e){if(typeof e=="number"){if(this.pos+e>this.len)throw Ke(this,e);this.pos+=e}else do if(this.pos>=this.len)throw Ke(this);while(this.buf[this.pos++]&128);return this}skipType(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;(e=this.uint32()&7)!==4;)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error(`invalid wire type ${e} at offset ${this.pos}`)}return this}readLongVarint(){let e=new Le(0,0),t=0;if(this.len-this.pos>4){for(;t<4;++t)if(e.lo=(e.lo|(this.buf[this.pos]&127)<<t*7)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(this.buf[this.pos]&127)<<28)>>>0,e.hi=(e.hi|(this.buf[this.pos]&127)>>4)>>>0,this.buf[this.pos++]<128)return e;t=0}else{for(;t<3;++t){if(this.pos>=this.len)throw Ke(this);if(e.lo=(e.lo|(this.buf[this.pos]&127)<<t*7)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(this.buf[this.pos++]&127)<<t*7)>>>0,e}if(this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(this.buf[this.pos]&127)<<t*7+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw Ke(this);if(e.hi=(e.hi|(this.buf[this.pos]&127)<<t*7+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}readFixed64(){if(this.pos+8>this.len)throw Ke(this,8);let e=yr(this.buf,this.pos+=4),t=yr(this.buf,this.pos+=4);return new Le(e,t)}int64(){return this.readLongVarint().toBigInt()}int64Number(){return this.readLongVarint().toNumber()}int64String(){return this.readLongVarint().toString()}uint64(){return this.readLongVarint().toBigInt(!0)}uint64Number(){let e=Ei(this.buf,this.pos);return this.pos+=Ee(e),e}uint64String(){return this.readLongVarint().toString(!0)}sint64(){return this.readLongVarint().zzDecode().toBigInt()}sint64Number(){return this.readLongVarint().zzDecode().toNumber()}sint64String(){return this.readLongVarint().zzDecode().toString()}fixed64(){return this.readFixed64().toBigInt()}fixed64Number(){return this.readFixed64().toNumber()}fixed64String(){return this.readFixed64().toString()}sfixed64(){return this.readFixed64().toBigInt()}sfixed64Number(){return this.readFixed64().toNumber()}sfixed64String(){return this.readFixed64().toString()}};function Ti(s){return new ki(s instanceof Uint8Array?s:s.subarray())}function Se(s,e,t){let r=Ti(s);return e.decode(r,void 0,t)}var Mi={};ve(Mi,{base10:()=>Fh});var Fh=vt({prefix:"9",name:"base10",alphabet:"0123456789"});var Pi={};ve(Pi,{base16:()=>Vh,base16upper:()=>Hh});var Vh=Q({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Hh=Q({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var Di={};ve(Di,{base2:()=>Oh});var Oh=Q({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var Ci={};ve(Ci,{base256emoji:()=>Wh});var ka=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),zh=ka.reduce((s,e,t)=>(s[t]=e,s),[]),Gh=ka.reduce((s,e,t)=>(s[e.codePointAt(0)]=t,s),[]);function qh(s){return s.reduce((e,t)=>(e+=zh[t],e),"")}function Kh(s){let e=[];for(let t of s){let r=Gh[t.codePointAt(0)];if(r===void 0)throw new Error(`Non-base256emoji character: ${t}`);e.push(r)}return new Uint8Array(e)}var Wh=es({prefix:"\u{1F680}",name:"base256emoji",encode:qh,decode:Kh});var Ni={};ve(Ni,{base36:()=>$h,base36upper:()=>jh});var $h=vt({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),jh=vt({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var Ri={};ve(Ri,{base64:()=>Zh,base64pad:()=>Jh,base64url:()=>Xh,base64urlpad:()=>Yh});var Zh=Q({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),Jh=Q({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),Xh=Q({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),Yh=Q({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var Li={};ve(Li,{base8:()=>Qh});var Qh=Q({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var Ui={};ve(Ui,{identity:()=>ef});var ef=es({prefix:"\0",name:"identity",encode:s=>Vo(s),decode:s=>Fo(s)});var jg=new TextEncoder,Zg=new TextDecoder;var Hi={};ve(Hi,{sha256:()=>Tt,sha512:()=>rf});function Vi({name:s,code:e,encode:t}){return new Fi(s,e,t)}var Fi=class{name;code;encode;constructor(e,t,r){this.name=e,this.code=t,this.encode=r}digest(e){if(e instanceof Uint8Array){let t=this.encode(e);return t instanceof Uint8Array?He(this.code,t):t.then(r=>He(this.code,r))}else throw Error("Unknown type, must be binary type")}};function Ma(s){return async e=>new Uint8Array(await crypto.subtle.digest(s,e))}var Tt=Vi({name:"sha2-256",code:18,encode:Ma("SHA-256")}),rf=Vi({name:"sha2-512",code:19,encode:Ma("SHA-512")});var Us={...Ui,...Di,...Li,...Mi,...Pi,...Zn,...Ni,...jn,...Ri,...Ci},am={...Hi,...Yn};function Da(s,e,t,r){return{name:s,prefix:e,encoder:{name:s,prefix:e,encode:t},decoder:{decode:r}}}var Pa=Da("utf8","u",s=>"u"+new TextDecoder("utf8").decode(s),s=>new TextEncoder().encode(s.substring(1))),Oi=Da("ascii","a",s=>{let e="a";for(let t=0;t<s.length;t++)e+=String.fromCharCode(s[t]);return e},s=>{s=s.substring(1);let e=de(s.length);for(let t=0;t<s.length;t++)e[t]=s.charCodeAt(t);return e}),nf={utf8:Pa,"utf-8":Pa,hex:Us.base16,latin1:Oi,ascii:Oi,binary:Oi,...Us},vr=nf;function X(s,e="utf8"){let t=vr[e];if(t==null)throw new Error(`Unsupported encoding "${e}"`);return t.decoder.decode(`${t.prefix}${s}`)}function zi(s){let e=s??8192,t=e>>>1,r,n=e;return function(o){if(o<1||o>t)return de(o);n+o>e&&(r=de(e),n=0);let a=r.subarray(n,n+=o);return n&7&&(n=(n|7)+1),a}}var Wt=class{fn;len;next;val;constructor(e,t,r){this.fn=e,this.len=t,this.next=void 0,this.val=r}};function Gi(){}var Ki=class{head;tail;len;next;constructor(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}},of=zi();function af(s){return globalThis.Buffer!=null?de(s):of(s)}var Vs=class{len;head;tail;states;constructor(){this.len=0,this.head=new Wt(Gi,0,0),this.tail=this.head,this.states=null}_push(e,t,r){return this.tail=this.tail.next=new Wt(e,t,r),this.len+=t,this}uint32(e){return this.len+=(this.tail=this.tail.next=new Wi((e=e>>>0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this}int32(e){return e<0?this._push(xr,10,Le.fromNumber(e)):this.uint32(e)}sint32(e){return this.uint32((e<<1^e>>31)>>>0)}uint64(e){let t=Le.fromBigInt(e);return this._push(xr,t.length(),t)}uint64Number(e){return this._push(xi,Ee(e),e)}uint64String(e){return this.uint64(BigInt(e))}int64(e){return this.uint64(e)}int64Number(e){return this.uint64Number(e)}int64String(e){return this.uint64String(e)}sint64(e){let t=Le.fromBigInt(e).zzEncode();return this._push(xr,t.length(),t)}sint64Number(e){let t=Le.fromNumber(e).zzEncode();return this._push(xr,t.length(),t)}sint64String(e){return this.sint64(BigInt(e))}bool(e){return this._push(qi,1,e?1:0)}fixed32(e){return this._push(Fs,4,e>>>0)}sfixed32(e){return this.fixed32(e)}fixed64(e){let t=Le.fromBigInt(e);return this._push(Fs,4,t.lo)._push(Fs,4,t.hi)}fixed64Number(e){let t=Le.fromNumber(e);return this._push(Fs,4,t.lo)._push(Fs,4,t.hi)}fixed64String(e){return this.fixed64(BigInt(e))}sfixed64(e){return this.fixed64(e)}sfixed64Number(e){return this.fixed64Number(e)}sfixed64String(e){return this.fixed64String(e)}float(e){return this._push(va,4,e)}double(e){return this._push(Ea,8,e)}bytes(e){let t=e.length>>>0;return t===0?this._push(qi,1,0):this.uint32(t)._push(lf,t,e)}string(e){let t=Ia(e);return t!==0?this.uint32(t)._push(Ai,t,e):this._push(qi,1,0)}fork(){return this.states=new Ki(this),this.head=this.tail=new Wt(Gi,0,0),this.len=0,this}reset(){return this.states!=null?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new Wt(Gi,0,0),this.len=0),this}ldelim(){let e=this.head,t=this.tail,r=this.len;return this.reset().uint32(r),r!==0&&(this.tail.next=e.next,this.tail=t,this.len+=r),this}finish(){let e=this.head.next,t=af(this.len),r=0;for(;e!=null;)e.fn(e.val,t,r),r+=e.len,e=e.next;return t}};function qi(s,e,t){e[t]=s&255}function cf(s,e,t){for(;s>127;)e[t++]=s&127|128,s>>>=7;e[t]=s}var Wi=class extends Wt{next;constructor(e,t){super(cf,e,t),this.next=void 0}};function xr(s,e,t){for(;s.hi!==0;)e[t++]=s.lo&127|128,s.lo=(s.lo>>>7|s.hi<<25)>>>0,s.hi>>>=7;for(;s.lo>127;)e[t++]=s.lo&127|128,s.lo=s.lo>>>7;e[t++]=s.lo}function Fs(s,e,t){e[t]=s&255,e[t+1]=s>>>8&255,e[t+2]=s>>>16&255,e[t+3]=s>>>24}function lf(s,e,t){e.set(s,t)}globalThis.Buffer!=null&&(Vs.prototype.bytes=function(s){let e=s.length>>>0;return this.uint32(e),e>0&&this._push(uf,e,s),this},Vs.prototype.string=function(s){let e=globalThis.Buffer.byteLength(s);return this.uint32(e),e>0&&this._push(hf,e,s),this});function uf(s,e,t){e.set(s,t)}function hf(s,e,t){s.length<40?Ai(s,e,t):e.utf8Write!=null?e.utf8Write(s,t):e.set(X(s),t)}function $i(){return new Vs}function Be(s,e){let t=$i();return e.encode(s,t,{lengthDelimited:!1}),t.finish()}var hs;(function(s){s[s.VARINT=0]="VARINT",s[s.BIT64=1]="BIT64",s[s.LENGTH_DELIMITED=2]="LENGTH_DELIMITED",s[s.START_GROUP=3]="START_GROUP",s[s.END_GROUP=4]="END_GROUP",s[s.BIT32=5]="BIT32"})(hs||(hs={}));function Er(s,e,t,r){return{name:s,type:e,encode:t,decode:r}}function ji(s){function e(n){if(s[n.toString()]==null)throw new Error("Invalid enum value");return s[n]}let t=function(i,o){let a=e(i);o.int32(a)},r=function(i){let o=i.int32();return e(o)};return Er("enum",hs.VARINT,t,r)}function Ie(s,e){return Er("message",hs.LENGTH_DELIMITED,s,e)}var Ne=class extends Error{code="ERR_MAX_LENGTH";name="MaxLengthError"};var ie;(function(s){s.RSA="RSA",s.Ed25519="Ed25519",s.secp256k1="secp256k1"})(ie||(ie={}));var Zi;(function(s){s[s.RSA=0]="RSA",s[s.Ed25519=1]="Ed25519",s[s.secp256k1=2]="secp256k1"})(Zi||(Zi={}));(function(s){s.codec=()=>ji(Zi)})(ie||(ie={}));var Xe;(function(s){let e;s.codec=()=>(e==null&&(e=Ie((t,r,n={})=>{n.lengthDelimited!==!1&&r.fork(),t.Type!=null&&(r.uint32(8),ie.codec().encode(t.Type,r)),t.Data!=null&&(r.uint32(18),r.bytes(t.Data)),n.lengthDelimited!==!1&&r.ldelim()},(t,r,n={})=>{let i={},o=r==null?t.len:t.pos+r;for(;t.pos<o;){let a=t.uint32();switch(a>>>3){case 1:{i.Type=ie.codec().decode(t);break}case 2:{i.Data=t.bytes();break}default:{t.skipType(a&7);break}}}return i})),e),s.encode=t=>Be(t,s.codec()),s.decode=(t,r)=>Se(t,s.codec(),r)})(Xe||(Xe={}));var Ji;(function(s){let e;s.codec=()=>(e==null&&(e=Ie((t,r,n={})=>{n.lengthDelimited!==!1&&r.fork(),t.Type!=null&&(r.uint32(8),ie.codec().encode(t.Type,r)),t.Data!=null&&(r.uint32(18),r.bytes(t.Data)),n.lengthDelimited!==!1&&r.ldelim()},(t,r,n={})=>{let i={},o=r==null?t.len:t.pos+r;for(;t.pos<o;){let a=t.uint32();switch(a>>>3){case 1:{i.Type=ie.codec().decode(t);break}case 2:{i.Data=t.bytes();break}default:{t.skipType(a&7);break}}}return i})),e),s.encode=t=>Be(t,s.codec()),s.decode=(t,r)=>Se(t,s.codec(),r)})(Ji||(Ji={}));var Zs={};ve(Zs,{MAX_RSA_KEY_SIZE:()=>gn,generateRSAKeyPair:()=>Tc,jwkToJWKKeyPair:()=>Mc,jwkToPkcs1:()=>Af,jwkToPkix:()=>io,jwkToRSAPrivateKey:()=>kc,pkcs1ToJwk:()=>Ic,pkcs1ToRSAPrivateKey:()=>Ac,pkixToJwk:()=>_c,pkixToRSAPublicKey:()=>oo});var ff=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Mt=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Pt=new Uint32Array(64),Xi=class extends cs{constructor(){super(64,32,8,!1),this.A=Mt[0]|0,this.B=Mt[1]|0,this.C=Mt[2]|0,this.D=Mt[3]|0,this.E=Mt[4]|0,this.F=Mt[5]|0,this.G=Mt[6]|0,this.H=Mt[7]|0}get(){let{A:e,B:t,C:r,D:n,E:i,F:o,G:a,H:c}=this;return[e,t,r,n,i,o,a,c]}set(e,t,r,n,i,o,a,c){this.A=e|0,this.B=t|0,this.C=r|0,this.D=n|0,this.E=i|0,this.F=o|0,this.G=a|0,this.H=c|0}process(e,t){for(let l=0;l<16;l++,t+=4)Pt[l]=e.getUint32(t,!1);for(let l=16;l<64;l++){let g=Pt[l-15],u=Pt[l-2],d=ze(g,7)^ze(g,18)^g>>>3,m=ze(u,17)^ze(u,19)^u>>>10;Pt[l]=m+Pt[l-7]+d+Pt[l-16]|0}let{A:r,B:n,C:i,D:o,E:a,F:c,G:h,H:f}=this;for(let l=0;l<64;l++){let g=ze(a,6)^ze(a,11)^ze(a,25),u=f+g+sa(a,c,h)+ff[l]+Pt[l]|0,m=(ze(r,2)^ze(r,13)^ze(r,22))+ra(r,n,i)|0;f=h,h=c,c=a,a=o+u|0,o=i,i=n,n=r,r=u+m|0}r=r+this.A|0,n=n+this.B|0,i=i+this.C|0,o=o+this.D|0,a=a+this.E|0,c=c+this.F|0,h=h+this.G|0,f=f+this.H|0,this.set(r,n,i,o,a,c,h,f)}roundClean(){Pt.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}};var fs=lr(()=>new Xi);var H=Co(Na());function $t(s,e){let t=0;if(s.length===1)return s[0];for(let r=s.length-1;r>=0;r--)t+=s[s.length-1-r]*Math.pow(2,e*r);return t}function Dt(s,e,t=-1){let r=t,n=s,i=0,o=Math.pow(2,e);for(let a=1;a<8;a++){if(s<o){let c;if(r<0)c=new ArrayBuffer(a),i=a;else{if(r<a)return new ArrayBuffer(0);c=new ArrayBuffer(r),i=r}let h=new Uint8Array(c);for(let f=a-1;f>=0;f--){let l=Math.pow(2,f*e);h[i-f-1]=Math.floor(n/l),n-=h[i-f-1]*l}return c}o*=Math.pow(2,e)}return new ArrayBuffer(0)}function Ir(...s){let e=0,t=0;for(let i of s)e+=i.length;let r=new ArrayBuffer(e),n=new Uint8Array(r);for(let i of s)n.set(i,t),t+=i.length;return n}function Qi(){let s=new Uint8Array(this.valueHex);if(this.valueHex.byteLength>=2){let a=s[0]===255&&s[1]&128,c=s[0]===0&&(s[1]&128)===0;(a||c)&&this.warnings.push("Needlessly long format")}let e=new ArrayBuffer(this.valueHex.byteLength),t=new Uint8Array(e);for(let a=0;a<this.valueHex.byteLength;a++)t[a]=0;t[0]=s[0]&128;let r=$t(t,8),n=new ArrayBuffer(this.valueHex.byteLength),i=new Uint8Array(n);for(let a=0;a<this.valueHex.byteLength;a++)i[a]=s[a];return i[0]&=127,$t(i,8)-r}function Ra(s){let e=s<0?s*-1:s,t=128;for(let r=1;r<8;r++){if(e<=t){if(s<0){let o=t-e,a=Dt(o,8,r),c=new Uint8Array(a);return c[0]|=128,a}let n=Dt(e,8,r),i=new Uint8Array(n);if(i[0]&128){let o=n.slice(0),a=new Uint8Array(o);n=new ArrayBuffer(n.byteLength+1),i=new Uint8Array(n);for(let c=0;c<o.byteLength;c++)i[c+1]=a[c];i[0]=0}return n}t*=Math.pow(2,8)}return new ArrayBuffer(0)}function La(s,e){if(s.byteLength!==e.byteLength)return!1;let t=new Uint8Array(s),r=new Uint8Array(e);for(let n=0;n<t.length;n++)if(t[n]!==r[n])return!1;return!0}function Re(s,e){let t=s.toString(10);if(e<t.length)return"";let r=e-t.length,n=new Array(r);for(let o=0;o<r;o++)n[o]="0";return n.join("").concat(t)}var zm=Math.log(2);function _r(){if(typeof BigInt>"u")throw new Error("BigInt is not defined. Your environment doesn't implement BigInt.")}function eo(s){let e=0,t=0;for(let n=0;n<s.length;n++){let i=s[n];e+=i.byteLength}let r=new Uint8Array(e);for(let n=0;n<s.length;n++){let i=s[n];r.set(new Uint8Array(i),t),t+=i.byteLength}return r.buffer}function pt(s,e,t,r){return e instanceof Uint8Array?e.byteLength?t<0?(s.error="Wrong parameter: inputOffset less than zero",!1):r<0?(s.error="Wrong parameter: inputLength less than zero",!1):e.byteLength-t-r<0?(s.error="End of input reached before message was fully decoded (inconsistent offset and length values)",!1):!0:(s.error="Wrong parameter: inputBuffer has zero length",!1):(s.error="Wrong parameter: inputBuffer must be 'Uint8Array'",!1)}var Os=class{constructor(){this.items=[]}write(e){this.items.push(e)}final(){return eo(this.items)}},Hs=[new Uint8Array([1])],Ua="0123456789";var bs="",We=new ArrayBuffer(0),to=new Uint8Array(0),zs="EndOfContent",Va="OCTET STRING",Ha="BIT STRING";function gt(s){var e;return e=class extends s{constructor(...r){var n;super(...r);let i=r[0]||{};this.isHexOnly=(n=i.isHexOnly)!==null&&n!==void 0?n:!1,this.valueHexView=i.valueHex?H.BufferSourceConverter.toUint8Array(i.valueHex):to}get valueHex(){return this.valueHexView.slice().buffer}set valueHex(r){this.valueHexView=new Uint8Array(r)}fromBER(r,n,i){let o=r instanceof ArrayBuffer?new Uint8Array(r):r;if(!pt(this,o,n,i))return-1;let a=n+i;return this.valueHexView=o.subarray(n,a),this.valueHexView.length?(this.blockLength=i,a):(this.warnings.push("Zero buffer length"),n)}toBER(r=!1){return this.isHexOnly?r?new ArrayBuffer(this.valueHexView.byteLength):this.valueHexView.byteLength===this.valueHexView.buffer.byteLength?this.valueHexView.buffer:this.valueHexView.slice().buffer:(this.error="Flag 'isHexOnly' is not set, abort",We)}toJSON(){return{...super.toJSON(),isHexOnly:this.isHexOnly,valueHex:H.Convert.ToHex(this.valueHexView)}}},e.NAME="hexBlock",e}var ht=class{constructor({blockLength:e=0,error:t=bs,warnings:r=[],valueBeforeDecode:n=to}={}){this.blockLength=e,this.error=t,this.warnings=r,this.valueBeforeDecodeView=H.BufferSourceConverter.toUint8Array(n)}static blockName(){return this.NAME}get valueBeforeDecode(){return this.valueBeforeDecodeView.slice().buffer}set valueBeforeDecode(e){this.valueBeforeDecodeView=new Uint8Array(e)}toJSON(){return{blockName:this.constructor.NAME,blockLength:this.blockLength,error:this.error,warnings:this.warnings,valueBeforeDecode:H.Convert.ToHex(this.valueBeforeDecodeView)}}};ht.NAME="baseBlock";var me=class extends ht{fromBER(e,t,r){throw TypeError("User need to make a specific function in a class which extends 'ValueBlock'")}toBER(e,t){throw TypeError("User need to make a specific function in a class which extends 'ValueBlock'")}};me.NAME="valueBlock";var Ar=class extends gt(ht){constructor({idBlock:e={}}={}){var t,r,n,i;super(),e?(this.isHexOnly=(t=e.isHexOnly)!==null&&t!==void 0?t:!1,this.valueHexView=e.valueHex?H.BufferSourceConverter.toUint8Array(e.valueHex):to,this.tagClass=(r=e.tagClass)!==null&&r!==void 0?r:-1,this.tagNumber=(n=e.tagNumber)!==null&&n!==void 0?n:-1,this.isConstructed=(i=e.isConstructed)!==null&&i!==void 0?i:!1):(this.tagClass=-1,this.tagNumber=-1,this.isConstructed=!1)}toBER(e=!1){let t=0;switch(this.tagClass){case 1:t|=0;break;case 2:t|=64;break;case 3:t|=128;break;case 4:t|=192;break;default:return this.error="Unknown tag class",We}if(this.isConstructed&&(t|=32),this.tagNumber<31&&!this.isHexOnly){let n=new Uint8Array(1);if(!e){let i=this.tagNumber;i&=31,t|=i,n[0]=t}return n.buffer}if(!this.isHexOnly){let n=Dt(this.tagNumber,7),i=new Uint8Array(n),o=n.byteLength,a=new Uint8Array(o+1);if(a[0]=t|31,!e){for(let c=0;c<o-1;c++)a[c+1]=i[c]|128;a[o]=i[o-1]}return a.buffer}let r=new Uint8Array(this.valueHexView.byteLength+1);if(r[0]=t|31,!e){let n=this.valueHexView;for(let i=0;i<n.length-1;i++)r[i+1]=n[i]|128;r[this.valueHexView.byteLength]=n[n.length-1]}return r.buffer}fromBER(e,t,r){let n=H.BufferSourceConverter.toUint8Array(e);if(!pt(this,n,t,r))return-1;let i=n.subarray(t,t+r);if(i.length===0)return this.error="Zero buffer length",-1;switch(i[0]&192){case 0:this.tagClass=1;break;case 64:this.tagClass=2;break;case 128:this.tagClass=3;break;case 192:this.tagClass=4;break;default:return this.error="Unknown tag class",-1}this.isConstructed=(i[0]&32)===32,this.isHexOnly=!1;let a=i[0]&31;if(a!==31)this.tagNumber=a,this.blockLength=1;else{let c=1,h=this.valueHexView=new Uint8Array(255),f=255;for(;i[c]&128;){if(h[c-1]=i[c]&127,c++,c>=i.length)return this.error="End of input reached before message was fully decoded",-1;if(c===f){f+=255;let g=new Uint8Array(f);for(let u=0;u<h.length;u++)g[u]=h[u];h=this.valueHexView=new Uint8Array(f)}}this.blockLength=c+1,h[c-1]=i[c]&127;let l=new Uint8Array(c);for(let g=0;g<c;g++)l[g]=h[g];h=this.valueHexView=new Uint8Array(c),h.set(l),this.blockLength<=9?this.tagNumber=$t(h,7):(this.isHexOnly=!0,this.warnings.push("Tag too long, represented as hex-coded"))}if(this.tagClass===1&&this.isConstructed)switch(this.tagNumber){case 1:case 2:case 5:case 6:case 9:case 13:case 14:case 23:case 24:case 31:case 32:case 33:case 34:return this.error="Constructed encoding used for primitive type",-1}return t+this.blockLength}toJSON(){return{...super.toJSON(),tagClass:this.tagClass,tagNumber:this.tagNumber,isConstructed:this.isConstructed}}};Ar.NAME="identificationBlock";var kr=class extends ht{constructor({lenBlock:e={}}={}){var t,r,n;super(),this.isIndefiniteForm=(t=e.isIndefiniteForm)!==null&&t!==void 0?t:!1,this.longFormUsed=(r=e.longFormUsed)!==null&&r!==void 0?r:!1,this.length=(n=e.length)!==null&&n!==void 0?n:0}fromBER(e,t,r){let n=H.BufferSourceConverter.toUint8Array(e);if(!pt(this,n,t,r))return-1;let i=n.subarray(t,t+r);if(i.length===0)return this.error="Zero buffer length",-1;if(i[0]===255)return this.error="Length block 0xFF is reserved by standard",-1;if(this.isIndefiniteForm=i[0]===128,this.isIndefiniteForm)return this.blockLength=1,t+this.blockLength;if(this.longFormUsed=!!(i[0]&128),this.longFormUsed===!1)return this.length=i[0],this.blockLength=1,t+this.blockLength;let o=i[0]&127;if(o>8)return this.error="Too big integer",-1;if(o+1>i.length)return this.error="End of input reached before message was fully decoded",-1;let a=t+1,c=n.subarray(a,a+o);return c[o-1]===0&&this.warnings.push("Needlessly long encoded length"),this.length=$t(c,8),this.longFormUsed&&this.length<=127&&this.warnings.push("Unnecessary usage of long length form"),this.blockLength=o+1,t+this.blockLength}toBER(e=!1){let t,r;if(this.length>127&&(this.longFormUsed=!0),this.isIndefiniteForm)return t=new ArrayBuffer(1),e===!1&&(r=new Uint8Array(t),r[0]=128),t;if(this.longFormUsed){let n=Dt(this.length,8);if(n.byteLength>127)return this.error="Too big length",We;if(t=new ArrayBuffer(n.byteLength+1),e)return t;let i=new Uint8Array(n);r=new Uint8Array(t),r[0]=n.byteLength|128;for(let o=0;o<n.byteLength;o++)r[o+1]=i[o];return t}return t=new ArrayBuffer(1),e===!1&&(r=new Uint8Array(t),r[0]=this.length),t}toJSON(){return{...super.toJSON(),isIndefiniteForm:this.isIndefiniteForm,longFormUsed:this.longFormUsed,length:this.length}}};kr.NAME="lengthBlock";var M={},he=class extends ht{constructor({name:e=bs,optional:t=!1,primitiveSchema:r,...n}={},i){super(n),this.name=e,this.optional=t,r&&(this.primitiveSchema=r),this.idBlock=new Ar(n),this.lenBlock=new kr(n),this.valueBlock=i?new i(n):new me(n)}fromBER(e,t,r){let n=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return n===-1?(this.error=this.valueBlock.error,n):(this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength),n)}toBER(e,t){let r=t||new Os;t||Oa(this);let n=this.idBlock.toBER(e);if(r.write(n),this.lenBlock.isIndefiniteForm)r.write(new Uint8Array([128]).buffer),this.valueBlock.toBER(e,r),r.write(new ArrayBuffer(2));else{let i=this.valueBlock.toBER(e);this.lenBlock.length=i.byteLength;let o=this.lenBlock.toBER(e);r.write(o),r.write(i)}return t?We:r.final()}toJSON(){let e={...super.toJSON(),idBlock:this.idBlock.toJSON(),lenBlock:this.lenBlock.toJSON(),valueBlock:this.valueBlock.toJSON(),name:this.name,optional:this.optional};return this.primitiveSchema&&(e.primitiveSchema=this.primitiveSchema.toJSON()),e}toString(e="ascii"){return e==="ascii"?this.onAsciiEncoding():H.Convert.ToHex(this.toBER())}onAsciiEncoding(){return`${this.constructor.NAME} : ${H.Convert.ToHex(this.valueBlock.valueBeforeDecodeView)}`}isEqual(e){if(this===e)return!0;if(!(e instanceof this.constructor))return!1;let t=this.toBER(),r=e.toBER();return La(t,r)}};he.NAME="BaseBlock";function Oa(s){if(s instanceof M.Constructed)for(let e of s.valueBlock.value)Oa(e)&&(s.lenBlock.isIndefiniteForm=!0);return!!s.lenBlock.isIndefiniteForm}var Tr=class extends he{constructor({value:e=bs,...t}={},r){super(t,r),e&&this.fromString(e)}getValue(){return this.valueBlock.value}setValue(e){this.valueBlock.value=e}fromBER(e,t,r){let n=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return n===-1?(this.error=this.valueBlock.error,n):(this.fromBuffer(this.valueBlock.valueHexView),this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength),n)}onAsciiEncoding(){return`${this.constructor.NAME} : '${this.valueBlock.value}'`}};Tr.NAME="BaseStringBlock";var Mr=class extends gt(me){constructor({isHexOnly:e=!0,...t}={}){super(t),this.isHexOnly=e}};Mr.NAME="PrimitiveValueBlock";var za,Pr=class extends he{constructor(e={}){super(e,Mr),this.idBlock.isConstructed=!1}};za=Pr;M.Primitive=za;Pr.NAME="PRIMITIVE";function xf(s,e){if(s instanceof e)return s;let t=new e;return t.idBlock=s.idBlock,t.lenBlock=s.lenBlock,t.warnings=s.warnings,t.valueBeforeDecodeView=s.valueBeforeDecodeView,t}function fn(s,e=0,t=s.length){let r=e,n=new he({},me),i=new ht;if(!pt(i,s,e,t))return n.error=i.error,{offset:-1,result:n};if(!s.subarray(e,e+t).length)return n.error="Zero buffer length",{offset:-1,result:n};let a=n.idBlock.fromBER(s,e,t);if(n.idBlock.warnings.length&&n.warnings.concat(n.idBlock.warnings),a===-1)return n.error=n.idBlock.error,{offset:-1,result:n};if(e=a,t-=n.idBlock.blockLength,a=n.lenBlock.fromBER(s,e,t),n.lenBlock.warnings.length&&n.warnings.concat(n.lenBlock.warnings),a===-1)return n.error=n.lenBlock.error,{offset:-1,result:n};if(e=a,t-=n.lenBlock.blockLength,!n.idBlock.isConstructed&&n.lenBlock.isIndefiniteForm)return n.error="Indefinite length form used for primitive encoding form",{offset:-1,result:n};let c=he;switch(n.idBlock.tagClass){case 1:if(n.idBlock.tagNumber>=37&&n.idBlock.isHexOnly===!1)return n.error="UNIVERSAL 37 and upper tags are reserved by ASN.1 standard",{offset:-1,result:n};switch(n.idBlock.tagNumber){case 0:if(n.idBlock.isConstructed&&n.lenBlock.length>0)return n.error="Type [UNIVERSAL 0] is reserved",{offset:-1,result:n};c=M.EndOfContent;break;case 1:c=M.Boolean;break;case 2:c=M.Integer;break;case 3:c=M.BitString;break;case 4:c=M.OctetString;break;case 5:c=M.Null;break;case 6:c=M.ObjectIdentifier;break;case 10:c=M.Enumerated;break;case 12:c=M.Utf8String;break;case 13:c=M.RelativeObjectIdentifier;break;case 14:c=M.TIME;break;case 15:return n.error="[UNIVERSAL 15] is reserved by ASN.1 standard",{offset:-1,result:n};case 16:c=M.Sequence;break;case 17:c=M.Set;break;case 18:c=M.NumericString;break;case 19:c=M.PrintableString;break;case 20:c=M.TeletexString;break;case 21:c=M.VideotexString;break;case 22:c=M.IA5String;break;case 23:c=M.UTCTime;break;case 24:c=M.GeneralizedTime;break;case 25:c=M.GraphicString;break;case 26:c=M.VisibleString;break;case 27:c=M.GeneralString;break;case 28:c=M.UniversalString;break;case 29:c=M.CharacterString;break;case 30:c=M.BmpString;break;case 31:c=M.DATE;break;case 32:c=M.TimeOfDay;break;case 33:c=M.DateTime;break;case 34:c=M.Duration;break;default:{let h=n.idBlock.isConstructed?new M.Constructed:new M.Primitive;h.idBlock=n.idBlock,h.lenBlock=n.lenBlock,h.warnings=n.warnings,n=h}}break;case 2:case 3:case 4:default:c=n.idBlock.isConstructed?M.Constructed:M.Primitive}return n=xf(n,c),a=n.fromBER(s,e,n.lenBlock.isIndefiniteForm?t:n.lenBlock.length),n.valueBeforeDecodeView=s.subarray(r,r+n.blockLength),{offset:a,result:n}}function so(s){if(!s.byteLength){let e=new he({},me);return e.error="Input buffer has zero length",{offset:-1,result:e}}return fn(H.BufferSourceConverter.toUint8Array(s).slice(),0,s.byteLength)}function Ef(s,e){return s?1:e}var Ye=class extends me{constructor({value:e=[],isIndefiniteForm:t=!1,...r}={}){super(r),this.value=e,this.isIndefiniteForm=t}fromBER(e,t,r){let n=H.BufferSourceConverter.toUint8Array(e);if(!pt(this,n,t,r))return-1;if(this.valueBeforeDecodeView=n.subarray(t,t+r),this.valueBeforeDecodeView.length===0)return this.warnings.push("Zero buffer length"),t;let i=t;for(;Ef(this.isIndefiniteForm,r)>0;){let o=fn(n,i,r);if(o.offset===-1)return this.error=o.result.error,this.warnings.concat(o.result.warnings),-1;if(i=o.offset,this.blockLength+=o.result.blockLength,r-=o.result.blockLength,this.value.push(o.result),this.isIndefiniteForm&&o.result.constructor.NAME===zs)break}return this.isIndefiniteForm&&(this.value[this.value.length-1].constructor.NAME===zs?this.value.pop():this.warnings.push("No EndOfContent block encoded")),i}toBER(e,t){let r=t||new Os;for(let n=0;n<this.value.length;n++)this.value[n].toBER(e,r);return t?We:r.final()}toJSON(){let e={...super.toJSON(),isIndefiniteForm:this.isIndefiniteForm,value:[]};for(let t of this.value)e.value.push(t.toJSON());return e}};Ye.NAME="ConstructedValueBlock";var Ga,Ct=class extends he{constructor(e={}){super(e,Ye),this.idBlock.isConstructed=!0}fromBER(e,t,r){this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm;let n=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return n===-1?(this.error=this.valueBlock.error,n):(this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength),n)}onAsciiEncoding(){let e=[];for(let r of this.valueBlock.value)e.push(r.toString("ascii").split(`
`).map(n=>`  ${n}`).join(`
`));let t=this.idBlock.tagClass===3?`[${this.idBlock.tagNumber}]`:this.constructor.NAME;return e.length?`${t} :
${e.join(`
`)}`:`${t} :`}};Ga=Ct;M.Constructed=Ga;Ct.NAME="CONSTRUCTED";var Dr=class extends me{fromBER(e,t,r){return t}toBER(e){return We}};Dr.override="EndOfContentValueBlock";var qa,Cr=class extends he{constructor(e={}){super(e,Dr),this.idBlock.tagClass=1,this.idBlock.tagNumber=0}};qa=Cr;M.EndOfContent=qa;Cr.NAME=zs;var Ka,ps=class extends he{constructor(e={}){super(e,me),this.idBlock.tagClass=1,this.idBlock.tagNumber=5}fromBER(e,t,r){return this.lenBlock.length>0&&this.warnings.push("Non-zero length of value block for Null type"),this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.blockLength+=r,t+r>e.byteLength?(this.error="End of input reached before message was fully decoded (inconsistent offset and length values)",-1):t+r}toBER(e,t){let r=new ArrayBuffer(2);if(!e){let n=new Uint8Array(r);n[0]=5,n[1]=0}return t&&t.write(r),r}onAsciiEncoding(){return`${this.constructor.NAME}`}};Ka=ps;M.Null=Ka;ps.NAME="NULL";var Nr=class extends gt(me){constructor({value:e,...t}={}){super(t),t.valueHex?this.valueHexView=H.BufferSourceConverter.toUint8Array(t.valueHex):this.valueHexView=new Uint8Array(1),e&&(this.value=e)}get value(){for(let e of this.valueHexView)if(e>0)return!0;return!1}set value(e){this.valueHexView[0]=e?255:0}fromBER(e,t,r){let n=H.BufferSourceConverter.toUint8Array(e);return pt(this,n,t,r)?(this.valueHexView=n.subarray(t,t+r),r>1&&this.warnings.push("Boolean value encoded in more then 1 octet"),this.isHexOnly=!0,Qi.call(this),this.blockLength=r,t+r):-1}toBER(){return this.valueHexView.slice()}toJSON(){return{...super.toJSON(),value:this.value}}};Nr.NAME="BooleanValueBlock";var Wa,Rr=class extends he{constructor(e={}){super(e,Nr),this.idBlock.tagClass=1,this.idBlock.tagNumber=1}getValue(){return this.valueBlock.value}setValue(e){this.valueBlock.value=e}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.getValue}`}};Wa=Rr;M.Boolean=Wa;Rr.NAME="BOOLEAN";var Lr=class extends gt(Ye){constructor({isConstructed:e=!1,...t}={}){super(t),this.isConstructed=e}fromBER(e,t,r){let n=0;if(this.isConstructed){if(this.isHexOnly=!1,n=Ye.prototype.fromBER.call(this,e,t,r),n===-1)return n;for(let i=0;i<this.value.length;i++){let o=this.value[i].constructor.NAME;if(o===zs){if(this.isIndefiniteForm)break;return this.error="EndOfContent is unexpected, OCTET STRING may consists of OCTET STRINGs only",-1}if(o!==Va)return this.error="OCTET STRING may consists of OCTET STRINGs only",-1}}else this.isHexOnly=!0,n=super.fromBER(e,t,r),this.blockLength=r;return n}toBER(e,t){return this.isConstructed?Ye.prototype.toBER.call(this,e,t):e?new ArrayBuffer(this.valueHexView.byteLength):this.valueHexView.slice().buffer}toJSON(){return{...super.toJSON(),isConstructed:this.isConstructed}}};Lr.NAME="OctetStringValueBlock";var $a,Ur=class s extends he{constructor({idBlock:e={},lenBlock:t={},...r}={}){var n,i;(n=r.isConstructed)!==null&&n!==void 0||(r.isConstructed=!!(!((i=r.value)===null||i===void 0)&&i.length)),super({idBlock:{isConstructed:r.isConstructed,...e},lenBlock:{...t,isIndefiniteForm:!!r.isIndefiniteForm},...r},Lr),this.idBlock.tagClass=1,this.idBlock.tagNumber=4}fromBER(e,t,r){if(this.valueBlock.isConstructed=this.idBlock.isConstructed,this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm,r===0)return this.idBlock.error.length===0&&(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length===0&&(this.blockLength+=this.lenBlock.blockLength),t;if(!this.valueBlock.isConstructed){let i=(e instanceof ArrayBuffer?new Uint8Array(e):e).subarray(t,t+r);try{if(i.byteLength){let o=fn(i,0,i.byteLength);o.offset!==-1&&o.offset===r&&(this.valueBlock.value=[o.result])}}catch{}}return super.fromBER(e,t,r)}onAsciiEncoding(){return this.valueBlock.isConstructed||this.valueBlock.value&&this.valueBlock.value.length?Ct.prototype.onAsciiEncoding.call(this):`${this.constructor.NAME} : ${H.Convert.ToHex(this.valueBlock.valueHexView)}`}getValue(){if(!this.idBlock.isConstructed)return this.valueBlock.valueHexView.slice().buffer;let e=[];for(let t of this.valueBlock.value)t instanceof s&&e.push(t.valueBlock.valueHexView);return H.BufferSourceConverter.concat(e)}};$a=Ur;M.OctetString=$a;Ur.NAME=Va;var Fr=class extends gt(Ye){constructor({unusedBits:e=0,isConstructed:t=!1,...r}={}){super(r),this.unusedBits=e,this.isConstructed=t,this.blockLength=this.valueHexView.byteLength}fromBER(e,t,r){if(!r)return t;let n=-1;if(this.isConstructed){if(n=Ye.prototype.fromBER.call(this,e,t,r),n===-1)return n;for(let a of this.value){let c=a.constructor.NAME;if(c===zs){if(this.isIndefiniteForm)break;return this.error="EndOfContent is unexpected, BIT STRING may consists of BIT STRINGs only",-1}if(c!==Ha)return this.error="BIT STRING may consists of BIT STRINGs only",-1;let h=a.valueBlock;if(this.unusedBits>0&&h.unusedBits>0)return this.error='Using of "unused bits" inside constructive BIT STRING allowed for least one only',-1;this.unusedBits=h.unusedBits}return n}let i=H.BufferSourceConverter.toUint8Array(e);if(!pt(this,i,t,r))return-1;let o=i.subarray(t,t+r);if(this.unusedBits=o[0],this.unusedBits>7)return this.error="Unused bits for BitString must be in range 0-7",-1;if(!this.unusedBits){let a=o.subarray(1);try{if(a.byteLength){let c=fn(a,0,a.byteLength);c.offset!==-1&&c.offset===r-1&&(this.value=[c.result])}}catch{}}return this.valueHexView=o.subarray(1),this.blockLength=o.length,t+r}toBER(e,t){if(this.isConstructed)return Ye.prototype.toBER.call(this,e,t);if(e)return new ArrayBuffer(this.valueHexView.byteLength+1);if(!this.valueHexView.byteLength)return We;let r=new Uint8Array(this.valueHexView.length+1);return r[0]=this.unusedBits,r.set(this.valueHexView,1),r.buffer}toJSON(){return{...super.toJSON(),unusedBits:this.unusedBits,isConstructed:this.isConstructed}}};Fr.NAME="BitStringValueBlock";var ja,gs=class extends he{constructor({idBlock:e={},lenBlock:t={},...r}={}){var n,i;(n=r.isConstructed)!==null&&n!==void 0||(r.isConstructed=!!(!((i=r.value)===null||i===void 0)&&i.length)),super({idBlock:{isConstructed:r.isConstructed,...e},lenBlock:{...t,isIndefiniteForm:!!r.isIndefiniteForm},...r},Fr),this.idBlock.tagClass=1,this.idBlock.tagNumber=3}fromBER(e,t,r){return this.valueBlock.isConstructed=this.idBlock.isConstructed,this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm,super.fromBER(e,t,r)}onAsciiEncoding(){if(this.valueBlock.isConstructed||this.valueBlock.value&&this.valueBlock.value.length)return Ct.prototype.onAsciiEncoding.call(this);{let e=[],t=this.valueBlock.valueHexView;for(let n of t)e.push(n.toString(2).padStart(8,"0"));let r=e.join("");return`${this.constructor.NAME} : ${r.substring(0,r.length-this.valueBlock.unusedBits)}`}}};ja=gs;M.BitString=ja;gs.NAME=Ha;var Za;function Sf(s,e){let t=new Uint8Array([0]),r=new Uint8Array(s),n=new Uint8Array(e),i=r.slice(0),o=i.length-1,a=n.slice(0),c=a.length-1,h=0,f=c<o?o:c,l=0;for(let g=f;g>=0;g--,l++){switch(!0){case l<a.length:h=i[o-l]+a[c-l]+t[0];break;default:h=i[o-l]+t[0]}switch(t[0]=h/10,!0){case l>=i.length:i=Ir(new Uint8Array([h%10]),i);break;default:i[o-l]=h%10}}return t[0]>0&&(i=Ir(t,i)),i}function Fa(s){if(s>=Hs.length)for(let e=Hs.length;e<=s;e++){let t=new Uint8Array([0]),r=Hs[e-1].slice(0);for(let n=r.length-1;n>=0;n--){let i=new Uint8Array([(r[n]<<1)+t[0]]);t[0]=i[0]/10,r[n]=i[0]%10}t[0]>0&&(r=Ir(t,r)),Hs.push(r)}return Hs[s]}function Bf(s,e){let t=0,r=new Uint8Array(s),n=new Uint8Array(e),i=r.slice(0),o=i.length-1,a=n.slice(0),c=a.length-1,h,f=0;for(let l=c;l>=0;l--,f++)switch(h=i[o-f]-a[c-f]-t,!0){case h<0:t=1,i[o-f]=h+10;break;default:t=0,i[o-f]=h}if(t>0)for(let l=o-c+1;l>=0;l--,f++)if(h=i[o-f]-t,h<0)t=1,i[o-f]=h+10;else{t=0,i[o-f]=h;break}return i.slice()}var Gs=class extends gt(me){constructor({value:e,...t}={}){super(t),this._valueDec=0,t.valueHex&&this.setValueHex(),e!==void 0&&(this.valueDec=e)}setValueHex(){this.valueHexView.length>=4?(this.warnings.push("Too big Integer for decoding, hex only"),this.isHexOnly=!0,this._valueDec=0):(this.isHexOnly=!1,this.valueHexView.length>0&&(this._valueDec=Qi.call(this)))}set valueDec(e){this._valueDec=e,this.isHexOnly=!1,this.valueHexView=new Uint8Array(Ra(e))}get valueDec(){return this._valueDec}fromDER(e,t,r,n=0){let i=this.fromBER(e,t,r);if(i===-1)return i;let o=this.valueHexView;return o[0]===0&&o[1]&128?this.valueHexView=o.subarray(1):n!==0&&o.length<n&&(n-o.length>1&&(n=o.length+1),this.valueHexView=o.subarray(n-o.length)),i}toDER(e=!1){let t=this.valueHexView;switch(!0){case(t[0]&128)!==0:{let r=new Uint8Array(this.valueHexView.length+1);r[0]=0,r.set(t,1),this.valueHexView=r}break;case(t[0]===0&&(t[1]&128)===0):this.valueHexView=this.valueHexView.subarray(1);break}return this.toBER(e)}fromBER(e,t,r){let n=super.fromBER(e,t,r);return n===-1||this.setValueHex(),n}toBER(e){return e?new ArrayBuffer(this.valueHexView.length):this.valueHexView.slice().buffer}toJSON(){return{...super.toJSON(),valueDec:this.valueDec}}toString(){let e=this.valueHexView.length*8-1,t=new Uint8Array(this.valueHexView.length*8/3),r=0,n,i=this.valueHexView,o="",a=!1;for(let c=i.byteLength-1;c>=0;c--){n=i[c];for(let h=0;h<8;h++){if((n&1)===1)switch(r){case e:t=Bf(Fa(r),t),o="-";break;default:t=Sf(t,Fa(r))}r++,n>>=1}}for(let c=0;c<t.length;c++)t[c]&&(a=!0),a&&(o+=Ua.charAt(t[c]));return a===!1&&(o+=Ua.charAt(0)),o}};Za=Gs;Gs.NAME="IntegerValueBlock";Object.defineProperty(Za.prototype,"valueHex",{set:function(s){this.valueHexView=new Uint8Array(s),this.setValueHex()},get:function(){return this.valueHexView.slice().buffer}});var Ja,fe=class s extends he{constructor(e={}){super(e,Gs),this.idBlock.tagClass=1,this.idBlock.tagNumber=2}toBigInt(){return _r(),BigInt(this.valueBlock.toString())}static fromBigInt(e){_r();let t=BigInt(e),r=new Os,n=t.toString(16).replace(/^-/,""),i=new Uint8Array(H.Convert.FromHex(n));if(t<0){let a=new Uint8Array(i.length+(i[0]&128?1:0));a[0]|=128;let h=BigInt(`0x${H.Convert.ToHex(a)}`)+t,f=H.BufferSourceConverter.toUint8Array(H.Convert.FromHex(h.toString(16)));f[0]|=128,r.write(f)}else i[0]&128&&r.write(new Uint8Array([0])),r.write(i);return new s({valueHex:r.final()})}convertToDER(){let e=new s({valueHex:this.valueBlock.valueHexView});return e.valueBlock.toDER(),e}convertFromDER(){return new s({valueHex:this.valueBlock.valueHexView[0]===0?this.valueBlock.valueHexView.subarray(1):this.valueBlock.valueHexView})}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()}`}};Ja=fe;M.Integer=Ja;fe.NAME="INTEGER";var Xa,Vr=class extends fe{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=10}};Xa=Vr;M.Enumerated=Xa;Vr.NAME="ENUMERATED";var qs=class extends gt(me){constructor({valueDec:e=-1,isFirstSid:t=!1,...r}={}){super(r),this.valueDec=e,this.isFirstSid=t}fromBER(e,t,r){if(!r)return t;let n=H.BufferSourceConverter.toUint8Array(e);if(!pt(this,n,t,r))return-1;let i=n.subarray(t,t+r);this.valueHexView=new Uint8Array(r);for(let a=0;a<r&&(this.valueHexView[a]=i[a]&127,this.blockLength++,!!(i[a]&128));a++);let o=new Uint8Array(this.blockLength);for(let a=0;a<this.blockLength;a++)o[a]=this.valueHexView[a];return this.valueHexView=o,i[this.blockLength-1]&128?(this.error="End of input reached before message was fully decoded",-1):(this.valueHexView[0]===0&&this.warnings.push("Needlessly long format of SID encoding"),this.blockLength<=8?this.valueDec=$t(this.valueHexView,7):(this.isHexOnly=!0,this.warnings.push("Too big SID for decoding, hex only")),t+this.blockLength)}set valueBigInt(e){_r();let t=BigInt(e).toString(2);for(;t.length%7;)t="0"+t;let r=new Uint8Array(t.length/7);for(let n=0;n<r.length;n++)r[n]=parseInt(t.slice(n*7,n*7+7),2)+(n+1<r.length?128:0);this.fromBER(r.buffer,0,r.length)}toBER(e){if(this.isHexOnly){if(e)return new ArrayBuffer(this.valueHexView.byteLength);let n=this.valueHexView,i=new Uint8Array(this.blockLength);for(let o=0;o<this.blockLength-1;o++)i[o]=n[o]|128;return i[this.blockLength-1]=n[this.blockLength-1],i.buffer}let t=Dt(this.valueDec,7);if(t.byteLength===0)return this.error="Error during encoding SID value",We;let r=new Uint8Array(t.byteLength);if(!e){let n=new Uint8Array(t),i=t.byteLength-1;for(let o=0;o<i;o++)r[o]=n[o]|128;r[i]=n[i]}return r}toString(){let e="";if(this.isHexOnly)e=H.Convert.ToHex(this.valueHexView);else if(this.isFirstSid){let t=this.valueDec;this.valueDec<=39?e="0.":this.valueDec<=79?(e="1.",t-=40):(e="2.",t-=80),e+=t.toString()}else e=this.valueDec.toString();return e}toJSON(){return{...super.toJSON(),valueDec:this.valueDec,isFirstSid:this.isFirstSid}}};qs.NAME="sidBlock";var Hr=class extends me{constructor({value:e=bs,...t}={}){super(t),this.value=[],e&&this.fromString(e)}fromBER(e,t,r){let n=t;for(;r>0;){let i=new qs;if(n=i.fromBER(e,n,r),n===-1)return this.blockLength=0,this.error=i.error,n;this.value.length===0&&(i.isFirstSid=!0),this.blockLength+=i.blockLength,r-=i.blockLength,this.value.push(i)}return n}toBER(e){let t=[];for(let r=0;r<this.value.length;r++){let n=this.value[r].toBER(e);if(n.byteLength===0)return this.error=this.value[r].error,We;t.push(n)}return eo(t)}fromString(e){this.value=[];let t=0,r=0,n="",i=!1;do if(r=e.indexOf(".",t),r===-1?n=e.substring(t):n=e.substring(t,r),t=r+1,i){let o=this.value[0],a=0;switch(o.valueDec){case 0:break;case 1:a=40;break;case 2:a=80;break;default:this.value=[];return}let c=parseInt(n,10);if(isNaN(c))return;o.valueDec=c+a,i=!1}else{let o=new qs;if(n>Number.MAX_SAFE_INTEGER){_r();let a=BigInt(n);o.valueBigInt=a}else if(o.valueDec=parseInt(n,10),isNaN(o.valueDec))return;this.value.length||(o.isFirstSid=!0,i=!0),this.value.push(o)}while(r!==-1)}toString(){let e="",t=!1;for(let r=0;r<this.value.length;r++){t=this.value[r].isHexOnly;let n=this.value[r].toString();r!==0&&(e=`${e}.`),t?(n=`{${n}}`,this.value[r].isFirstSid?e=`2.{${n} - 80}`:e+=n):e+=n}return e}toJSON(){let e={...super.toJSON(),value:this.toString(),sidArray:[]};for(let t=0;t<this.value.length;t++)e.sidArray.push(this.value[t].toJSON());return e}};Hr.NAME="ObjectIdentifierValueBlock";var Ya,ms=class extends he{constructor(e={}){super(e,Hr),this.idBlock.tagClass=1,this.idBlock.tagNumber=6}getValue(){return this.valueBlock.toString()}setValue(e){this.valueBlock.fromString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()||"empty"}`}toJSON(){return{...super.toJSON(),value:this.getValue()}}};Ya=ms;M.ObjectIdentifier=Ya;ms.NAME="OBJECT IDENTIFIER";var Ks=class extends gt(ht){constructor({valueDec:e=0,...t}={}){super(t),this.valueDec=e}fromBER(e,t,r){if(r===0)return t;let n=H.BufferSourceConverter.toUint8Array(e);if(!pt(this,n,t,r))return-1;let i=n.subarray(t,t+r);this.valueHexView=new Uint8Array(r);for(let a=0;a<r&&(this.valueHexView[a]=i[a]&127,this.blockLength++,!!(i[a]&128));a++);let o=new Uint8Array(this.blockLength);for(let a=0;a<this.blockLength;a++)o[a]=this.valueHexView[a];return this.valueHexView=o,i[this.blockLength-1]&128?(this.error="End of input reached before message was fully decoded",-1):(this.valueHexView[0]===0&&this.warnings.push("Needlessly long format of SID encoding"),this.blockLength<=8?this.valueDec=$t(this.valueHexView,7):(this.isHexOnly=!0,this.warnings.push("Too big SID for decoding, hex only")),t+this.blockLength)}toBER(e){if(this.isHexOnly){if(e)return new ArrayBuffer(this.valueHexView.byteLength);let n=this.valueHexView,i=new Uint8Array(this.blockLength);for(let o=0;o<this.blockLength-1;o++)i[o]=n[o]|128;return i[this.blockLength-1]=n[this.blockLength-1],i.buffer}let t=Dt(this.valueDec,7);if(t.byteLength===0)return this.error="Error during encoding SID value",We;let r=new Uint8Array(t.byteLength);if(!e){let n=new Uint8Array(t),i=t.byteLength-1;for(let o=0;o<i;o++)r[o]=n[o]|128;r[i]=n[i]}return r.buffer}toString(){let e="";return this.isHexOnly?e=H.Convert.ToHex(this.valueHexView):e=this.valueDec.toString(),e}toJSON(){return{...super.toJSON(),valueDec:this.valueDec}}};Ks.NAME="relativeSidBlock";var Or=class extends me{constructor({value:e=bs,...t}={}){super(t),this.value=[],e&&this.fromString(e)}fromBER(e,t,r){let n=t;for(;r>0;){let i=new Ks;if(n=i.fromBER(e,n,r),n===-1)return this.blockLength=0,this.error=i.error,n;this.blockLength+=i.blockLength,r-=i.blockLength,this.value.push(i)}return n}toBER(e,t){let r=[];for(let n=0;n<this.value.length;n++){let i=this.value[n].toBER(e);if(i.byteLength===0)return this.error=this.value[n].error,We;r.push(i)}return eo(r)}fromString(e){this.value=[];let t=0,r=0,n="";do{r=e.indexOf(".",t),r===-1?n=e.substring(t):n=e.substring(t,r),t=r+1;let i=new Ks;if(i.valueDec=parseInt(n,10),isNaN(i.valueDec))return!0;this.value.push(i)}while(r!==-1);return!0}toString(){let e="",t=!1;for(let r=0;r<this.value.length;r++){t=this.value[r].isHexOnly;let n=this.value[r].toString();r!==0&&(e=`${e}.`),t&&(n=`{${n}}`),e+=n}return e}toJSON(){let e={...super.toJSON(),value:this.toString(),sidArray:[]};for(let t=0;t<this.value.length;t++)e.sidArray.push(this.value[t].toJSON());return e}};Or.NAME="RelativeObjectIdentifierValueBlock";var Qa,zr=class extends he{constructor(e={}){super(e,Or),this.idBlock.tagClass=1,this.idBlock.tagNumber=13}getValue(){return this.valueBlock.toString()}setValue(e){this.valueBlock.fromString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()||"empty"}`}toJSON(){return{...super.toJSON(),value:this.getValue()}}};Qa=zr;M.RelativeObjectIdentifier=Qa;zr.NAME="RelativeObjectIdentifier";var ec,ft=class extends Ct{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=16}};ec=ft;M.Sequence=ec;ft.NAME="SEQUENCE";var tc,Gr=class extends Ct{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=17}};tc=Gr;M.Set=tc;Gr.NAME="SET";var qr=class extends gt(me){constructor({...e}={}){super(e),this.isHexOnly=!0,this.value=bs}toJSON(){return{...super.toJSON(),value:this.value}}};qr.NAME="StringValueBlock";var Kr=class extends qr{};Kr.NAME="SimpleStringValueBlock";var _e=class extends Tr{constructor({...e}={}){super(e,Kr)}fromBuffer(e){this.valueBlock.value=String.fromCharCode.apply(null,H.BufferSourceConverter.toUint8Array(e))}fromString(e){let t=e.length,r=this.valueBlock.valueHexView=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);this.valueBlock.value=e}};_e.NAME="SIMPLE STRING";var Wr=class extends _e{fromBuffer(e){this.valueBlock.valueHexView=H.BufferSourceConverter.toUint8Array(e);try{this.valueBlock.value=H.Convert.ToUtf8String(e)}catch(t){this.warnings.push(`Error during "decodeURIComponent": ${t}, using raw string`),this.valueBlock.value=H.Convert.ToBinary(e)}}fromString(e){this.valueBlock.valueHexView=new Uint8Array(H.Convert.FromUtf8String(e)),this.valueBlock.value=e}};Wr.NAME="Utf8StringValueBlock";var sc,dt=class extends Wr{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=12}};sc=dt;M.Utf8String=sc;dt.NAME="UTF8String";var $r=class extends _e{fromBuffer(e){this.valueBlock.value=H.Convert.ToUtf16String(e),this.valueBlock.valueHexView=H.BufferSourceConverter.toUint8Array(e)}fromString(e){this.valueBlock.value=e,this.valueBlock.valueHexView=new Uint8Array(H.Convert.FromUtf16String(e))}};$r.NAME="BmpStringValueBlock";var rc,jr=class extends $r{constructor({...e}={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=30}};rc=jr;M.BmpString=rc;jr.NAME="BMPString";var Zr=class extends _e{fromBuffer(e){let t=ArrayBuffer.isView(e)?e.slice().buffer:e.slice(0),r=new Uint8Array(t);for(let n=0;n<r.length;n+=4)r[n]=r[n+3],r[n+1]=r[n+2],r[n+2]=0,r[n+3]=0;this.valueBlock.value=String.fromCharCode.apply(null,new Uint32Array(t))}fromString(e){let t=e.length,r=this.valueBlock.valueHexView=new Uint8Array(t*4);for(let n=0;n<t;n++){let i=Dt(e.charCodeAt(n),8),o=new Uint8Array(i);if(o.length>4)continue;let a=4-o.length;for(let c=o.length-1;c>=0;c--)r[n*4+c+a]=o[c]}this.valueBlock.value=e}};Zr.NAME="UniversalStringValueBlock";var nc,Jr=class extends Zr{constructor({...e}={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=28}};nc=Jr;M.UniversalString=nc;Jr.NAME="UniversalString";var ic,Xr=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=18}};ic=Xr;M.NumericString=ic;Xr.NAME="NumericString";var oc,Yr=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=19}};oc=Yr;M.PrintableString=oc;Yr.NAME="PrintableString";var ac,Qr=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=20}};ac=Qr;M.TeletexString=ac;Qr.NAME="TeletexString";var cc,en=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=21}};cc=en;M.VideotexString=cc;en.NAME="VideotexString";var lc,tn=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=22}};lc=tn;M.IA5String=lc;tn.NAME="IA5String";var uc,sn=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=25}};uc=sn;M.GraphicString=uc;sn.NAME="GraphicString";var hc,Ws=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=26}};hc=Ws;M.VisibleString=hc;Ws.NAME="VisibleString";var fc,rn=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=27}};fc=rn;M.GeneralString=fc;rn.NAME="GeneralString";var dc,nn=class extends _e{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=29}};dc=nn;M.CharacterString=dc;nn.NAME="CharacterString";var pc,$s=class extends Ws{constructor({value:e,valueDate:t,...r}={}){if(super(r),this.year=0,this.month=0,this.day=0,this.hour=0,this.minute=0,this.second=0,e){this.fromString(e),this.valueBlock.valueHexView=new Uint8Array(e.length);for(let n=0;n<e.length;n++)this.valueBlock.valueHexView[n]=e.charCodeAt(n)}t&&(this.fromDate(t),this.valueBlock.valueHexView=new Uint8Array(this.toBuffer())),this.idBlock.tagClass=1,this.idBlock.tagNumber=23}fromBuffer(e){this.fromString(String.fromCharCode.apply(null,H.BufferSourceConverter.toUint8Array(e)))}toBuffer(){let e=this.toString(),t=new ArrayBuffer(e.length),r=new Uint8Array(t);for(let n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return t}fromDate(e){this.year=e.getUTCFullYear(),this.month=e.getUTCMonth()+1,this.day=e.getUTCDate(),this.hour=e.getUTCHours(),this.minute=e.getUTCMinutes(),this.second=e.getUTCSeconds()}toDate(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second))}fromString(e){let r=/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})Z/ig.exec(e);if(r===null){this.error="Wrong input string for conversion";return}let n=parseInt(r[1],10);n>=50?this.year=1900+n:this.year=2e3+n,this.month=parseInt(r[2],10),this.day=parseInt(r[3],10),this.hour=parseInt(r[4],10),this.minute=parseInt(r[5],10),this.second=parseInt(r[6],10)}toString(e="iso"){if(e==="iso"){let t=new Array(7);return t[0]=Re(this.year<2e3?this.year-1900:this.year-2e3,2),t[1]=Re(this.month,2),t[2]=Re(this.day,2),t[3]=Re(this.hour,2),t[4]=Re(this.minute,2),t[5]=Re(this.second,2),t[6]="Z",t.join("")}return super.toString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.toDate().toISOString()}`}toJSON(){return{...super.toJSON(),year:this.year,month:this.month,day:this.day,hour:this.hour,minute:this.minute,second:this.second}}};pc=$s;M.UTCTime=pc;$s.NAME="UTCTime";var gc,on=class extends $s{constructor(e={}){var t;super(e),(t=this.millisecond)!==null&&t!==void 0||(this.millisecond=0),this.idBlock.tagClass=1,this.idBlock.tagNumber=24}fromDate(e){super.fromDate(e),this.millisecond=e.getUTCMilliseconds()}toDate(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second,this.millisecond))}fromString(e){let t=!1,r="",n="",i=0,o,a=0,c=0;if(e[e.length-1]==="Z")r=e.substring(0,e.length-1),t=!0;else{let l=new Number(e[e.length-1]);if(isNaN(l.valueOf()))throw new Error("Wrong input string for conversion");r=e}if(t){if(r.indexOf("+")!==-1)throw new Error("Wrong input string for conversion");if(r.indexOf("-")!==-1)throw new Error("Wrong input string for conversion")}else{let l=1,g=r.indexOf("+"),u="";if(g===-1&&(g=r.indexOf("-"),l=-1),g!==-1){if(u=r.substring(g+1),r=r.substring(0,g),u.length!==2&&u.length!==4)throw new Error("Wrong input string for conversion");let d=parseInt(u.substring(0,2),10);if(isNaN(d.valueOf()))throw new Error("Wrong input string for conversion");if(a=l*d,u.length===4){if(d=parseInt(u.substring(2,4),10),isNaN(d.valueOf()))throw new Error("Wrong input string for conversion");c=l*d}}}let h=r.indexOf(".");if(h===-1&&(h=r.indexOf(",")),h!==-1){let l=new Number(`0${r.substring(h)}`);if(isNaN(l.valueOf()))throw new Error("Wrong input string for conversion");i=l.valueOf(),n=r.substring(0,h)}else n=r;switch(!0){case n.length===8:if(o=/(\d{4})(\d{2})(\d{2})/ig,h!==-1)throw new Error("Wrong input string for conversion");break;case n.length===10:if(o=/(\d{4})(\d{2})(\d{2})(\d{2})/ig,h!==-1){let l=60*i;this.minute=Math.floor(l),l=60*(l-this.minute),this.second=Math.floor(l),l=1e3*(l-this.second),this.millisecond=Math.floor(l)}break;case n.length===12:if(o=/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/ig,h!==-1){let l=60*i;this.second=Math.floor(l),l=1e3*(l-this.second),this.millisecond=Math.floor(l)}break;case n.length===14:if(o=/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/ig,h!==-1){let l=1e3*i;this.millisecond=Math.floor(l)}break;default:throw new Error("Wrong input string for conversion")}let f=o.exec(n);if(f===null)throw new Error("Wrong input string for conversion");for(let l=1;l<f.length;l++)switch(l){case 1:this.year=parseInt(f[l],10);break;case 2:this.month=parseInt(f[l],10);break;case 3:this.day=parseInt(f[l],10);break;case 4:this.hour=parseInt(f[l],10)+a;break;case 5:this.minute=parseInt(f[l],10)+c;break;case 6:this.second=parseInt(f[l],10);break;default:throw new Error("Wrong input string for conversion")}if(t===!1){let l=new Date(this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond);this.year=l.getUTCFullYear(),this.month=l.getUTCMonth(),this.day=l.getUTCDay(),this.hour=l.getUTCHours(),this.minute=l.getUTCMinutes(),this.second=l.getUTCSeconds(),this.millisecond=l.getUTCMilliseconds()}}toString(e="iso"){if(e==="iso"){let t=[];return t.push(Re(this.year,4)),t.push(Re(this.month,2)),t.push(Re(this.day,2)),t.push(Re(this.hour,2)),t.push(Re(this.minute,2)),t.push(Re(this.second,2)),this.millisecond!==0&&(t.push("."),t.push(Re(this.millisecond,3))),t.push("Z"),t.join("")}return super.toString(e)}toJSON(){return{...super.toJSON(),millisecond:this.millisecond}}};gc=on;M.GeneralizedTime=gc;on.NAME="GeneralizedTime";var mc,an=class extends dt{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=31}};mc=an;M.DATE=mc;an.NAME="DATE";var bc,cn=class extends dt{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=32}};bc=cn;M.TimeOfDay=bc;cn.NAME="TimeOfDay";var wc,ln=class extends dt{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=33}};wc=ln;M.DateTime=wc;ln.NAME="DateTime";var yc,un=class extends dt{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=34}};yc=un;M.Duration=yc;un.NAME="Duration";var vc,hn=class extends dt{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=14}};vc=hn;M.TIME=vc;hn.NAME="TIME";function $(s,e="utf8"){let t=vr[e];if(t==null)throw new Error(`Unsupported encoding "${e}"`);return t.encoder.encode(s).substring(1)}function dn(s){if(isNaN(s)||s<=0)throw new ce("random bytes length must be a Number bigger than 0");return as(s)}var js=class extends Error{constructor(e="An error occurred while verifying a message"){super(e),this.name="VerificationError"}},pn=class extends Error{constructor(e="Missing Web Crypto API"){super(e),this.name="WebCryptoMissingError"}};var xc={get(s=globalThis){let e=s.crypto;if(e?.subtle==null)throw new pn("Missing Web Crypto API. The most likely cause of this error is that this page is being accessed from an insecure context (i.e. not HTTPS). For more information and possible resolutions see https://github.com/libp2p/js-libp2p/blob/main/packages/crypto/README.md#web-crypto-api");return e}};var Nt=xc;async function Ec(s){let e=await Nt.get().subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:s,publicExponent:new Uint8Array([1,0,1]),hash:{name:"SHA-256"}},!0,["sign","verify"]),t=await _f(e);return{privateKey:t[0],publicKey:t[1]}}async function Sc(s,e){let t=await Nt.get().subtle.importKey("jwk",s,{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},!1,["sign"]),r=await Nt.get().subtle.sign({name:"RSASSA-PKCS1-v1_5"},t,e instanceof Uint8Array?e:e.subarray());return new Uint8Array(r,0,r.byteLength)}async function Bc(s,e,t){let r=await Nt.get().subtle.importKey("jwk",s,{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},!1,["verify"]);return Nt.get().subtle.verify({name:"RSASSA-PKCS1-v1_5"},r,e,t instanceof Uint8Array?t:t.subarray())}async function _f(s){if(s.privateKey==null||s.publicKey==null)throw new ce("Private and public key are required");return Promise.all([Nt.get().subtle.exportKey("jwk",s.privateKey),Nt.get().subtle.exportKey("jwk",s.publicKey)])}function ro(s){if(s.kty!=="RSA")throw new ce("invalid key type");if(s.n==null)throw new ce("invalid key modulus");return X(s.n,"base64url").length*8}var ws=class{type="RSA";_key;_raw;_multihash;constructor(e,t){this._key=e,this._multihash=t}get raw(){return this._raw==null&&(this._raw=Zs.jwkToPkix(this._key)),this._raw}toMultihash(){return this._multihash}toCID(){return Pe.createV1(114,this._multihash)}toString(){return te.encode(this.toMultihash().bytes).substring(1)}equals(e){return e==null||!(e.raw instanceof Uint8Array)?!1:Fe(this.raw,e.raw)}verify(e,t){return Bc(this._key,t,e)}},Js=class{type="RSA";_key;_raw;publicKey;constructor(e,t){this._key=e,this.publicKey=t}get raw(){return this._raw==null&&(this._raw=Zs.jwkToPkcs1(this._key)),this._raw}equals(e){return e==null||!(e.raw instanceof Uint8Array)?!1:Fe(this.raw,e.raw)}sign(e){return Sc(this._key,e)}};var gn=8192,no=18;function Ic(s){let{result:e}=so(s),t=e.valueBlock.value;return{n:$(Qe(t[1].toBigInt()),"base64url"),e:$(Qe(t[2].toBigInt()),"base64url"),d:$(Qe(t[3].toBigInt()),"base64url"),p:$(Qe(t[4].toBigInt()),"base64url"),q:$(Qe(t[5].toBigInt()),"base64url"),dp:$(Qe(t[6].toBigInt()),"base64url"),dq:$(Qe(t[7].toBigInt()),"base64url"),qi:$(Qe(t[8].toBigInt()),"base64url"),kty:"RSA",alg:"RS256"}}function Af(s){if(s.n==null||s.e==null||s.d==null||s.p==null||s.q==null||s.dp==null||s.dq==null||s.qi==null)throw new ce("JWK was missing components");let t=new ft({value:[new fe({value:0}),fe.fromBigInt(et(X(s.n,"base64url"))),fe.fromBigInt(et(X(s.e,"base64url"))),fe.fromBigInt(et(X(s.d,"base64url"))),fe.fromBigInt(et(X(s.p,"base64url"))),fe.fromBigInt(et(X(s.q,"base64url"))),fe.fromBigInt(et(X(s.dp,"base64url"))),fe.fromBigInt(et(X(s.dq,"base64url"))),fe.fromBigInt(et(X(s.qi,"base64url")))]}).toBER();return new Uint8Array(t,0,t.byteLength)}function _c(s){let{result:e}=so(s),t=e.valueBlock.value[1].valueBlock.value[0].valueBlock.value;return{kty:"RSA",n:$(Qe(t[0].toBigInt()),"base64url"),e:$(Qe(t[1].toBigInt()),"base64url")}}function io(s){if(s.n==null||s.e==null)throw new ce("JWK was missing components");let t=new ft({value:[new ft({value:[new ms({value:"1.2.840.113549.1.1.1"}),new ps]}),new gs({valueHex:new ft({value:[fe.fromBigInt(et(X(s.n,"base64url"))),fe.fromBigInt(et(X(s.e,"base64url")))]}).toBER()})]}).toBER();return new Uint8Array(t,0,t.byteLength)}function Qe(s){let e=s.toString(16);e.length%2>0&&(e=`0${e}`);let t=e.length/2,r=new Uint8Array(t),n=0,i=0;for(;n<t;)r[n]=parseInt(e.slice(i,i+2),16),n+=1,i+=2;return r}function et(s){let e=[];return s.forEach(function(t){let r=t.toString(16);r.length%2>0&&(r=`0${r}`),e.push(r)}),BigInt("0x"+e.join(""))}function Ac(s){let e=Ic(s);return kc(e)}function oo(s){let e=_c(s);if(ro(e)>gn)throw new Yt("Key size is too large");let t=fs(Xe.encode({Type:ie.RSA,Data:s})),r=He(no,t);return new ws(e,r)}function kc(s){if(ro(s)>gn)throw new ce("Key size is too large");let e=Mc(s),t=fs(Xe.encode({Type:ie.RSA,Data:io(e.publicKey)})),r=He(no,t);return new Js(e.privateKey,new ws(e.publicKey,r))}async function Tc(s){if(s>gn)throw new ce("Key size is too large");let e=await Ec(s),t=fs(Xe.encode({Type:ie.RSA,Data:io(e.publicKey)})),r=He(no,t);return new Js(e.privateKey,new ws(e.publicKey,r))}function Mc(s){if(s==null)throw new ce("Missing key parameter");return{privateKey:s,publicKey:{kty:s.kty,n:s.n,e:s.e}}}var mn=class extends os{constructor(e,t){super(),this.finished=!1,this.destroyed=!1,Qo(e);let r=Cs(t);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;let n=this.blockLen,i=new Uint8Array(n);i.set(r.length>n?e.create().update(r).digest():r);for(let o=0;o<i.length;o++)i[o]^=54;this.iHash.update(i),this.oHash=e.create();for(let o=0;o<i.length;o++)i[o]^=106;this.oHash.update(i),i.fill(0)}update(e){return is(this),this.iHash.update(e),this}digestInto(e){is(this),ns(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){let e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));let{oHash:t,iHash:r,finished:n,destroyed:i,blockLen:o,outputLen:a}=this;return e=e,e.finished=n,e.destroyed=i,e.blockLen=o,e.outputLen=a,e.oHash=t._cloneInto(e.oHash),e.iHash=r._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}},ao=(s,e,t)=>new mn(s,e).update(t).digest();ao.create=(s,e)=>new mn(s,e);function Pc(s){s.lowS!==void 0&&Ge("lowS",s.lowS),s.prehash!==void 0&&Ge("prehash",s.prehash)}function kf(s){let e=Ls(s);Je(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});let{endo:t,Fp:r,a:n}=e;if(t){if(!r.eql(n,r.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if(typeof t!="object"||typeof t.beta!="bigint"||typeof t.splitScalar!="function")throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...e})}var{bytesToNumberBE:Tf,hexToBytes:Mf}=fr,mt={Err:class extends Error{constructor(e=""){super(e)}},_tlv:{encode:(s,e)=>{let{Err:t}=mt;if(s<0||s>256)throw new t("tlv.encode: wrong tag");if(e.length&1)throw new t("tlv.encode: unpadded data");let r=e.length/2,n=Ht(r);if(n.length/2&128)throw new t("tlv.encode: long form length too big");let i=r>127?Ht(n.length/2|128):"";return`${Ht(s)}${i}${n}${e}`},decode(s,e){let{Err:t}=mt,r=0;if(s<0||s>256)throw new t("tlv.encode: wrong tag");if(e.length<2||e[r++]!==s)throw new t("tlv.decode: wrong tlv");let n=e[r++],i=!!(n&128),o=0;if(!i)o=n;else{let c=n&127;if(!c)throw new t("tlv.decode(long): indefinite length not supported");if(c>4)throw new t("tlv.decode(long): byte length is too big");let h=e.subarray(r,r+c);if(h.length!==c)throw new t("tlv.decode: length bytes not complete");if(h[0]===0)throw new t("tlv.decode(long): zero leftmost byte");for(let f of h)o=o<<8|f;if(r+=c,o<128)throw new t("tlv.decode(long): not minimal encoding")}let a=e.subarray(r,r+o);if(a.length!==o)throw new t("tlv.decode: wrong value length");return{v:a,l:e.subarray(r+o)}}},_int:{encode(s){let{Err:e}=mt;if(s<bt)throw new e("integer: negative integers are not allowed");let t=Ht(s);if(Number.parseInt(t[0],16)&8&&(t="00"+t),t.length&1)throw new e("unexpected assertion");return t},decode(s){let{Err:e}=mt;if(s[0]&128)throw new e("Invalid signature integer: negative");if(s[0]===0&&!(s[1]&128))throw new e("Invalid signature integer: unnecessary leading zero");return Tf(s)}},toSig(s){let{Err:e,_int:t,_tlv:r}=mt,n=typeof s=="string"?Mf(s):s;ls(n);let{v:i,l:o}=r.decode(48,n);if(o.length)throw new e("Invalid signature: left bytes after parsing");let{v:a,l:c}=r.decode(2,i),{v:h,l:f}=r.decode(2,c);if(f.length)throw new e("Invalid signature: left bytes after parsing");return{r:t.decode(a),s:t.decode(h)}},hexFromSig(s){let{_tlv:e,_int:t}=mt,r=`${e.encode(2,t.encode(s.r))}${e.encode(2,t.encode(s.s))}`;return e.encode(48,r)}},bt=BigInt(0),le=BigInt(1),S0=BigInt(2),Dc=BigInt(3),B0=BigInt(4);function Pf(s){let e=kf(s),{Fp:t}=e,r=_t(e.n,e.nBitLength),n=e.toBytes||((m,p,b)=>{let y=p.toAffine();return ct(Uint8Array.from([4]),t.toBytes(y.x),t.toBytes(y.y))}),i=e.fromBytes||(m=>{let p=m.subarray(1),b=t.fromBytes(p.subarray(0,t.BYTES)),y=t.fromBytes(p.subarray(t.BYTES,2*t.BYTES));return{x:b,y}});function o(m){let{a:p,b}=e,y=t.sqr(m),w=t.mul(y,m);return t.add(t.add(w,t.mul(m,p)),b)}if(!t.eql(t.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function a(m){return Ns(m,le,e.n)}function c(m){let{allowedPrivateKeyLengths:p,nByteLength:b,wrapPrivateKey:y,n:w}=e;if(p&&typeof m!="bigint"){if(St(m)&&(m=ot(m)),typeof m!="string"||!p.includes(m.length))throw new Error("Invalid key");m=m.padStart(b*2,"0")}let I;try{I=typeof m=="bigint"?m:at(ee("private key",m,b))}catch{throw new Error(`private key must be ${b} bytes, hex or bigint, not ${typeof m}`)}return y&&(I=W(I,w)),De("private key",I,le,w),I}function h(m){if(!(m instanceof g))throw new Error("ProjectivePoint expected")}let f=Gt((m,p)=>{let{px:b,py:y,pz:w}=m;if(t.eql(w,t.ONE))return{x:b,y};let I=m.is0();p==null&&(p=I?t.ONE:t.inv(w));let x=t.mul(b,p),B=t.mul(y,p),E=t.mul(w,p);if(I)return{x:t.ZERO,y:t.ZERO};if(!t.eql(E,t.ONE))throw new Error("invZ was invalid");return{x,y:B}}),l=Gt(m=>{if(m.is0()){if(e.allowInfinityPoint&&!t.is0(m.py))return;throw new Error("bad point: ZERO")}let{x:p,y:b}=m.toAffine();if(!t.isValid(p)||!t.isValid(b))throw new Error("bad point: x or y not FE");let y=t.sqr(b),w=o(p);if(!t.eql(y,w))throw new Error("bad point: equation left != right");if(!m.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class g{constructor(p,b,y){if(this.px=p,this.py=b,this.pz=y,p==null||!t.isValid(p))throw new Error("x required");if(b==null||!t.isValid(b))throw new Error("y required");if(y==null||!t.isValid(y))throw new Error("z required");Object.freeze(this)}static fromAffine(p){let{x:b,y}=p||{};if(!p||!t.isValid(b)||!t.isValid(y))throw new Error("invalid affine point");if(p instanceof g)throw new Error("projective point not allowed");let w=I=>t.eql(I,t.ZERO);return w(b)&&w(y)?g.ZERO:new g(b,y,t.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(p){let b=t.invertBatch(p.map(y=>y.pz));return p.map((y,w)=>y.toAffine(b[w])).map(g.fromAffine)}static fromHex(p){let b=g.fromAffine(i(ee("pointHex",p)));return b.assertValidity(),b}static fromPrivateKey(p){return g.BASE.multiply(c(p))}static msm(p,b){return gr(g,r,p,b)}_setWindowSize(p){d.setWindowSize(this,p)}assertValidity(){l(this)}hasEvenY(){let{y:p}=this.toAffine();if(t.isOdd)return!t.isOdd(p);throw new Error("Field doesn't support isOdd")}equals(p){h(p);let{px:b,py:y,pz:w}=this,{px:I,py:x,pz:B}=p,E=t.eql(t.mul(b,B),t.mul(I,w)),_=t.eql(t.mul(y,B),t.mul(x,w));return E&&_}negate(){return new g(this.px,t.neg(this.py),this.pz)}double(){let{a:p,b}=e,y=t.mul(b,Dc),{px:w,py:I,pz:x}=this,B=t.ZERO,E=t.ZERO,_=t.ZERO,k=t.mul(w,w),z=t.mul(I,I),L=t.mul(x,x),U=t.mul(w,I);return U=t.add(U,U),_=t.mul(w,x),_=t.add(_,_),B=t.mul(p,_),E=t.mul(y,L),E=t.add(B,E),B=t.sub(z,E),E=t.add(z,E),E=t.mul(B,E),B=t.mul(U,B),_=t.mul(y,_),L=t.mul(p,L),U=t.sub(k,L),U=t.mul(p,U),U=t.add(U,_),_=t.add(k,k),k=t.add(_,k),k=t.add(k,L),k=t.mul(k,U),E=t.add(E,k),L=t.mul(I,x),L=t.add(L,L),k=t.mul(L,U),B=t.sub(B,k),_=t.mul(L,z),_=t.add(_,_),_=t.add(_,_),new g(B,E,_)}add(p){h(p);let{px:b,py:y,pz:w}=this,{px:I,py:x,pz:B}=p,E=t.ZERO,_=t.ZERO,k=t.ZERO,z=e.a,L=t.mul(e.b,Dc),U=t.mul(b,I),se=t.mul(y,x),T=t.mul(w,B),P=t.add(b,y),S=t.add(I,x);P=t.mul(P,S),S=t.add(U,se),P=t.sub(P,S),S=t.add(b,w);let v=t.add(I,B);return S=t.mul(S,v),v=t.add(U,T),S=t.sub(S,v),v=t.add(y,w),E=t.add(x,B),v=t.mul(v,E),E=t.add(se,T),v=t.sub(v,E),k=t.mul(z,S),E=t.mul(L,T),k=t.add(E,k),E=t.sub(se,k),k=t.add(se,k),_=t.mul(E,k),se=t.add(U,U),se=t.add(se,U),T=t.mul(z,T),S=t.mul(L,S),se=t.add(se,T),T=t.sub(U,T),T=t.mul(z,T),S=t.add(S,T),U=t.mul(se,S),_=t.add(_,U),U=t.mul(v,S),E=t.mul(P,E),E=t.sub(E,U),U=t.mul(P,se),k=t.mul(v,k),k=t.add(k,U),new g(E,_,k)}subtract(p){return this.add(p.negate())}is0(){return this.equals(g.ZERO)}wNAF(p){return d.wNAFCached(this,p,g.normalizeZ)}multiplyUnsafe(p){De("scalar",p,bt,e.n);let b=g.ZERO;if(p===bt)return b;if(p===le)return this;let{endo:y}=e;if(!y)return d.unsafeLadder(this,p);let{k1neg:w,k1:I,k2neg:x,k2:B}=y.splitScalar(p),E=b,_=b,k=this;for(;I>bt||B>bt;)I&le&&(E=E.add(k)),B&le&&(_=_.add(k)),k=k.double(),I>>=le,B>>=le;return w&&(E=E.negate()),x&&(_=_.negate()),_=new g(t.mul(_.px,y.beta),_.py,_.pz),E.add(_)}multiply(p){let{endo:b,n:y}=e;De("scalar",p,le,y);let w,I;if(b){let{k1neg:x,k1:B,k2neg:E,k2:_}=b.splitScalar(p),{p:k,f:z}=this.wNAF(B),{p:L,f:U}=this.wNAF(_);k=d.constTimeNegate(x,k),L=d.constTimeNegate(E,L),L=new g(t.mul(L.px,b.beta),L.py,L.pz),w=k.add(L),I=z.add(U)}else{let{p:x,f:B}=this.wNAF(p);w=x,I=B}return g.normalizeZ([w,I])[0]}multiplyAndAddUnsafe(p,b,y){let w=g.BASE,I=(B,E)=>E===bt||E===le||!B.equals(w)?B.multiplyUnsafe(E):B.multiply(E),x=I(this,b).add(I(p,y));return x.is0()?void 0:x}toAffine(p){return f(this,p)}isTorsionFree(){let{h:p,isTorsionFree:b}=e;if(p===le)return!0;if(b)return b(g,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){let{h:p,clearCofactor:b}=e;return p===le?this:b?b(g,this):this.multiplyUnsafe(e.h)}toRawBytes(p=!0){return Ge("isCompressed",p),this.assertValidity(),n(g,this,p)}toHex(p=!0){return Ge("isCompressed",p),ot(this.toRawBytes(p))}}g.BASE=new g(e.Gx,e.Gy,t.ONE),g.ZERO=new g(t.ZERO,t.ONE,t.ZERO);let u=e.nBitLength,d=pr(g,e.endo?Math.ceil(u/2):u);return{CURVE:e,ProjectivePoint:g,normPrivateKeyToScalar:c,weierstrassEquation:o,isWithinCurveOrder:a}}function Df(s){let e=Ls(s);return Je(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}function Cc(s){let e=Df(s),{Fp:t,n:r}=e,n=t.BYTES+1,i=2*t.BYTES+1;function o(T){return W(T,r)}function a(T){return dr(T,r)}let{ProjectivePoint:c,normPrivateKeyToScalar:h,weierstrassEquation:f,isWithinCurveOrder:l}=Pf({...e,toBytes(T,P,S){let v=P.toAffine(),A=t.toBytes(v.x),D=ct;return Ge("isCompressed",S),S?D(Uint8Array.from([P.hasEvenY()?2:3]),A):D(Uint8Array.from([4]),A,t.toBytes(v.y))},fromBytes(T){let P=T.length,S=T[0],v=T.subarray(1);if(P===n&&(S===2||S===3)){let A=at(v);if(!Ns(A,le,t.ORDER))throw new Error("Point is not on curve");let D=f(A),C;try{C=t.sqrt(D)}catch(O){let V=O instanceof Error?": "+O.message:"";throw new Error("Point is not on curve"+V)}let N=(C&le)===le;return(S&1)===1!==N&&(C=t.neg(C)),{x:A,y:C}}else if(P===i&&S===4){let A=t.fromBytes(v.subarray(0,t.BYTES)),D=t.fromBytes(v.subarray(t.BYTES,2*t.BYTES));return{x:A,y:D}}else throw new Error(`Point of length ${P} was invalid. Expected ${n} compressed bytes or ${i} uncompressed bytes`)}}),g=T=>ot(It(T,e.nByteLength));function u(T){let P=r>>le;return T>P}function d(T){return u(T)?o(-T):T}let m=(T,P,S)=>at(T.slice(P,S));class p{constructor(P,S,v){this.r=P,this.s=S,this.recovery=v,this.assertValidity()}static fromCompact(P){let S=e.nByteLength;return P=ee("compactSignature",P,S*2),new p(m(P,0,S),m(P,S,2*S))}static fromDER(P){let{r:S,s:v}=mt.toSig(ee("DER",P));return new p(S,v)}assertValidity(){De("r",this.r,le,r),De("s",this.s,le,r)}addRecoveryBit(P){return new p(this.r,this.s,P)}recoverPublicKey(P){let{r:S,s:v,recovery:A}=this,D=B(ee("msgHash",P));if(A==null||![0,1,2,3].includes(A))throw new Error("recovery id invalid");let C=A===2||A===3?S+e.n:S;if(C>=t.ORDER)throw new Error("recovery id 2 or 3 invalid");let N=A&1?"03":"02",F=c.fromHex(N+g(C)),O=a(C),V=o(-D*O),K=o(v*O),j=c.BASE.multiplyAndAddUnsafe(F,V,K);if(!j)throw new Error("point at infinify");return j.assertValidity(),j}hasHighS(){return u(this.s)}normalizeS(){return this.hasHighS()?new p(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return Ot(this.toDERHex())}toDERHex(){return mt.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return Ot(this.toCompactHex())}toCompactHex(){return g(this.r)+g(this.s)}}let b={isValidPrivateKey(T){try{return h(T),!0}catch{return!1}},normPrivateKeyToScalar:h,randomPrivateKey:()=>{let T=hi(e.n);return fa(e.randomBytes(T),e.n)},precompute(T=8,P=c.BASE){return P._setWindowSize(T),P.multiply(BigInt(3)),P}};function y(T,P=!0){return c.fromPrivateKey(T).toRawBytes(P)}function w(T){let P=St(T),S=typeof T=="string",v=(P||S)&&T.length;return P?v===n||v===i:S?v===2*n||v===2*i:T instanceof c}function I(T,P,S=!0){if(w(T))throw new Error("first arg must be private key");if(!w(P))throw new Error("second arg must be public key");return c.fromHex(P).multiply(h(T)).toRawBytes(S)}let x=e.bits2int||function(T){let P=at(T),S=T.length*8-e.nBitLength;return S>0?P>>BigInt(S):P},B=e.bits2int_modN||function(T){return o(x(T))},E=Rs(e.nBitLength);function _(T){return De(`num < 2^${e.nBitLength}`,T,bt,E),It(T,e.nByteLength)}function k(T,P,S=z){if(["recovered","canonical"].some(ae=>ae in S))throw new Error("sign() legacy options not supported");let{hash:v,randomBytes:A}=e,{lowS:D,prehash:C,extraEntropy:N}=S;D==null&&(D=!0),T=ee("msgHash",T),Pc(S),C&&(T=ee("prehashed msgHash",v(T)));let F=B(T),O=h(P),V=[_(O),_(F)];if(N!=null&&N!==!1){let ae=N===!0?A(t.BYTES):N;V.push(ee("extraEntropy",ae))}let K=ct(...V),j=F;function oe(ae){let ue=x(ae);if(!l(ue))return;let ye=a(ue),re=c.BASE.multiply(ue).toAffine(),Me=o(re.x);if(Me===bt)return;let st=o(ye*o(j+Me*O));if(st===bt)return;let ks=(re.x===Me?0:2)|Number(re.y&le),Ts=st;return D&&u(st)&&(Ts=d(st),ks^=1),new p(Me,Ts,ks)}return{seed:K,k2sig:oe}}let z={lowS:e.lowS,prehash:!1},L={lowS:e.lowS,prehash:!1};function U(T,P,S=z){let{seed:v,k2sig:A}=k(T,P,S),D=e;return ai(D.hash.outputLen,D.nByteLength,D.hmac)(v,A)}c.BASE._setWindowSize(8);function se(T,P,S,v=L){let A=T;if(P=ee("msgHash",P),S=ee("publicKey",S),"strict"in v)throw new Error("options.strict was renamed to lowS");Pc(v);let{lowS:D,prehash:C}=v,N,F;try{if(typeof A=="string"||St(A))try{N=p.fromDER(A)}catch(re){if(!(re instanceof mt.Err))throw re;N=p.fromCompact(A)}else if(typeof A=="object"&&typeof A.r=="bigint"&&typeof A.s=="bigint"){let{r:re,s:Me}=A;N=new p(re,Me)}else throw new Error("PARSE");F=c.fromHex(S)}catch(re){if(re.message==="PARSE")throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(D&&N.hasHighS())return!1;C&&(P=e.hash(P));let{r:O,s:V}=N,K=B(P),j=a(V),oe=o(K*j),ae=o(O*j),ue=c.BASE.multiplyAndAddUnsafe(F,oe,ae)?.toAffine();return ue?o(ue.x)===O:!1}return{CURVE:e,getPublicKey:y,getSharedSecret:I,sign:U,verify:se,ProjectivePoint:c,Signature:p,utils:b}}function Cf(s){return{hash:s,hmac:(e,...t)=>ao(s,e,Qn(...t)),randomBytes:as}}function Nc(s,e){let t=r=>Cc({...s,...Cf(r)});return Object.freeze({...t(e),create:t})}var Uc=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),Rc=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),Nf=BigInt(1),co=BigInt(2),Lc=(s,e)=>(s+e/co)/e;function Rf(s){let e=Uc,t=BigInt(3),r=BigInt(6),n=BigInt(11),i=BigInt(22),o=BigInt(23),a=BigInt(44),c=BigInt(88),h=s*s*s%e,f=h*h*s%e,l=J(f,t,e)*f%e,g=J(l,t,e)*f%e,u=J(g,co,e)*h%e,d=J(u,n,e)*u%e,m=J(d,i,e)*d%e,p=J(m,a,e)*m%e,b=J(p,c,e)*p%e,y=J(b,a,e)*m%e,w=J(y,t,e)*f%e,I=J(w,o,e)*d%e,x=J(I,r,e)*h%e,B=J(x,co,e);if(!lo.eql(lo.sqr(B),s))throw new Error("Cannot find square root");return B}var lo=_t(Uc,void 0,void 0,{sqrt:Rf}),jt=Nc({a:BigInt(0),b:BigInt(7),Fp:lo,n:Rc,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:s=>{let e=Rc,t=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-Nf*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),n=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),i=t,o=BigInt("0x100000000000000000000000000000000"),a=Lc(i*s,e),c=Lc(-r*s,e),h=W(s-a*t-c*n,e),f=W(-a*r-c*i,e),l=h>o,g=f>o;if(l&&(h=e-h),g&&(f=e-f),h>o||f>o)throw new Error("splitScalar: Endomorphism failed, k="+s);return{k1neg:l,k1:h,k2neg:g,k2:f}}}},fs),C0=BigInt(0);var N0=jt.ProjectivePoint;function Rt(s,e){e==null&&(e=s.reduce((n,i)=>n+i.length,0));let t=de(e),r=0;for(let n of s)t.set(n,r),r+=n.length;return t}function Fc(s){return s==null?!1:typeof s.then=="function"&&typeof s.catch=="function"&&typeof s.finally=="function"}function Vc(s,e,t){let r=Tt.digest(t instanceof Uint8Array?t:t.subarray());if(Fc(r))return r.then(({digest:n})=>jt.verify(e,n,s)).catch(n=>{throw new js(String(n))});try{return jt.verify(e,r.digest,s)}catch(n){throw new js(String(n))}}var bn=class{type="secp256k1";raw;_key;constructor(e){this._key=Oc(e),this.raw=Hc(this._key)}toMultihash(){return nt.digest(At(this))}toCID(){return Pe.createV1(114,this.toMultihash())}toString(){return te.encode(this.toMultihash().bytes).substring(1)}equals(e){return e==null||!(e.raw instanceof Uint8Array)?!1:Fe(this.raw,e.raw)}verify(e,t){return Vc(this._key,t,e)}};function uo(s){return new bn(s)}function Hc(s){return jt.ProjectivePoint.fromHex(s).toRawBytes(!0)}function Oc(s){try{return jt.ProjectivePoint.fromHex(s),s}catch(e){throw new Yt(String(e))}}function Xs(s){let{Type:e,Data:t}=Xe.decode(s),r=t??new Uint8Array;switch(e){case ie.RSA:return oo(r);case ie.Ed25519:return mi(r);case ie.secp256k1:return uo(r);default:throw new Qt}}function zc(s){let{Type:e,Data:t}=Xe.decode(s.digest),r=t??new Uint8Array;switch(e){case ie.Ed25519:return mi(r);case ie.secp256k1:return uo(r);default:throw new Qt}}function At(s){return Xe.encode({Type:ie[s.type],Data:s.raw})}var Gc=Symbol.for("nodejs.util.inspect.custom"),Lf=114,Ys=class{type;multihash;publicKey;string;constructor(e){this.type=e.type,this.multihash=e.multihash,Object.defineProperty(this,"string",{enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return`PeerId(${this.toString()})`}[Gn]=!0;toString(){return this.string==null&&(this.string=te.encode(this.multihash.bytes).slice(1)),this.string}toMultihash(){return this.multihash}toCID(){return Pe.createV1(Lf,this.multihash)}toJSON(){return this.toString()}equals(e){if(e==null)return!1;if(e instanceof Uint8Array)return Fe(this.multihash.bytes,e);if(typeof e=="string")return this.toString()===e;if(e?.toMultihash()?.bytes!=null)return Fe(this.multihash.bytes,e.toMultihash().bytes);throw new Error("not valid Id")}[Gc](){return`PeerId(${this.toString()})`}},wn=class extends Ys{type="RSA";publicKey;constructor(e){super({...e,type:"RSA"}),this.publicKey=e.publicKey}},yn=class extends Ys{type="Ed25519";publicKey;constructor(e){super({...e,type:"Ed25519"}),this.publicKey=e.publicKey}},vn=class extends Ys{type="secp256k1";publicKey;constructor(e){super({...e,type:"secp256k1"}),this.publicKey=e.publicKey}},Uf=2336,xn=class{type="url";multihash;publicKey;url;constructor(e){this.url=e.toString(),this.multihash=nt.digest(X(this.url))}[Gc](){return`PeerId(${this.url})`}[Gn]=!0;toString(){return this.toCID().toString()}toMultihash(){return this.multihash}toCID(){return Pe.createV1(Uf,this.toMultihash())}toJSON(){return this.toString()}equals(e){return e==null?!1:(e instanceof Uint8Array&&(e=$(e)),e.toString()===this.toString())}};function Zt(s,e){let t;if(s.charAt(0)==="1"||s.charAt(0)==="Q")t=Oe(te.decode(`z${s}`));else{if(e==null)throw new ce('Please pass a multibase decoder for strings that do not start with "1" or "Q"');t=Oe(e.decode(s))}return Qs(t)}function Qs(s){if(Vf(s))return new wn({multihash:s});if(Ff(s))try{let e=zc(s);if(e.type==="Ed25519")return new yn({multihash:s,publicKey:e});if(e.type==="secp256k1")return new vn({multihash:s,publicKey:e})}catch{let t=$(s.digest);return new xn(new URL(t))}throw new ir("Supplied PeerID Multihash is invalid")}function Ff(s){return s.code===nt.code}function Vf(s){return s.code===Tt.code}var Kc=Symbol.for("@achingbrain/uint8arraylist");function qc(s,e){if(e==null||e<0)throw new RangeError("index is out of bounds");let t=0;for(let r of s){let n=t+r.byteLength;if(e<n)return{buf:r,index:e-t};t=n}throw new RangeError("index is out of bounds")}function En(s){return!!s?.[Kc]}var ys=class s{bufs;length;[Kc]=!0;constructor(...e){this.bufs=[],this.length=0,e.length>0&&this.appendAll(e)}*[Symbol.iterator](){yield*this.bufs}get byteLength(){return this.length}append(...e){this.appendAll(e)}appendAll(e){let t=0;for(let r of e)if(r instanceof Uint8Array)t+=r.byteLength,this.bufs.push(r);else if(En(r))t+=r.byteLength,this.bufs.push(...r.bufs);else throw new Error("Could not append value, must be an Uint8Array or a Uint8ArrayList");this.length+=t}prepend(...e){this.prependAll(e)}prependAll(e){let t=0;for(let r of e.reverse())if(r instanceof Uint8Array)t+=r.byteLength,this.bufs.unshift(r);else if(En(r))t+=r.byteLength,this.bufs.unshift(...r.bufs);else throw new Error("Could not prepend value, must be an Uint8Array or a Uint8ArrayList");this.length+=t}get(e){let t=qc(this.bufs,e);return t.buf[t.index]}set(e,t){let r=qc(this.bufs,e);r.buf[r.index]=t}write(e,t=0){if(e instanceof Uint8Array)for(let r=0;r<e.length;r++)this.set(t+r,e[r]);else if(En(e))for(let r=0;r<e.length;r++)this.set(t+r,e.get(r));else throw new Error("Could not write value, must be an Uint8Array or a Uint8ArrayList")}consume(e){if(e=Math.trunc(e),!(Number.isNaN(e)||e<=0)){if(e===this.byteLength){this.bufs=[],this.length=0;return}for(;this.bufs.length>0;)if(e>=this.bufs[0].byteLength)e-=this.bufs[0].byteLength,this.length-=this.bufs[0].byteLength,this.bufs.shift();else{this.bufs[0]=this.bufs[0].subarray(e),this.length-=e;break}}}slice(e,t){let{bufs:r,length:n}=this._subList(e,t);return Rt(r,n)}subarray(e,t){let{bufs:r,length:n}=this._subList(e,t);return r.length===1?r[0]:Rt(r,n)}sublist(e,t){let{bufs:r,length:n}=this._subList(e,t),i=new s;return i.length=n,i.bufs=[...r],i}_subList(e,t){if(e=e??0,t=t??this.length,e<0&&(e=this.length+e),t<0&&(t=this.length+t),e<0||t>this.length)throw new RangeError("index is out of bounds");if(e===t)return{bufs:[],length:0};if(e===0&&t===this.length)return{bufs:this.bufs,length:this.length};let r=[],n=0;for(let i=0;i<this.bufs.length;i++){let o=this.bufs[i],a=n,c=a+o.byteLength;if(n=c,e>=c)continue;let h=e>=a&&e<c,f=t>a&&t<=c;if(h&&f){if(e===a&&t===c){r.push(o);break}let l=e-a;r.push(o.subarray(l,l+(t-e)));break}if(h){if(e===0){r.push(o);continue}r.push(o.subarray(e-a));continue}if(f){if(t===c){r.push(o);break}r.push(o.subarray(0,t-a));break}r.push(o)}return{bufs:r,length:t-e}}indexOf(e,t=0){if(!En(e)&&!(e instanceof Uint8Array))throw new TypeError('The "value" argument must be a Uint8ArrayList or Uint8Array');let r=e instanceof Uint8Array?e:e.subarray();if(t=Number(t??0),isNaN(t)&&(t=0),t<0&&(t=this.length+t),t<0&&(t=0),e.length===0)return t>this.length?this.length:t;let n=r.byteLength;if(n===0)throw new TypeError("search must be at least 1 byte long");let i=256,o=new Int32Array(i);for(let l=0;l<i;l++)o[l]=-1;for(let l=0;l<n;l++)o[r[l]]=l;let a=o,c=this.byteLength-r.byteLength,h=r.byteLength-1,f;for(let l=t;l<=c;l+=f){f=0;for(let g=h;g>=0;g--){let u=this.get(l+g);if(r[g]!==u){f=Math.max(1,g-a[u]);break}}if(f===0)return l}return-1}getInt8(e){let t=this.subarray(e,e+1);return new DataView(t.buffer,t.byteOffset,t.byteLength).getInt8(0)}setInt8(e,t){let r=de(1);new DataView(r.buffer,r.byteOffset,r.byteLength).setInt8(0,t),this.write(r,e)}getInt16(e,t){let r=this.subarray(e,e+2);return new DataView(r.buffer,r.byteOffset,r.byteLength).getInt16(0,t)}setInt16(e,t,r){let n=lt(2);new DataView(n.buffer,n.byteOffset,n.byteLength).setInt16(0,t,r),this.write(n,e)}getInt32(e,t){let r=this.subarray(e,e+4);return new DataView(r.buffer,r.byteOffset,r.byteLength).getInt32(0,t)}setInt32(e,t,r){let n=lt(4);new DataView(n.buffer,n.byteOffset,n.byteLength).setInt32(0,t,r),this.write(n,e)}getBigInt64(e,t){let r=this.subarray(e,e+8);return new DataView(r.buffer,r.byteOffset,r.byteLength).getBigInt64(0,t)}setBigInt64(e,t,r){let n=lt(8);new DataView(n.buffer,n.byteOffset,n.byteLength).setBigInt64(0,t,r),this.write(n,e)}getUint8(e){let t=this.subarray(e,e+1);return new DataView(t.buffer,t.byteOffset,t.byteLength).getUint8(0)}setUint8(e,t){let r=de(1);new DataView(r.buffer,r.byteOffset,r.byteLength).setUint8(0,t),this.write(r,e)}getUint16(e,t){let r=this.subarray(e,e+2);return new DataView(r.buffer,r.byteOffset,r.byteLength).getUint16(0,t)}setUint16(e,t,r){let n=lt(2);new DataView(n.buffer,n.byteOffset,n.byteLength).setUint16(0,t,r),this.write(n,e)}getUint32(e,t){let r=this.subarray(e,e+4);return new DataView(r.buffer,r.byteOffset,r.byteLength).getUint32(0,t)}setUint32(e,t,r){let n=lt(4);new DataView(n.buffer,n.byteOffset,n.byteLength).setUint32(0,t,r),this.write(n,e)}getBigUint64(e,t){let r=this.subarray(e,e+8);return new DataView(r.buffer,r.byteOffset,r.byteLength).getBigUint64(0,t)}setBigUint64(e,t,r){let n=lt(8);new DataView(n.buffer,n.byteOffset,n.byteLength).setBigUint64(0,t,r),this.write(n,e)}getFloat32(e,t){let r=this.subarray(e,e+4);return new DataView(r.buffer,r.byteOffset,r.byteLength).getFloat32(0,t)}setFloat32(e,t,r){let n=lt(4);new DataView(n.buffer,n.byteOffset,n.byteLength).setFloat32(0,t,r),this.write(n,e)}getFloat64(e,t){let r=this.subarray(e,e+8);return new DataView(r.buffer,r.byteOffset,r.byteLength).getFloat64(0,t)}setFloat64(e,t,r){let n=lt(8);new DataView(n.buffer,n.byteOffset,n.byteLength).setFloat64(0,t,r),this.write(n,e)}equals(e){if(e==null||!(e instanceof s)||e.bufs.length!==this.bufs.length)return!1;for(let t=0;t<this.bufs.length;t++)if(!Fe(this.bufs[t],e.bufs[t]))return!1;return!0}static fromUint8Arrays(e,t){let r=new s;return r.bufs=e,t==null&&(t=e.reduce((n,i)=>n+i.byteLength,0)),r.length=t,r}};function Sn(s){return s[Symbol.asyncIterator]!=null}var Bn=s=>{let e=Ee(s),t=de(e);return Si(s,t),Bn.bytes=e,t};Bn.bytes=0;function vs(s,e){e=e??{};let t=e.lengthEncoder??Bn;function*r(n){let i=t(n.byteLength);i instanceof Uint8Array?yield i:yield*i,n instanceof Uint8Array?yield n:yield*n}return Sn(s)?async function*(){for await(let n of s)yield*r(n)}():function*(){for(let n of s)yield*r(n)}()}vs.single=(s,e)=>{e=e??{};let t=e.lengthEncoder??Bn;return new ys(t(s.byteLength),s)};var In=class extends Error{name="InvalidMessageLengthError";code="ERR_INVALID_MSG_LENGTH"},_n=class extends Error{name="InvalidDataLengthError";code="ERR_MSG_DATA_TOO_LONG"},An=class extends Error{name="InvalidDataLengthLengthError";code="ERR_MSG_LENGTH_TOO_LONG"},er=class extends Error{name="UnexpectedEOFError";code="ERR_UNEXPECTED_EOF"};var Hf=8,Of=1024*1024*4,Jt;(function(s){s[s.LENGTH=0]="LENGTH",s[s.DATA=1]="DATA"})(Jt||(Jt={}));var ho=s=>{let e=us(s);return ho.bytes=Ee(e),e};ho.bytes=0;function tr(s,e){let t=new ys,r=Jt.LENGTH,n=-1,i=e?.lengthDecoder??ho,o=e?.maxLengthLength??Hf,a=e?.maxDataLength??Of;function*c(){for(;t.byteLength>0;){if(r===Jt.LENGTH)try{if(n=i(t),n<0)throw new In("Invalid message length");if(n>a)throw new _n("Message length too long");let h=i.bytes;t.consume(h),e?.onLength!=null&&e.onLength(n),r=Jt.DATA}catch(h){if(h instanceof RangeError){if(t.byteLength>o)throw new An("Message length length too long");break}throw h}if(r===Jt.DATA){if(t.byteLength<n)break;let h=t.sublist(0,n);t.consume(n),e?.onData!=null&&e.onData(h),yield h,r=Jt.LENGTH}}}return Sn(s)?async function*(){for await(let h of s)t.append(h),yield*c();if(t.byteLength>0)throw new er("Unexpected end of input")}():function*(){for(let h of s)t.append(h),yield*c();if(t.byteLength>0)throw new er("Unexpected end of input")}()}tr.fromReader=(s,e)=>{let t=1,r=async function*(){for(;;)try{let{done:i,value:o}=await s.next(t);if(i===!0)return;o!=null&&(yield o)}catch(i){if(i.code==="ERR_UNDER_READ")return{done:!0,value:null};throw i}finally{t=1}}();return tr(r,{...e??{},onLength:i=>{t=i}})};function kn(){let s={};return s.promise=new Promise((e,t)=>{s.resolve=e,s.reject=t}),s}var Tn=class{buffer;mask;top;btm;next;constructor(e){if(!(e>0)||e-1&e)throw new Error("Max size for a FixedFIFO should be a power of two");this.buffer=new Array(e),this.mask=e-1,this.top=0,this.btm=0,this.next=null}push(e){return this.buffer[this.top]!==void 0?!1:(this.buffer[this.top]=e,this.top=this.top+1&this.mask,!0)}shift(){let e=this.buffer[this.btm];if(e!==void 0)return this.buffer[this.btm]=void 0,this.btm=this.btm+1&this.mask,e}isEmpty(){return this.buffer[this.btm]===void 0}},xs=class{size;hwm;head;tail;constructor(e={}){this.hwm=e.splitLimit??16,this.head=new Tn(this.hwm),this.tail=this.head,this.size=0}calculateSize(e){return e?.byteLength!=null?e.byteLength:1}push(e){if(e?.value!=null&&(this.size+=this.calculateSize(e.value)),!this.head.push(e)){let t=this.head;this.head=t.next=new Tn(2*this.head.buffer.length),this.head.push(e)}}shift(){let e=this.tail.shift();if(e===void 0&&this.tail.next!=null){let t=this.tail.next;this.tail.next=null,this.tail=t,e=this.tail.shift()}return e?.value!=null&&(this.size-=this.calculateSize(e.value)),e}isEmpty(){return this.head.isEmpty()}};var fo=class extends Error{type;code;constructor(e,t){super(e??"The operation was aborted"),this.type="aborted",this.code=t??"ABORT_ERR"}};function wt(s={}){return zf(t=>{let r=t.shift();if(r==null)return{done:!0};if(r.error!=null)throw r.error;return{done:r.done===!0,value:r.value}},s)}function zf(s,e){e=e??{};let t=e.onEnd,r=new xs,n,i,o,a=kn(),c=async()=>{try{return r.isEmpty()?o?{done:!0}:await new Promise((p,b)=>{i=y=>{i=null,r.push(y);try{p(s(r))}catch(w){b(w)}return n}}):s(r)}finally{r.isEmpty()&&queueMicrotask(()=>{a.resolve(),a=kn()})}},h=p=>i!=null?i(p):(r.push(p),n),f=p=>(r=new xs,i!=null?i({error:p}):(r.push({error:p}),n)),l=p=>{if(o)return n;if(e?.objectMode!==!0&&p?.byteLength==null)throw new Error("objectMode was not true but tried to push non-Uint8Array value");return h({done:!1,value:p})},g=p=>o?n:(o=!0,p!=null?f(p):h({done:!0})),u=()=>(r=new xs,g(),{done:!0}),d=p=>(g(p),{done:!0});if(n={[Symbol.asyncIterator](){return this},next:c,return:u,throw:d,push:l,end:g,get readableLength(){return r.size},onEmpty:async p=>{let b=p?.signal;if(b?.throwIfAborted(),r.isEmpty())return;let y,w;b!=null&&(y=new Promise((I,x)=>{w=()=>{x(new fo)},b.addEventListener("abort",w)}));try{await Promise.race([a.promise,y])}finally{w!=null&&b!=null&&b?.removeEventListener("abort",w)}}},t==null)return n;let m=n;return n={[Symbol.asyncIterator](){return this},next(){return m.next()},throw(p){return m.throw(p),t!=null&&(t(p),t=void 0),{done:!0}},return(){return m.return(),t!=null&&(t(),t=void 0),{done:!0}},push:l,end(p){return m.end(p),t!=null&&(t(p),t=void 0),n},get readableLength(){return m.readableLength},onEmpty:p=>m.onEmpty(p)},n}function Gf(s){return s[Symbol.asyncIterator]!=null}function qf(...s){let e=[];for(let t of s)Gf(t)||e.push(t);return e.length===s.length?function*(){for(let t of e)yield*t}():async function*(){let t=wt({objectMode:!0});Promise.resolve().then(async()=>{try{await Promise.all(s.map(async r=>{for await(let n of r)t.push(n)})),t.end()}catch(r){t.end(r)}}),yield*t}()}var Wc=qf;function Es(s,...e){if(s==null)throw new Error("Empty pipeline");if(po(s)){let r=s;s=()=>r.source}else if(jc(s)||$c(s)){let r=s;s=()=>r}let t=[s,...e];if(t.length>1&&po(t[t.length-1])&&(t[t.length-1]=t[t.length-1].sink),t.length>2)for(let r=1;r<t.length-1;r++)po(t[r])&&(t[r]=Wf(t[r]));return Kf(...t)}var Kf=(...s)=>{let e;for(;s.length>0;)e=s.shift()(e);return e},$c=s=>s?.[Symbol.asyncIterator]!=null,jc=s=>s?.[Symbol.iterator]!=null,po=s=>s==null?!1:s.sink!=null&&s.source!=null,Wf=s=>e=>{let t=s.sink(e);if(t?.then!=null){let r=wt({objectMode:!0});t.then(()=>{r.end()},o=>{r.end(o)});let n,i=s.source;if($c(i))n=async function*(){yield*i,r.end()};else if(jc(i))n=function*(){yield*i,r.end()};else throw new Error("Unknown duplex source type - must be Iterable or AsyncIterable");return Wc(r,n())}return s.source};var go="/floodsub/1.0.0",mo="/meshsub/1.0.0",Zc="/meshsub/1.1.0",sr="/meshsub/1.2.0";var Jc="ERR_TOPIC_VALIDATOR_REJECT",Xc="ERR_TOPIC_VALIDATOR_IGNORE";var Yc={maxSubscriptions:1/0,maxMessages:1/0,maxIhaveMessageIDs:1/0,maxIwantMessageIDs:1/0,maxIdontwantMessageIDs:1/0,maxControlMessages:1/0,maxPeerInfos:1/0};var yt;(function(s){let e;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{m.lengthDelimited!==!1&&d.fork(),u.subscribe!=null&&(d.uint32(8),d.bool(u.subscribe)),u.topic!=null&&(d.uint32(18),d.string(u.topic)),m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{p.subscribe=u.bool();break}case 2:{p.topic=u.string();break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(e=s.SubOpts||(s.SubOpts={}));let t;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{m.lengthDelimited!==!1&&d.fork(),u.from!=null&&(d.uint32(10),d.bytes(u.from)),u.data!=null&&(d.uint32(18),d.bytes(u.data)),u.seqno!=null&&(d.uint32(26),d.bytes(u.seqno)),u.topic!=null&&u.topic!==""&&(d.uint32(34),d.string(u.topic)),u.signature!=null&&(d.uint32(42),d.bytes(u.signature)),u.key!=null&&(d.uint32(50),d.bytes(u.key)),m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={topic:""},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{p.from=u.bytes();break}case 2:{p.data=u.bytes();break}case 3:{p.seqno=u.bytes();break}case 4:{p.topic=u.string();break}case 5:{p.signature=u.bytes();break}case 6:{p.key=u.bytes();break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(t=s.Message||(s.Message={}));let r;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{if(m.lengthDelimited!==!1&&d.fork(),u.ihave!=null)for(let p of u.ihave)d.uint32(10),s.ControlIHave.codec().encode(p,d);if(u.iwant!=null)for(let p of u.iwant)d.uint32(18),s.ControlIWant.codec().encode(p,d);if(u.graft!=null)for(let p of u.graft)d.uint32(26),s.ControlGraft.codec().encode(p,d);if(u.prune!=null)for(let p of u.prune)d.uint32(34),s.ControlPrune.codec().encode(p,d);if(u.idontwant!=null)for(let p of u.idontwant)d.uint32(42),s.ControlIDontWant.codec().encode(p,d);m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={ihave:[],iwant:[],graft:[],prune:[],idontwant:[]},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{if(m.limits?.ihave!=null&&p.ihave.length===m.limits.ihave)throw new Ne('Decode error - map field "ihave" had too many elements');p.ihave.push(s.ControlIHave.codec().decode(u,u.uint32(),{limits:m.limits?.ihave$}));break}case 2:{if(m.limits?.iwant!=null&&p.iwant.length===m.limits.iwant)throw new Ne('Decode error - map field "iwant" had too many elements');p.iwant.push(s.ControlIWant.codec().decode(u,u.uint32(),{limits:m.limits?.iwant$}));break}case 3:{if(m.limits?.graft!=null&&p.graft.length===m.limits.graft)throw new Ne('Decode error - map field "graft" had too many elements');p.graft.push(s.ControlGraft.codec().decode(u,u.uint32(),{limits:m.limits?.graft$}));break}case 4:{if(m.limits?.prune!=null&&p.prune.length===m.limits.prune)throw new Ne('Decode error - map field "prune" had too many elements');p.prune.push(s.ControlPrune.codec().decode(u,u.uint32(),{limits:m.limits?.prune$}));break}case 5:{if(m.limits?.idontwant!=null&&p.idontwant.length===m.limits.idontwant)throw new Ne('Decode error - map field "idontwant" had too many elements');p.idontwant.push(s.ControlIDontWant.codec().decode(u,u.uint32(),{limits:m.limits?.idontwant$}));break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(r=s.ControlMessage||(s.ControlMessage={}));let n;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{if(m.lengthDelimited!==!1&&d.fork(),u.topicID!=null&&(d.uint32(10),d.string(u.topicID)),u.messageIDs!=null)for(let p of u.messageIDs)d.uint32(18),d.bytes(p);m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={messageIDs:[]},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{p.topicID=u.string();break}case 2:{if(m.limits?.messageIDs!=null&&p.messageIDs.length===m.limits.messageIDs)throw new Ne('Decode error - map field "messageIDs" had too many elements');p.messageIDs.push(u.bytes());break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(n=s.ControlIHave||(s.ControlIHave={}));let i;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{if(m.lengthDelimited!==!1&&d.fork(),u.messageIDs!=null)for(let p of u.messageIDs)d.uint32(10),d.bytes(p);m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={messageIDs:[]},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{if(m.limits?.messageIDs!=null&&p.messageIDs.length===m.limits.messageIDs)throw new Ne('Decode error - map field "messageIDs" had too many elements');p.messageIDs.push(u.bytes());break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(i=s.ControlIWant||(s.ControlIWant={}));let o;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{m.lengthDelimited!==!1&&d.fork(),u.topicID!=null&&(d.uint32(10),d.string(u.topicID)),m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{p.topicID=u.string();break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(o=s.ControlGraft||(s.ControlGraft={}));let a;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{if(m.lengthDelimited!==!1&&d.fork(),u.topicID!=null&&(d.uint32(10),d.string(u.topicID)),u.peers!=null)for(let p of u.peers)d.uint32(18),s.PeerInfo.codec().encode(p,d);u.backoff!=null&&(d.uint32(24),d.uint64Number(u.backoff)),m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={peers:[]},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{p.topicID=u.string();break}case 2:{if(m.limits?.peers!=null&&p.peers.length===m.limits.peers)throw new Ne('Decode error - map field "peers" had too many elements');p.peers.push(s.PeerInfo.codec().decode(u,u.uint32(),{limits:m.limits?.peers$}));break}case 3:{p.backoff=u.uint64Number();break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(a=s.ControlPrune||(s.ControlPrune={}));let c;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{m.lengthDelimited!==!1&&d.fork(),u.peerID!=null&&(d.uint32(10),d.bytes(u.peerID)),u.signedPeerRecord!=null&&(d.uint32(18),d.bytes(u.signedPeerRecord)),m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{p.peerID=u.bytes();break}case 2:{p.signedPeerRecord=u.bytes();break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(c=s.PeerInfo||(s.PeerInfo={}));let h;(function(l){let g;l.codec=()=>(g==null&&(g=Ie((u,d,m={})=>{if(m.lengthDelimited!==!1&&d.fork(),u.messageIDs!=null)for(let p of u.messageIDs)d.uint32(10),d.bytes(p);m.lengthDelimited!==!1&&d.ldelim()},(u,d,m={})=>{let p={messageIDs:[]},b=d==null?u.len:u.pos+d;for(;u.pos<b;){let y=u.uint32();switch(y>>>3){case 1:{if(m.limits?.messageIDs!=null&&p.messageIDs.length===m.limits.messageIDs)throw new Ne('Decode error - map field "messageIDs" had too many elements');p.messageIDs.push(u.bytes());break}default:{u.skipType(y&7);break}}}return p})),g),l.encode=u=>Be(u,l.codec()),l.decode=(u,d)=>Se(u,l.codec(),d)})(h=s.ControlIDontWant||(s.ControlIDontWant={}));let f;s.codec=()=>(f==null&&(f=Ie((l,g,u={})=>{if(u.lengthDelimited!==!1&&g.fork(),l.subscriptions!=null)for(let d of l.subscriptions)g.uint32(10),s.SubOpts.codec().encode(d,g);if(l.messages!=null)for(let d of l.messages)g.uint32(18),s.Message.codec().encode(d,g);l.control!=null&&(g.uint32(26),s.ControlMessage.codec().encode(l.control,g)),u.lengthDelimited!==!1&&g.ldelim()},(l,g,u={})=>{let d={subscriptions:[],messages:[]},m=g==null?l.len:l.pos+g;for(;l.pos<m;){let p=l.uint32();switch(p>>>3){case 1:{if(u.limits?.subscriptions!=null&&d.subscriptions.length===u.limits.subscriptions)throw new Ne('Decode error - map field "subscriptions" had too many elements');d.subscriptions.push(s.SubOpts.codec().decode(l,l.uint32(),{limits:u.limits?.subscriptions$}));break}case 2:{if(u.limits?.messages!=null&&d.messages.length===u.limits.messages)throw new Ne('Decode error - map field "messages" had too many elements');d.messages.push(s.Message.codec().decode(l,l.uint32(),{limits:u.limits?.messages$}));break}case 3:{d.control=s.ControlMessage.codec().decode(l,l.uint32(),{limits:u.limits?.control});break}default:{l.skipType(p&7);break}}}return d})),f),s.encode=l=>Be(l,s.codec()),s.decode=(l,g)=>Se(l,s.codec(),g)})(yt||(yt={}));var Mn=class{gossip;msgs=new Map;msgIdToStrFn;history=[];notValidatedCount=0;constructor(e,t,r){this.gossip=e,this.msgIdToStrFn=r;for(let n=0;n<t;n++)this.history[n]=[]}get size(){return this.msgs.size}put(e,t,r=!1){let{msgIdStr:n}=e;return this.msgs.has(n)?!1:(this.msgs.set(n,{message:t,validated:r,originatingPeers:new Set,iwantCounts:new Map}),this.history[0].push({...e,topic:t.topic}),r||this.notValidatedCount++,!0)}observeDuplicate(e,t){let r=this.msgs.get(e);r!=null&&!r.validated&&r.originatingPeers.add(t)}get(e){return this.msgs.get(this.msgIdToStrFn(e))?.message}getWithIWantCount(e,t){let r=this.msgs.get(e);if(r==null)return null;let n=(r.iwantCounts.get(t)??0)+1;return r.iwantCounts.set(t,n),{msg:r.message,count:n}}getGossipIDs(e){let t=new Map;for(let r=0;r<this.gossip;r++)this.history[r].forEach(n=>{if((this.msgs.get(n.msgIdStr)?.validated??!1)&&e.has(n.topic)){let o=t.get(n.topic);o==null&&(o=[],t.set(n.topic,o)),o.push(n.msgId)}});return t}validate(e){let t=this.msgs.get(e);if(t==null)return null;t.validated||this.notValidatedCount--;let{message:r,originatingPeers:n}=t;return t.validated=!0,t.originatingPeers=new Set,{message:r,originatingPeers:n}}shift(){this.history[this.history.length-1].forEach(t=>{let r=this.msgs.get(t.msgIdStr);r!=null&&(this.msgs.delete(t.msgIdStr),r.validated||this.notValidatedCount--)}),this.history.pop(),this.history.unshift([])}remove(e){let t=this.msgs.get(e);return t==null?null:(this.msgs.delete(e),t)}};var Qc;(function(s){s.StrictSign="StrictSign",s.StrictNoSign="StrictNoSign"})(Qc||(Qc={}));var Lt;(function(s){s[s.Signing=0]="Signing",s[s.Anonymous=1]="Anonymous"})(Lt||(Lt={}));var Ae;(function(s){s.Error="error",s.Ignore="ignore",s.Reject="reject",s.Blacklisted="blacklisted"})(Ae||(Ae={}));var be;(function(s){s.InvalidSignature="invalid_signature",s.InvalidSeqno="invalid_seqno",s.InvalidPeerId="invalid_peerid",s.SignaturePresent="signature_present",s.SeqnoPresent="seqno_present",s.FromPresent="from_present",s.TransformFailed="transform_failed"})(be||(be={}));var we;(function(s){s.duplicate="duplicate",s.invalid="invalid",s.valid="valid"})(we||(we={}));function bo(s){switch(s){case xe.Ignore:return Ae.Ignore;case xe.Reject:return Ae.Reject;default:throw new Error("Unreachable")}}var el;(function(s){s.forward="forward",s.publish="publish"})(el||(el={}));var ke;(function(s){s.Fanout="fanout",s.Random="random",s.Subscribed="subscribed",s.Outbound="outbound",s.NotEnough="not_enough",s.Opportunistic="opportunistic"})(ke||(ke={}));var $e;(function(s){s.Dc="disconnected",s.BadScore="bad_score",s.Prune="prune",s.Excess="excess"})($e||($e={}));var Bs;(function(s){s.GraftBackoff="graft_backoff",s.BrokenPromise="broken_promise",s.MessageDeficit="message_deficit",s.IPColocation="IP_colocation"})(Bs||(Bs={}));var Is;(function(s){s.LowScore="low_score",s.MaxIhave="max_ihave",s.MaxIasked="max_iasked"})(Is||(Is={}));var Ss;(function(s){s.graylist="graylist",s.publish="publish",s.gossip="gossip",s.mesh="mesh"})(Ss||(Ss={}));function tl(s,e,t){return{protocolsEnabled:s.gauge({name:"gossipsub_protocol",help:"Status of enabled protocols",labelNames:["protocol"]}),topicSubscriptionStatus:s.gauge({name:"gossipsub_topic_subscription_status",help:"Status of our subscription to this topic",labelNames:["topicStr"]}),topicPeersCount:s.gauge({name:"gossipsub_topic_peer_count",help:"Number of peers subscribed to each topic",labelNames:["topicStr"]}),meshPeerCounts:s.gauge({name:"gossipsub_mesh_peer_count",help:"Number of peers in our mesh",labelNames:["topicStr"]}),meshPeerInclusionEventsFanout:s.gauge({name:"gossipsub_mesh_peer_inclusion_events_fanout_total",help:"Number of times we include peers in a topic mesh for fanout reasons",labelNames:["topic"]}),meshPeerInclusionEventsRandom:s.gauge({name:"gossipsub_mesh_peer_inclusion_events_random_total",help:"Number of times we include peers in a topic mesh for random reasons",labelNames:["topic"]}),meshPeerInclusionEventsSubscribed:s.gauge({name:"gossipsub_mesh_peer_inclusion_events_subscribed_total",help:"Number of times we include peers in a topic mesh for subscribed reasons",labelNames:["topic"]}),meshPeerInclusionEventsOutbound:s.gauge({name:"gossipsub_mesh_peer_inclusion_events_outbound_total",help:"Number of times we include peers in a topic mesh for outbound reasons",labelNames:["topic"]}),meshPeerInclusionEventsNotEnough:s.gauge({name:"gossipsub_mesh_peer_inclusion_events_not_enough_total",help:"Number of times we include peers in a topic mesh for not_enough reasons",labelNames:["topic"]}),meshPeerInclusionEventsOpportunistic:s.gauge({name:"gossipsub_mesh_peer_inclusion_events_opportunistic_total",help:"Number of times we include peers in a topic mesh for opportunistic reasons",labelNames:["topic"]}),meshPeerInclusionEventsUnknown:s.gauge({name:"gossipsub_mesh_peer_inclusion_events_unknown_total",help:"Number of times we include peers in a topic mesh for unknown reasons",labelNames:["topic"]}),meshPeerChurnEventsDisconnected:s.gauge({name:"gossipsub_peer_churn_events_disconnected_total",help:"Number of times we remove peers in a topic mesh for disconnected reasons",labelNames:["topic"]}),meshPeerChurnEventsBadScore:s.gauge({name:"gossipsub_peer_churn_events_bad_score_total",help:"Number of times we remove peers in a topic mesh for bad_score reasons",labelNames:["topic"]}),meshPeerChurnEventsPrune:s.gauge({name:"gossipsub_peer_churn_events_prune_total",help:"Number of times we remove peers in a topic mesh for prune reasons",labelNames:["topic"]}),meshPeerChurnEventsExcess:s.gauge({name:"gossipsub_peer_churn_events_excess_total",help:"Number of times we remove peers in a topic mesh for excess reasons",labelNames:["topic"]}),meshPeerChurnEventsUnknown:s.gauge({name:"gossipsub_peer_churn_events_unknown_total",help:"Number of times we remove peers in a topic mesh for unknown reasons",labelNames:["topic"]}),peersPerProtocol:s.gauge({name:"gossipsub_peers_per_protocol_count",help:"Peers connected for each topic",labelNames:["protocol"]}),heartbeatDuration:s.histogram({name:"gossipsub_heartbeat_duration_seconds",help:"The time it takes to complete one iteration of the heartbeat",buckets:[.01,.1,1]}),heartbeatSkipped:s.gauge({name:"gossipsub_heartbeat_skipped",help:"Heartbeat run took longer than heartbeat interval so next is skipped"}),acceptedMessagesTotal:s.gauge({name:"gossipsub_accepted_messages_total",help:"Total accepted messages for each topic",labelNames:["topic"]}),ignoredMessagesTotal:s.gauge({name:"gossipsub_ignored_messages_total",help:"Total ignored messages for each topic",labelNames:["topic"]}),rejectedMessagesTotal:s.gauge({name:"gossipsub_rejected_messages_total",help:"Total rejected messages for each topic",labelNames:["topic"]}),unknownValidationResultsTotal:s.gauge({name:"gossipsub_unknown_validation_results_total",help:"Total unknown validation results for each topic",labelNames:["topic"]}),asyncValidationMcacheHit:s.gauge({name:"gossipsub_async_validation_mcache_hit_total",help:"Async validation result reported by the user layer",labelNames:["hit"]}),asyncValidationDelayFromFirstSeenSec:s.histogram({name:"gossipsub_async_validation_delay_from_first_seen",help:"Async validation report delay from first seen in second",buckets:[.01,.03,.1,.3,1,3,10]}),asyncValidationUnknownFirstSeen:s.gauge({name:"gossipsub_async_validation_unknown_first_seen_count_total",help:"Async validation report unknown first seen value for message"}),peerReadStreamError:s.gauge({name:"gossipsub_peer_read_stream_err_count_total",help:"Peer read stream error"}),rpcRecvBytes:s.gauge({name:"gossipsub_rpc_recv_bytes_total",help:"RPC recv"}),rpcRecvCount:s.gauge({name:"gossipsub_rpc_recv_count_total",help:"RPC recv"}),rpcRecvSubscription:s.gauge({name:"gossipsub_rpc_recv_subscription_total",help:"RPC recv"}),rpcRecvMessage:s.gauge({name:"gossipsub_rpc_recv_message_total",help:"RPC recv"}),rpcRecvControl:s.gauge({name:"gossipsub_rpc_recv_control_total",help:"RPC recv"}),rpcRecvIHave:s.gauge({name:"gossipsub_rpc_recv_ihave_total",help:"RPC recv"}),rpcRecvIWant:s.gauge({name:"gossipsub_rpc_recv_iwant_total",help:"RPC recv"}),rpcRecvGraft:s.gauge({name:"gossipsub_rpc_recv_graft_total",help:"RPC recv"}),rpcRecvPrune:s.gauge({name:"gossipsub_rpc_recv_prune_total",help:"RPC recv"}),rpcDataError:s.gauge({name:"gossipsub_rpc_data_err_count_total",help:"RPC data error"}),rpcRecvError:s.gauge({name:"gossipsub_rpc_recv_err_count_total",help:"RPC recv error"}),rpcRecvNotAccepted:s.gauge({name:"gossipsub_rpc_rcv_not_accepted_total",help:"Total count of RPC dropped because acceptFrom() == false"}),rpcSentBytes:s.gauge({name:"gossipsub_rpc_sent_bytes_total",help:"RPC sent"}),rpcSentCount:s.gauge({name:"gossipsub_rpc_sent_count_total",help:"RPC sent"}),rpcSentSubscription:s.gauge({name:"gossipsub_rpc_sent_subscription_total",help:"RPC sent"}),rpcSentMessage:s.gauge({name:"gossipsub_rpc_sent_message_total",help:"RPC sent"}),rpcSentControl:s.gauge({name:"gossipsub_rpc_sent_control_total",help:"RPC sent"}),rpcSentIHave:s.gauge({name:"gossipsub_rpc_sent_ihave_total",help:"RPC sent"}),rpcSentIWant:s.gauge({name:"gossipsub_rpc_sent_iwant_total",help:"RPC sent"}),rpcSentGraft:s.gauge({name:"gossipsub_rpc_sent_graft_total",help:"RPC sent"}),rpcSentPrune:s.gauge({name:"gossipsub_rpc_sent_prune_total",help:"RPC sent"}),rpcSentIDontWant:s.gauge({name:"gossipsub_rpc_sent_idontwant_total",help:"RPC sent"}),msgPublishCount:s.gauge({name:"gossipsub_msg_publish_count_total",help:"Total count of msg published by topic",labelNames:["topic"]}),msgPublishPeersByTopic:s.gauge({name:"gossipsub_msg_publish_peers_total",help:"Total count of peers that we publish a msg to",labelNames:["topic"]}),directPeersPublishedTotal:s.gauge({name:"gossipsub_direct_peers_published_total",help:"Total direct peers that we publish a msg to",labelNames:["topic"]}),floodsubPeersPublishedTotal:s.gauge({name:"gossipsub_floodsub_peers_published_total",help:"Total floodsub peers that we publish a msg to",labelNames:["topic"]}),meshPeersPublishedTotal:s.gauge({name:"gossipsub_mesh_peers_published_total",help:"Total mesh peers that we publish a msg to",labelNames:["topic"]}),fanoutPeersPublishedTotal:s.gauge({name:"gossipsub_fanout_peers_published_total",help:"Total fanout peers that we publish a msg to",labelNames:["topic"]}),msgPublishBytes:s.gauge({name:"gossipsub_msg_publish_bytes_total",help:"Total count of msg publish data.length bytes",labelNames:["topic"]}),msgPublishTime:s.histogram({name:"gossipsub_msg_publish_seconds",help:"Total time in seconds to publish a message",buckets:[.001,.002,.005,.01,.1,.5,1],labelNames:["topic"]}),msgForwardCount:s.gauge({name:"gossipsub_msg_forward_count_total",help:"Total count of msg forwarded by topic",labelNames:["topic"]}),msgForwardPeers:s.gauge({name:"gossipsub_msg_forward_peers_total",help:"Total count of peers that we forward a msg to",labelNames:["topic"]}),msgReceivedPreValidation:s.gauge({name:"gossipsub_msg_received_prevalidation_total",help:"Total count of recv msgs before any validation",labelNames:["topic"]}),msgReceivedError:s.gauge({name:"gossipsub_msg_received_error_total",help:"Total count of recv msgs error",labelNames:["topic"]}),prevalidationInvalidTotal:s.gauge({name:"gossipsub_pre_validation_invalid_total",help:"Total count of invalid messages received",labelNames:["topic"]}),prevalidationValidTotal:s.gauge({name:"gossipsub_pre_validation_valid_total",help:"Total count of valid messages received",labelNames:["topic"]}),prevalidationDuplicateTotal:s.gauge({name:"gossipsub_pre_validation_duplicate_total",help:"Total count of duplicate messages received",labelNames:["topic"]}),prevalidationUnknownTotal:s.gauge({name:"gossipsub_pre_validation_unknown_status_total",help:"Total count of unknown_status messages received",labelNames:["topic"]}),msgReceivedInvalid:s.gauge({name:"gossipsub_msg_received_invalid_total",help:"Tracks specific reason of invalid",labelNames:["error"]}),msgReceivedInvalidByTopic:s.gauge({name:"gossipsub_msg_received_invalid_by_topic_total",help:"Tracks specific invalid message by topic",labelNames:["topic"]}),duplicateMsgDeliveryDelay:s.histogram({name:"gossisub_duplicate_msg_delivery_delay_seconds",help:"Time since the 1st duplicated message validated",labelNames:["topic"],buckets:[.25*t.maxMeshMessageDeliveriesWindowSec,.5*t.maxMeshMessageDeliveriesWindowSec,Number(t.maxMeshMessageDeliveriesWindowSec),2*t.maxMeshMessageDeliveriesWindowSec,4*t.maxMeshMessageDeliveriesWindowSec]}),duplicateMsgLateDelivery:s.gauge({name:"gossisub_duplicate_msg_late_delivery_total",help:"Total count of late duplicate message delivery by topic, which triggers P3 penalty",labelNames:["topic"]}),duplicateMsgIgnored:s.gauge({name:"gossisub_ignored_published_duplicate_msgs_total",help:"Total count of published duplicate message ignored by topic",labelNames:["topic"]}),scoreFnCalls:s.gauge({name:"gossipsub_score_fn_calls_total",help:"Total times score() is called"}),scoreFnRuns:s.gauge({name:"gossipsub_score_fn_runs_total",help:"Total times score() call actually computed computeScore(), no cache"}),scoreCachedDelta:s.histogram({name:"gossipsub_score_cache_delta",help:"Delta of score between cached values that expired",buckets:[10,100,1e3]}),peersByScoreThreshold:s.gauge({name:"gossipsub_peers_by_score_threshold_count",help:"Current count of peers by score threshold",labelNames:["threshold"]}),score:s.avgMinMax({name:"gossipsub_score",help:"Avg min max of gossip scores"}),scoreWeights:s.avgMinMax({name:"gossipsub_score_weights",help:"Separate score weights",labelNames:["topic","p"]}),scorePerMesh:s.avgMinMax({name:"gossipsub_score_per_mesh",help:"Histogram of the scores for each mesh topic",labelNames:["topic"]}),scoringPenalties:s.gauge({name:"gossipsub_scoring_penalties_total",help:"A counter of the kind of penalties being applied to peers",labelNames:["penalty"]}),behaviourPenalty:s.histogram({name:"gossipsub_peer_stat_behaviour_penalty",help:"Current peer stat behaviour_penalty at each scrape",buckets:[.25*t.behaviourPenaltyThreshold,.5*t.behaviourPenaltyThreshold,Number(t.behaviourPenaltyThreshold),2*t.behaviourPenaltyThreshold,4*t.behaviourPenaltyThreshold]}),ihaveRcvIgnored:s.gauge({name:"gossipsub_ihave_rcv_ignored_total",help:"Total received IHAVE messages that we ignore for some reason",labelNames:["reason"]}),ihaveRcvMsgids:s.gauge({name:"gossipsub_ihave_rcv_msgids_total",help:"Total received IHAVE messages by topic",labelNames:["topic"]}),ihaveRcvNotSeenMsgids:s.gauge({name:"gossipsub_ihave_rcv_not_seen_msgids_total",help:"Total messages per topic we do not have, not actual requests",labelNames:["topic"]}),iwantRcvMsgids:s.gauge({name:"gossipsub_iwant_rcv_msgids_total",help:"Total received IWANT messages by topic",labelNames:["topic"]}),iwantRcvDonthaveMsgids:s.gauge({name:"gossipsub_iwant_rcv_dont_have_msgids_total",help:"Total requested messageIDs that we do not have"}),idontwantRcvMsgids:s.gauge({name:"gossipsub_idontwant_rcv_msgids_total",help:"Total received IDONTWANT messages"}),idontwantRcvDonthaveMsgids:s.gauge({name:"gossipsub_idontwant_rcv_dont_have_msgids_total",help:"Total received IDONTWANT messageIDs that we do not have in mcache"}),iwantPromiseStarted:s.gauge({name:"gossipsub_iwant_promise_sent_total",help:"Total count of started IWANT promises"}),iwantPromiseResolved:s.gauge({name:"gossipsub_iwant_promise_resolved_total",help:"Total count of resolved IWANT promises"}),iwantPromiseResolvedFromDuplicate:s.gauge({name:"gossipsub_iwant_promise_resolved_from_duplicate_total",help:"Total count of resolved IWANT promises from duplicate messages"}),iwantPromiseResolvedPeers:s.gauge({name:"gossipsub_iwant_promise_resolved_peers",help:"Total count of peers we have asked IWANT promises that are resolved"}),iwantPromiseBroken:s.gauge({name:"gossipsub_iwant_promise_broken",help:"Total count of broken IWANT promises"}),iwantMessagePruned:s.gauge({name:"gossipsub_iwant_message_pruned",help:"Total count of pruned IWANT messages"}),iwantPromiseDeliveryTime:s.histogram({name:"gossipsub_iwant_promise_delivery_seconds",help:"Histogram of delivery time of resolved IWANT promises",buckets:[.5*t.gossipPromiseExpireSec,Number(t.gossipPromiseExpireSec),2*t.gossipPromiseExpireSec,4*t.gossipPromiseExpireSec]}),iwantPromiseUntracked:s.gauge({name:"gossip_iwant_promise_untracked",help:"Total count of untracked IWANT promise"}),connectedPeersBackoffSec:s.histogram({name:"gossipsub_connected_peers_backoff_seconds",help:"Backoff time in seconds",buckets:[1,2,4,10,20,60,120]}),cacheSize:s.gauge({name:"gossipsub_cache_size",help:"Unbounded cache sizes",labelNames:["cache"]}),mcacheSize:s.gauge({name:"gossipsub_mcache_size",help:"Current mcache msg count"}),mcacheNotValidatedCount:s.gauge({name:"gossipsub_mcache_not_validated_count",help:"Current mcache msg count not validated"}),fastMsgIdCacheCollision:s.gauge({name:"gossipsub_fastmsgid_cache_collision_total",help:"Total count of key collisions on fastmsgid cache put"}),newConnectionCount:s.gauge({name:"gossipsub_new_connection_total",help:"Total new connection by status",labelNames:["status"]}),topicStrToLabel:e,toTopic(r){return this.topicStrToLabel.get(r)??r},onJoin(r){this.topicSubscriptionStatus.set({topicStr:r},1),this.meshPeerCounts.set({topicStr:r},0)},onLeave(r){this.topicSubscriptionStatus.set({topicStr:r},0),this.meshPeerCounts.set({topicStr:r},0)},onAddToMesh(r,n,i){let o=this.toTopic(r);switch(n){case ke.Fanout:this.meshPeerInclusionEventsFanout.inc({topic:o},i);break;case ke.Random:this.meshPeerInclusionEventsRandom.inc({topic:o},i);break;case ke.Subscribed:this.meshPeerInclusionEventsSubscribed.inc({topic:o},i);break;case ke.Outbound:this.meshPeerInclusionEventsOutbound.inc({topic:o},i);break;case ke.NotEnough:this.meshPeerInclusionEventsNotEnough.inc({topic:o},i);break;case ke.Opportunistic:this.meshPeerInclusionEventsOpportunistic.inc({topic:o},i);break;default:this.meshPeerInclusionEventsUnknown.inc({topic:o},i);break}},onRemoveFromMesh(r,n,i){let o=this.toTopic(r);switch(n){case $e.Dc:this.meshPeerChurnEventsDisconnected.inc({topic:o},i);break;case $e.BadScore:this.meshPeerChurnEventsBadScore.inc({topic:o},i);break;case $e.Prune:this.meshPeerChurnEventsPrune.inc({topic:o},i);break;case $e.Excess:this.meshPeerChurnEventsExcess.inc({topic:o},i);break;default:this.meshPeerChurnEventsUnknown.inc({topic:o},i);break}},onReportValidation(r,n,i){if(this.asyncValidationMcacheHit.inc({hit:r!=null?"hit":"miss"}),r!=null){let o=this.toTopic(r.message.topic);switch(n){case xe.Accept:this.acceptedMessagesTotal.inc({topic:o});break;case xe.Ignore:this.ignoredMessagesTotal.inc({topic:o});break;case xe.Reject:this.rejectedMessagesTotal.inc({topic:o});break;default:this.unknownValidationResultsTotal.inc({topic:o});break}}i!=null?this.asyncValidationDelayFromFirstSeenSec.observe((Date.now()-i)/1e3):this.asyncValidationUnknownFirstSeen.inc()},onScorePenalty(r){this.scoringPenalties.inc({penalty:r},1)},onIhaveRcv(r,n,i){let o=this.toTopic(r);this.ihaveRcvMsgids.inc({topic:o},n),this.ihaveRcvNotSeenMsgids.inc({topic:o},i)},onIwantRcv(r,n){for(let[i,o]of r){let a=this.toTopic(i);this.iwantRcvMsgids.inc({topic:a},o)}this.iwantRcvDonthaveMsgids.inc(n)},onIdontwantRcv(r,n){this.idontwantRcvMsgids.inc(r),this.idontwantRcvDonthaveMsgids.inc(n)},onForwardMsg(r,n){let i=this.toTopic(r);this.msgForwardCount.inc({topic:i},1),this.msgForwardPeers.inc({topic:i},n)},onPublishMsg(r,n,i,o,a){let c=this.toTopic(r);this.msgPublishCount.inc({topic:c},1),this.msgPublishBytes.inc({topic:c},i*o),this.msgPublishPeersByTopic.inc({topic:c},i),this.directPeersPublishedTotal.inc({topic:c},n.direct),this.floodsubPeersPublishedTotal.inc({topic:c},n.floodsub),this.meshPeersPublishedTotal.inc({topic:c},n.mesh),this.fanoutPeersPublishedTotal.inc({topic:c},n.fanout),this.msgPublishTime.observe({topic:c},a/1e3)},onMsgRecvPreValidation(r){let n=this.toTopic(r);this.msgReceivedPreValidation.inc({topic:n},1)},onMsgRecvError(r){let n=this.toTopic(r);this.msgReceivedError.inc({topic:n},1)},onPrevalidationResult(r,n){let i=this.toTopic(r);switch(n){case we.duplicate:this.prevalidationDuplicateTotal.inc({topic:i});break;case we.invalid:this.prevalidationInvalidTotal.inc({topic:i});break;case we.valid:this.prevalidationValidTotal.inc({topic:i});break;default:this.prevalidationUnknownTotal.inc({topic:i});break}},onMsgRecvInvalid(r,n){let i=this.toTopic(r),o=n.reason===Ae.Error?n.error:n.reason;this.msgReceivedInvalid.inc({error:o},1),this.msgReceivedInvalidByTopic.inc({topic:i},1)},onDuplicateMsgDelivery(r,n,i){let o=this.toTopic(r);this.duplicateMsgDeliveryDelay.observe({topic:o},n/1e3),i&&this.duplicateMsgLateDelivery.inc({topic:o},1)},onPublishDuplicateMsg(r){let n=this.toTopic(r);this.duplicateMsgIgnored.inc({topic:n},1)},onPeerReadStreamError(){this.peerReadStreamError.inc(1)},onRpcRecvError(){this.rpcRecvError.inc(1)},onRpcDataError(){this.rpcDataError.inc(1)},onRpcRecv(r,n){this.rpcRecvBytes.inc(n),this.rpcRecvCount.inc(1),r.subscriptions!=null&&this.rpcRecvSubscription.inc(r.subscriptions.length),r.messages!=null&&this.rpcRecvMessage.inc(r.messages.length),r.control!=null&&(this.rpcRecvControl.inc(1),r.control.ihave!=null&&this.rpcRecvIHave.inc(r.control.ihave.length),r.control.iwant!=null&&this.rpcRecvIWant.inc(r.control.iwant.length),r.control.graft!=null&&this.rpcRecvGraft.inc(r.control.graft.length),r.control.prune!=null&&this.rpcRecvPrune.inc(r.control.prune.length))},onRpcSent(r,n){if(this.rpcSentBytes.inc(n),this.rpcSentCount.inc(1),r.subscriptions!=null&&this.rpcSentSubscription.inc(r.subscriptions.length),r.messages!=null&&this.rpcSentMessage.inc(r.messages.length),r.control!=null){let i=r.control.ihave?.length??0,o=r.control.iwant?.length??0,a=r.control.graft?.length??0,c=r.control.prune?.length??0,h=r.control.idontwant?.length??0;i>0&&this.rpcSentIHave.inc(i),o>0&&this.rpcSentIWant.inc(o),a>0&&this.rpcSentGraft.inc(a),c>0&&this.rpcSentPrune.inc(c),h>0&&this.rpcSentIDontWant.inc(h),(i>0||o>0||a>0||c>0||h>0)&&this.rpcSentControl.inc(1)}},registerScores(r,n){let i=0,o=0,a=0,c=0;for(let h of r)h>=n.graylistThreshold&&i++,h>=n.publishThreshold&&o++,h>=n.gossipThreshold&&a++,h>=0&&c++;this.peersByScoreThreshold.set({threshold:Ss.graylist},i),this.peersByScoreThreshold.set({threshold:Ss.publish},o),this.peersByScoreThreshold.set({threshold:Ss.gossip},a),this.peersByScoreThreshold.set({threshold:Ss.mesh},c),this.score.set(r)},registerScoreWeights(r){for(let[n,i]of r.byTopic)this.scoreWeights.set({topic:n,p:"p1"},i.p1w),this.scoreWeights.set({topic:n,p:"p2"},i.p2w),this.scoreWeights.set({topic:n,p:"p3"},i.p3w),this.scoreWeights.set({topic:n,p:"p3b"},i.p3bw),this.scoreWeights.set({topic:n,p:"p4"},i.p4w);this.scoreWeights.set({p:"p5"},r.p5w),this.scoreWeights.set({p:"p6"},r.p6w),this.scoreWeights.set({p:"p7"},r.p7w)},registerScorePerMesh(r,n){let i=new Map;r.forEach((o,a)=>{let c=this.topicStrToLabel.get(a)??"unknown",h=i.get(c);h==null&&(h=new Set,i.set(c,h)),o.forEach(f=>h?.add(f))});for(let[o,a]of i){let c=[];a.forEach(h=>{c.push(n.get(h)??0)}),this.scorePerMesh.set({topic:o},c)}}}}var q=class extends Error{static name="InvalidPeerScoreParamsError";constructor(e="Invalid peer score params"){super(e),this.name="InvalidPeerScoreParamsError"}};var jf={topics:{},topicScoreCap:10,appSpecificScore:()=>0,appSpecificWeight:10,IPColocationFactorWeight:-5,IPColocationFactorThreshold:10,IPColocationFactorWhitelist:new Set,behaviourPenaltyWeight:-10,behaviourPenaltyThreshold:0,behaviourPenaltyDecay:.2,decayInterval:1e3,decayToZero:.1,retainScore:3600*1e3},Zf={topicWeight:.5,timeInMeshWeight:1,timeInMeshQuantum:1,timeInMeshCap:3600,firstMessageDeliveriesWeight:1,firstMessageDeliveriesDecay:.5,firstMessageDeliveriesCap:2e3,meshMessageDeliveriesWeight:-1,meshMessageDeliveriesDecay:.5,meshMessageDeliveriesCap:100,meshMessageDeliveriesThreshold:20,meshMessageDeliveriesWindow:10,meshMessageDeliveriesActivation:5e3,meshFailurePenaltyWeight:-1,meshFailurePenaltyDecay:.5,invalidMessageDeliveriesWeight:-1,invalidMessageDeliveriesDecay:.3};function sl(s={}){return{...jf,...s,topics:s.topics!=null?Object.entries(s.topics).reduce((e,[t,r])=>(e[t]=Jf(r),e),{}):{}}}function Jf(s={}){return{...Zf,...s}}function rl(s){for(let[e,t]of Object.entries(s.topics))try{Xf(t)}catch(r){throw new q(`invalid score parameters for topic ${e}: ${r.message}`)}if(s.topicScoreCap<0)throw new q("invalid topic score cap; must be positive (or 0 for no cap)");if(s.appSpecificScore===null||s.appSpecificScore===void 0)throw new q("missing application specific score function");if(s.IPColocationFactorWeight>0)throw new q("invalid IPColocationFactorWeight; must be negative (or 0 to disable)");if(s.IPColocationFactorWeight!==0&&s.IPColocationFactorThreshold<1)throw new q("invalid IPColocationFactorThreshold; must be at least 1");if(s.behaviourPenaltyWeight>0)throw new q("invalid BehaviourPenaltyWeight; must be negative (or 0 to disable)");if(s.behaviourPenaltyWeight!==0&&(s.behaviourPenaltyDecay<=0||s.behaviourPenaltyDecay>=1))throw new q("invalid BehaviourPenaltyDecay; must be between 0 and 1");if(s.decayInterval<1e3)throw new q("invalid DecayInterval; must be at least 1s");if(s.decayToZero<=0||s.decayToZero>=1)throw new q("invalid DecayToZero; must be between 0 and 1")}function Xf(s){if(s.topicWeight<0)throw new q("invalid topic weight; must be >= 0");if(s.timeInMeshQuantum===0)throw new q("invalid TimeInMeshQuantum; must be non zero");if(s.timeInMeshWeight<0)throw new q("invalid TimeInMeshWeight; must be positive (or 0 to disable)");if(s.timeInMeshWeight!==0&&s.timeInMeshQuantum<=0)throw new q("invalid TimeInMeshQuantum; must be positive");if(s.timeInMeshWeight!==0&&s.timeInMeshCap<=0)throw new q("invalid TimeInMeshCap; must be positive");if(s.firstMessageDeliveriesWeight<0)throw new q("invallid FirstMessageDeliveriesWeight; must be positive (or 0 to disable)");if(s.firstMessageDeliveriesWeight!==0&&(s.firstMessageDeliveriesDecay<=0||s.firstMessageDeliveriesDecay>=1))throw new q("invalid FirstMessageDeliveriesDecay; must be between 0 and 1");if(s.firstMessageDeliveriesWeight!==0&&s.firstMessageDeliveriesCap<=0)throw new q("invalid FirstMessageDeliveriesCap; must be positive");if(s.meshMessageDeliveriesWeight>0)throw new q("invalid MeshMessageDeliveriesWeight; must be negative (or 0 to disable)");if(s.meshMessageDeliveriesWeight!==0&&(s.meshMessageDeliveriesDecay<=0||s.meshMessageDeliveriesDecay>=1))throw new q("invalid MeshMessageDeliveriesDecay; must be between 0 and 1");if(s.meshMessageDeliveriesWeight!==0&&s.meshMessageDeliveriesCap<=0)throw new q("invalid MeshMessageDeliveriesCap; must be positive");if(s.meshMessageDeliveriesWeight!==0&&s.meshMessageDeliveriesThreshold<=0)throw new q("invalid MeshMessageDeliveriesThreshold; must be positive");if(s.meshMessageDeliveriesWindow<0)throw new q("invalid MeshMessageDeliveriesWindow; must be non-negative");if(s.meshMessageDeliveriesWeight!==0&&s.meshMessageDeliveriesActivation<1e3)throw new q("invalid MeshMessageDeliveriesActivation; must be at least 1s");if(s.meshFailurePenaltyWeight>0)throw new q("invalid MeshFailurePenaltyWeight; must be negative (or 0 to disable)");if(s.meshFailurePenaltyWeight!==0&&(s.meshFailurePenaltyDecay<=0||s.meshFailurePenaltyDecay>=1))throw new q("invalid MeshFailurePenaltyDecay; must be between 0 and 1");if(s.invalidMessageDeliveriesWeight>0)throw new q("invalid InvalidMessageDeliveriesWeight; must be negative (or 0 to disable)");if(s.invalidMessageDeliveriesDecay<=0||s.invalidMessageDeliveriesDecay>=1)throw new q("invalid InvalidMessageDeliveriesDecay; must be between 0 and 1")}var Yf={gossipThreshold:-10,publishThreshold:-50,graylistThreshold:-80,acceptPXThreshold:10,opportunisticGraftThreshold:20};function nl(s={}){return{...Yf,...s}}function Dn(s,e,t=()=>!0){let r=new Set;if(e<=0)return r;for(let n of s){if(r.size>=e)break;t(n)&&(r.add(n),s.delete(n))}return r}function il(s,e){return Dn(s,e,()=>!0)}var Pn=class extends Map{getDefault;constructor(e){super(),this.getDefault=e}getOrDefault(e){let t=super.get(e);return t===void 0&&(t=this.getDefault(),this.set(e,t)),t}};function ol(s,e,t,r){let n=0;Object.entries(e.topics).forEach(([o,a])=>{let c=t.topics[o];if(c===void 0)return;let h=0;if(a.inMesh){let u=a.meshTime/c.timeInMeshQuantum;u>c.timeInMeshCap&&(u=c.timeInMeshCap),h+=u*c.timeInMeshWeight}let f=a.firstMessageDeliveries;if(f>c.firstMessageDeliveriesCap&&(f=c.firstMessageDeliveriesCap),h+=f*c.firstMessageDeliveriesWeight,a.meshMessageDeliveriesActive&&a.meshMessageDeliveries<c.meshMessageDeliveriesThreshold){let u=c.meshMessageDeliveriesThreshold-a.meshMessageDeliveries,d=u*u;h+=d*c.meshMessageDeliveriesWeight}let l=a.meshFailurePenalty;h+=l*c.meshFailurePenaltyWeight;let g=a.invalidMessageDeliveries*a.invalidMessageDeliveries;h+=g*c.invalidMessageDeliveriesWeight,n+=h*c.topicWeight}),t.topicScoreCap>0&&n>t.topicScoreCap&&(n=t.topicScoreCap);let i=t.appSpecificScore(s);if(n+=i*t.appSpecificWeight,e.knownIPs.forEach(o=>{if(t.IPColocationFactorWhitelist.has(o))return;let a=r.get(o),c=a!=null?a.size:0;if(c>t.IPColocationFactorThreshold){let h=c-t.IPColocationFactorThreshold,f=h*h;n+=f*t.IPColocationFactorWeight}}),e.behaviourPenalty>t.behaviourPenaltyThreshold){let o=e.behaviourPenalty-t.behaviourPenaltyThreshold,a=o*o;n+=a*t.behaviourPenaltyWeight}return n}var ll=Co(cl(),1);var Te;(function(s){s[s.unknown=0]="unknown",s[s.valid=1]="valid",s[s.invalid=2]="invalid",s[s.ignored=3]="ignored"})(Te||(Te={}));var Cn=class{records;queue;constructor(){this.records=new Map,this.queue=new ll.default}getRecord(e){return this.records.get(e)}ensureRecord(e){let t=this.records.get(e);if(t!=null)return t;t={status:Te.unknown,firstSeenTsMs:Date.now(),validated:0,peers:new Set},this.records.set(e,t);let r={msgId:e,expire:Date.now()+12e4};return this.queue.push(r),t}gc(){let e=Date.now(),t=this.queue.peekFront();for(;t!=null&&t.expire<e;)this.records.delete(t.msgId),this.queue.shift(),t=this.queue.peekFront()}clear(){this.records.clear(),this.queue.clear()}};var Nn=class{params;metrics;peerStats=new Map;peerIPs=new Pn(()=>new Set);scoreCache=new Map;deliveryRecords=new Cn;_backgroundInterval;scoreCacheValidityMs;computeScore;log;constructor(e,t,r,n){this.params=e,this.metrics=t,rl(e),this.scoreCacheValidityMs=n.scoreCacheValidityMs,this.computeScore=n.computeScore??ol,this.log=r.forComponent("libp2p:gossipsub:score")}get size(){return this.peerStats.size}start(){if(this._backgroundInterval!=null){this.log("Peer score already running");return}this._backgroundInterval=setInterval(()=>{this.background()},this.params.decayInterval),this.log("started")}stop(){if(this._backgroundInterval==null){this.log("Peer score already stopped");return}clearInterval(this._backgroundInterval),delete this._backgroundInterval,this.peerIPs.clear(),this.peerStats.clear(),this.deliveryRecords.clear(),this.log("stopped")}background(){this.refreshScores(),this.deliveryRecords.gc()}dumpPeerScoreStats(){return Object.fromEntries(Array.from(this.peerStats.entries()).map(([e,t])=>[e,t]))}messageFirstSeenTimestampMs(e){let t=this.deliveryRecords.getRecord(e);return t!=null?t.firstSeenTsMs:null}refreshScores(){let e=Date.now(),t=this.params.decayToZero;this.peerStats.forEach((r,n)=>{if(!r.connected){e>r.expire&&(this.removeIPsForPeer(n,r.knownIPs),this.peerStats.delete(n),this.scoreCache.delete(n));return}Object.entries(r.topics).forEach(([i,o])=>{let a=this.params.topics[i];a!==void 0&&(o.firstMessageDeliveries*=a.firstMessageDeliveriesDecay,o.firstMessageDeliveries<t&&(o.firstMessageDeliveries=0),o.meshMessageDeliveries*=a.meshMessageDeliveriesDecay,o.meshMessageDeliveries<t&&(o.meshMessageDeliveries=0),o.meshFailurePenalty*=a.meshFailurePenaltyDecay,o.meshFailurePenalty<t&&(o.meshFailurePenalty=0),o.invalidMessageDeliveries*=a.invalidMessageDeliveriesDecay,o.invalidMessageDeliveries<t&&(o.invalidMessageDeliveries=0),o.inMesh&&(o.meshTime=e-o.graftTime,o.meshTime>a.meshMessageDeliveriesActivation&&(o.meshMessageDeliveriesActive=!0)))}),r.behaviourPenalty*=this.params.behaviourPenaltyDecay,r.behaviourPenalty<t&&(r.behaviourPenalty=0)})}score(e){this.metrics?.scoreFnCalls.inc();let t=this.peerStats.get(e);if(t==null)return 0;let r=Date.now(),n=this.scoreCache.get(e);if(n!=null&&n.cacheUntil>r)return n.score;this.metrics?.scoreFnRuns.inc();let i=this.computeScore(e,t,this.params,this.peerIPs),o=r+this.scoreCacheValidityMs;return n!=null?(this.metrics?.scoreCachedDelta.observe(Math.abs(i-n.score)),n.score=i,n.cacheUntil=o):this.scoreCache.set(e,{score:i,cacheUntil:o}),i}addPenalty(e,t,r){let n=this.peerStats.get(e);n!=null&&(n.behaviourPenalty+=t,this.metrics?.onScorePenalty(r))}addPeer(e){let t={connected:!0,expire:0,topics:{},knownIPs:new Set,behaviourPenalty:0};this.peerStats.set(e,t)}addIP(e,t){let r=this.peerStats.get(e);r?.knownIPs.add(t),this.peerIPs.getOrDefault(t).add(e)}removeIP(e,t){let r=this.peerStats.get(e);r?.knownIPs.delete(t);let n=this.peerIPs.get(t);n!=null&&(n.delete(e),n.size===0&&this.peerIPs.delete(t))}removePeer(e){let t=this.peerStats.get(e);if(t!=null){if(this.score(e)>0){this.removeIPsForPeer(e,t.knownIPs),this.peerStats.delete(e);return}Object.entries(t.topics).forEach(([r,n])=>{n.firstMessageDeliveries=0;let i=this.params.topics[r].meshMessageDeliveriesThreshold;if(n.inMesh&&n.meshMessageDeliveriesActive&&n.meshMessageDeliveries<i){let o=i-n.meshMessageDeliveries;n.meshFailurePenalty+=o*o}n.inMesh=!1,n.meshMessageDeliveriesActive=!1}),t.connected=!1,t.expire=Date.now()+this.params.retainScore}}graft(e,t){let r=this.peerStats.get(e);if(r!=null){let n=this.getPtopicStats(r,t);n!=null&&(n.inMesh=!0,n.graftTime=Date.now(),n.meshTime=0,n.meshMessageDeliveriesActive=!1)}}prune(e,t){let r=this.peerStats.get(e);if(r!=null){let n=this.getPtopicStats(r,t);if(n!=null){let i=this.params.topics[t].meshMessageDeliveriesThreshold;if(n.meshMessageDeliveriesActive&&n.meshMessageDeliveries<i){let o=i-n.meshMessageDeliveries;n.meshFailurePenalty+=o*o}n.meshMessageDeliveriesActive=!1,n.inMesh=!1}}}validateMessage(e){this.deliveryRecords.ensureRecord(e)}deliverMessage(e,t,r){this.markFirstMessageDelivery(e,r);let n=this.deliveryRecords.ensureRecord(t),i=Date.now();if(n.status!==Te.unknown){this.log("unexpected delivery: message from %s was first seen %s ago and has delivery status %s",e,i-n.firstSeenTsMs,Te[n.status]);return}n.status=Te.valid,n.validated=i,n.peers.forEach(o=>{o!==e.toString()&&this.markDuplicateMessageDelivery(o,r)})}rejectInvalidMessage(e,t){this.markInvalidMessageDelivery(e,t)}rejectMessage(e,t,r,n){switch(n){case Ae.Error:this.markInvalidMessageDelivery(e,r);return;case Ae.Blacklisted:return}let i=this.deliveryRecords.ensureRecord(t);if(i.status!==Te.unknown){this.log("unexpected rejection: message from %s was first seen %s ago and has delivery status %d",e,Date.now()-i.firstSeenTsMs,Te[i.status]);return}if(n===Ae.Ignore){i.status=Te.ignored,i.peers.clear();return}i.status=Te.invalid,this.markInvalidMessageDelivery(e,r),i.peers.forEach(o=>{this.markInvalidMessageDelivery(o,r)}),i.peers.clear()}duplicateMessage(e,t,r){let n=this.deliveryRecords.ensureRecord(t);if(!n.peers.has(e))switch(n.status){case Te.unknown:n.peers.add(e);break;case Te.valid:n.peers.add(e),this.markDuplicateMessageDelivery(e,r,n.validated);break;case Te.invalid:this.markInvalidMessageDelivery(e,r);break;case Te.ignored:break}}markInvalidMessageDelivery(e,t){let r=this.peerStats.get(e);if(r!=null){let n=this.getPtopicStats(r,t);n!=null&&(n.invalidMessageDeliveries+=1)}}markFirstMessageDelivery(e,t){let r=this.peerStats.get(e);if(r!=null){let n=this.getPtopicStats(r,t);if(n!=null){let i=this.params.topics[t].firstMessageDeliveriesCap;n.firstMessageDeliveries=Math.min(i,n.firstMessageDeliveries+1),n.inMesh&&(i=this.params.topics[t].meshMessageDeliveriesCap,n.meshMessageDeliveries=Math.min(i,n.meshMessageDeliveries+1))}}}markDuplicateMessageDelivery(e,t,r){let n=this.peerStats.get(e);if(n!=null){let i=r!==void 0?Date.now():0,o=this.getPtopicStats(n,t);if(o!=null&&o.inMesh){let a=this.params.topics[t];if(r!==void 0){let h=i-r,f=h>a.meshMessageDeliveriesWindow;if(this.metrics?.onDuplicateMsgDelivery(t,h,f),f)return}let c=a.meshMessageDeliveriesCap;o.meshMessageDeliveries=Math.min(c,o.meshMessageDeliveries+1)}}}removeIPsForPeer(e,t){for(let r of t){let n=this.peerIPs.get(r);n!=null&&(n.delete(e),n.size===0&&this.peerIPs.delete(r))}}getPtopicStats(e,t){let r=e.topics[t];return r!==void 0?r:this.params.topics[t]!==void 0?(r={inMesh:!1,graftTime:0,meshTime:0,firstMessageDeliveries:0,meshMessageDeliveries:0,meshMessageDeliveriesActive:!1,meshFailurePenalty:0,invalidMessageDeliveries:0},e.topics[t]=r,r):null}};function Qf(s,e,t,r,n){let i=0,o=new Map;if(Object.entries(e.topics).forEach(([g,u])=>{let d=n.get(g)??"unknown",m=t.topics[g];if(m===void 0)return;let p=o.get(d);p==null&&(p={p1w:0,p2w:0,p3w:0,p3bw:0,p4w:0},o.set(d,p));let b=0,y=0,w=0,I=0,x=0;if(u.inMesh){let k=Math.max(u.meshTime/m.timeInMeshQuantum,m.timeInMeshCap);b+=k*m.timeInMeshWeight}let B=u.firstMessageDeliveries;if(B>m.firstMessageDeliveriesCap&&(B=m.firstMessageDeliveriesCap),y+=B*m.firstMessageDeliveriesWeight,u.meshMessageDeliveriesActive&&u.meshMessageDeliveries<m.meshMessageDeliveriesThreshold){let k=m.meshMessageDeliveriesThreshold-u.meshMessageDeliveries,z=k*k;w+=z*m.meshMessageDeliveriesWeight}let E=u.meshFailurePenalty;I+=E*m.meshFailurePenaltyWeight;let _=u.invalidMessageDeliveries*u.invalidMessageDeliveries;x+=_*m.invalidMessageDeliveriesWeight,i+=(b+y+w+I+x)*m.topicWeight,p.p1w+=b,p.p2w+=y,p.p3w+=w,p.p3bw+=I,p.p4w+=x}),t.topicScoreCap>0&&i>t.topicScoreCap){i=t.topicScoreCap;let g=t.topicScoreCap/i;for(let u of o.values())u.p1w*=g,u.p2w*=g,u.p3w*=g,u.p3bw*=g,u.p4w*=g}let a=0,c=0,h=0,f=t.appSpecificScore(s);a+=f*t.appSpecificWeight,e.knownIPs.forEach(g=>{if(t.IPColocationFactorWhitelist.has(g))return;let u=r.get(g),d=u!=null?u.size:0;if(d>t.IPColocationFactorThreshold){let m=d-t.IPColocationFactorThreshold,p=m*m;c+=p*t.IPColocationFactorWeight}});let l=e.behaviourPenalty*e.behaviourPenalty;return h+=l*t.behaviourPenaltyWeight,i+=a+c+h,{byTopic:o,p5w:a,p6w:c,p7w:h,score:i}}function ul(s,e,t,r,n){let i={byTopic:new Map,p5w:[],p6w:[],p7w:[],score:[]};for(let o of s){let a=e.get(o);if(a!=null){let c=Qf(o,a,t,r,n);for(let[h,f]of c.byTopic){let l=i.byTopic.get(h);l==null&&(l={p1w:[],p2w:[],p3w:[],p3bw:[],p4w:[]},i.byTopic.set(h,l)),l.p1w.push(f.p1w),l.p2w.push(f.p2w),l.p3w.push(f.p3w),l.p3bw.push(f.p3bw),l.p4w.push(f.p4w)}i.p5w.push(c.p5w),i.p6w.push(c.p6w),i.p7w.push(c.p7w),i.score.push(c.score)}else i.p5w.push(0),i.p6w.push(0),i.p7w.push(0),i.score.push(0)}return i}var Rn=class{rawStream;pushable;closeController;maxBufferSize;constructor(e,t,r){this.rawStream=e,this.pushable=wt(),this.closeController=new AbortController,this.maxBufferSize=r.maxBufferSize??1/0,this.closeController.signal.addEventListener("abort",()=>{e.close().catch(n=>{e.abort(n)})}),Es(this.pushable,this.rawStream).catch(t)}get protocol(){return this.rawStream.protocol}push(e){if(this.pushable.readableLength>this.maxBufferSize)throw Error(`OutboundStream buffer full, size > ${this.maxBufferSize}`);this.pushable.push(vs.single(e))}pushPrefixed(e){if(this.pushable.readableLength>this.maxBufferSize)throw Error(`OutboundStream buffer full, size > ${this.maxBufferSize}`);this.pushable.push(e)}async close(){this.closeController.abort(),await this.pushable.return()}},Ln=class{source;rawStream;closeController;constructor(e,t={}){this.rawStream=e,this.closeController=new AbortController,this.closeController.signal.addEventListener("abort",()=>{e.close().catch(r=>{e.abort(r)})}),this.source=Es(this.rawStream,r=>tr(r,t))}async close(){this.closeController.abort()}};var Un=class{gossipsubIWantFollowupMs;msgIdToStrFn;metrics;promises=new Map;requestMsByMsg=new Map;requestMsByMsgExpire;constructor(e,t,r){this.gossipsubIWantFollowupMs=e,this.msgIdToStrFn=t,this.metrics=r,this.requestMsByMsgExpire=10*e}get size(){return this.promises.size}get requestMsByMsgSize(){return this.requestMsByMsg.size}addPromise(e,t){let r=Math.floor(Math.random()*t.length),n=t[r],i=this.msgIdToStrFn(n),o=this.promises.get(i);o==null&&(o=new Map,this.promises.set(i,o));let a=Date.now();o.has(e)||(o.set(e,a+this.gossipsubIWantFollowupMs),this.metrics!=null&&(this.metrics.iwantPromiseStarted.inc(1),this.requestMsByMsg.has(i)||this.requestMsByMsg.set(i,a)))}getBrokenPromises(){let e=Date.now(),t=new Map,r=0;return this.promises.forEach((n,i)=>{n.forEach((o,a)=>{o<e&&(t.set(a,(t.get(a)??0)+1),n.delete(a),r++)}),n.size===0&&this.promises.delete(i)}),this.metrics?.iwantPromiseBroken.inc(r),t}deliverMessage(e,t=!1){this.trackMessage(e);let r=this.promises.get(e);r!=null&&(this.promises.delete(e),this.metrics!=null&&(this.metrics.iwantPromiseResolved.inc(1),t&&this.metrics.iwantPromiseResolvedFromDuplicate.inc(1),this.metrics.iwantPromiseResolvedPeers.inc(r.size)))}rejectMessage(e,t){switch(this.trackMessage(e),t){case Ae.Error:return;default:break}this.promises.delete(e)}clear(){this.promises.clear()}prune(){let e=Date.now()-this.requestMsByMsgExpire,t=0;for(let[r,n]of this.requestMsByMsg.entries())if(n<e)this.requestMsByMsg.delete(r),t++;else break;this.metrics?.iwantMessagePruned.inc(t)}trackMessage(e){if(this.metrics!=null){let t=this.requestMsByMsg.get(e);t!==void 0&&(this.metrics.iwantPromiseDeliveryTime.observe((Date.now()-t)/1e3),this.requestMsByMsg.delete(e))}}};var hl=X("libp2p-pubsub:");async function fl(s,e,t,r){switch(s.type){case Lt.Signing:{let n={from:s.author.toMultihash().bytes,data:r,seqno:dn(8),topic:e,signature:void 0,key:void 0},i=Rt([hl,yt.Message.encode(n)]);n.signature=await s.privateKey.sign(i),n.key=s.key;let o={type:"signed",from:s.author,data:t,sequenceNumber:BigInt(`0x${$(n.seqno??new Uint8Array(0),"base16")}`),topic:e,signature:n.signature,key:Xs(n.key)};return{raw:n,msg:o}}case Lt.Anonymous:return{raw:{from:void 0,data:r,seqno:void 0,topic:e,signature:void 0,key:void 0},msg:{type:"unsigned",data:t,topic:e}};default:throw new Error("Unreachable")}}async function dl(s,e){switch(s){case Xt:return e.signature!=null?{valid:!1,error:be.SignaturePresent}:e.seqno!=null?{valid:!1,error:be.SeqnoPresent}:e.key!=null?{valid:!1,error:be.FromPresent}:{valid:!0,message:{type:"unsigned",topic:e.topic,data:e.data??new Uint8Array(0)}};case Ut:{if(e.seqno==null)return{valid:!1,error:be.InvalidSeqno};if(e.seqno.length!==8)return{valid:!1,error:be.InvalidSeqno};if(e.signature==null)return{valid:!1,error:be.InvalidSignature};if(e.from==null)return{valid:!1,error:be.InvalidPeerId};let t;try{t=Qs(Oe(e.from))}catch{return{valid:!1,error:be.InvalidPeerId}}let r;if(e.key!=null){if(r=Xs(e.key),t.publicKey!==void 0&&!r.equals(t.publicKey))return{valid:!1,error:be.InvalidPeerId}}else{if(t.publicKey==null)return{valid:!1,error:be.InvalidPeerId};r=t.publicKey}let n={from:e.from,data:e.data,seqno:e.seqno,topic:e.topic,signature:void 0,key:void 0},i=Rt([hl,yt.Message.encode(n)]);return await r.verify(i,e.signature)?{valid:!0,message:{type:"signed",from:t,data:e.data??new Uint8Array(0),sequenceNumber:BigInt(`0x${$(e.seqno,"base16")}`),topic:e.topic,signature:e.signature,key:e.key!=null?Xs(e.key):r}}:{valid:!1,error:be.InvalidSignature}}default:throw new Error("Unreachable")}}function je(s=[],e){return{subscriptions:[],messages:s,control:e!==void 0?{graft:e.graft??[],prune:e.prune??[],ihave:e.ihave??[],iwant:e.iwant??[],idontwant:e.idontwant??[]}:void 0}}function wo(s){return s.control===void 0&&(s.control={graft:[],prune:[],ihave:[],iwant:[],idontwant:[]}),s}function tt(s){if(s.length<=1)return s;let e=()=>Math.floor(Math.random()*Math.floor(s.length));for(let t=0;t<s.length;t++){let r=e(),n=s[t];s[t]=s[r],s[r]=n}return s}function pl(s){return $(s,"base64")}function gl(s,e,t){switch(s){case Ut:return{type:Lt.Signing,author:e,key:At(t.publicKey),privateKey:t};case Xt:return{type:Lt.Anonymous};default:throw new Error(`Unknown signature policy "${s}"`)}}var ml=(s,e)=>{let t=X(e.toString(16).padStart(16,"0"),"base16"),r=At(s),n=new Uint8Array(r.byteLength+t.length);return n.set(r,0),n.set(t,r.byteLength),n};function bl(s){if(s.type!=="signed")throw new Error("expected signed message type");if(s.sequenceNumber==null)throw Error("missing seqno field");return ml(s.from.publicKey??s.key,s.sequenceNumber)}async function wl(s){return Tt.encode(s.data)}var Fn=class{index=0;input="";new(e){return this.index=0,this.input=e,this}readAtomically(e){let t=this.index,r=e();return r===void 0&&(this.index=t),r}parseWith(e){let t=e();if(this.index===this.input.length)return t}peekChar(){if(!(this.index>=this.input.length))return this.input[this.index]}readChar(){if(!(this.index>=this.input.length))return this.input[this.index++]}readGivenChar(e){return this.readAtomically(()=>{let t=this.readChar();if(t===e)return t})}readSeparator(e,t,r){return this.readAtomically(()=>{if(!(t>0&&this.readGivenChar(e)===void 0))return r()})}readNumber(e,t,r,n){return this.readAtomically(()=>{let i=0,o=0,a=this.peekChar();if(a===void 0)return;let c=a==="0",h=2**(8*n)-1;for(;;){let f=this.readAtomically(()=>{let l=this.readChar();if(l===void 0)return;let g=Number.parseInt(l,e);if(!Number.isNaN(g))return g});if(f===void 0)break;if(i*=e,i+=f,i>h||(o+=1,t!==void 0&&o>t))return}if(o!==0)return!r&&c&&o>1?void 0:i})}readIPv4Addr(){return this.readAtomically(()=>{let e=new Uint8Array(4);for(let t=0;t<e.length;t++){let r=this.readSeparator(".",t,()=>this.readNumber(10,3,!1,1));if(r===void 0)return;e[t]=r}return e})}readIPv6Addr(){let e=t=>{for(let r=0;r<t.length/2;r++){let n=r*2;if(r<t.length-3){let o=this.readSeparator(":",r,()=>this.readIPv4Addr());if(o!==void 0)return t[n]=o[0],t[n+1]=o[1],t[n+2]=o[2],t[n+3]=o[3],[n+4,!0]}let i=this.readSeparator(":",r,()=>this.readNumber(16,4,!0,2));if(i===void 0)return[n,!1];t[n]=i>>8,t[n+1]=i&255}return[t.length,!1]};return this.readAtomically(()=>{let t=new Uint8Array(16),[r,n]=e(t);if(r===16)return t;if(n||this.readGivenChar(":")===void 0||this.readGivenChar(":")===void 0)return;let i=new Uint8Array(14),o=16-(r+2),[a]=e(i.subarray(0,o));return t.set(i.subarray(0,a),16-a),t})}readIPAddr(){return this.readIPv4Addr()??this.readIPv6Addr()}};var ed=45;var yl=new Fn;function Vn(s){if(s.includes("%")&&(s=s.split("%")[0]),!(s.length>ed))return yl.new(s).parseWith(()=>yl.readIPAddr())}var ky=parseInt("0xFFFF",16),Ty=new Uint8Array([0,0,0,0,0,0,0,0,0,0,255,255]);function yo(s){return!!Vn(s)}var El=function(s,e=0,t){e=~~e,t=t??s.length-e;let r=new DataView(s.buffer);if(t===4){let n=[];for(let i=0;i<t;i++)n.push(s[e+i]);return n.join(".")}if(t===16){let n=[];for(let i=0;i<t;i+=2)n.push(r.getUint16(e+i).toString(16));return n.join(":").replace(/(^|:)0(:0)*:0(:|$)/,"$1::$3").replace(/:{3,4}/,"::")}return""};var vo={},xo={},cd=[[4,32,"ip4"],[6,16,"tcp"],[33,16,"dccp"],[41,128,"ip6"],[42,-1,"ip6zone"],[43,8,"ipcidr"],[53,-1,"dns",!0],[54,-1,"dns4",!0],[55,-1,"dns6",!0],[56,-1,"dnsaddr",!0],[132,16,"sctp"],[273,16,"udp"],[275,0,"p2p-webrtc-star"],[276,0,"p2p-webrtc-direct"],[277,0,"p2p-stardust"],[280,0,"webrtc-direct"],[281,0,"webrtc"],[290,0,"p2p-circuit"],[301,0,"udt"],[302,0,"utp"],[400,-1,"unix",!1,!0],[421,-1,"ipfs"],[421,-1,"p2p"],[443,0,"https"],[444,96,"onion"],[445,296,"onion3"],[446,-1,"garlic64"],[448,0,"tls"],[449,-1,"sni"],[460,0,"quic"],[461,0,"quic-v1"],[465,0,"webtransport"],[466,-1,"certhash"],[477,0,"ws"],[478,0,"wss"],[479,0,"p2p-websocket-star"],[480,0,"http"],[481,-1,"http-path"],[777,-1,"memory"]];cd.forEach(s=>{let e=ld(...s);xo[e.code]=e,vo[e.name]=e});function ld(s,e,t,r,n){return{code:s,size:e,name:t,resolvable:!!r,path:!!n}}function rr(s){if(typeof s=="number"){if(xo[s]!=null)return xo[s];throw new Error(`no protocol with code: ${s}`)}else if(typeof s=="string"){if(vo[s]!=null)return vo[s];throw new Error(`no protocol with name: ${s}`)}throw new Error(`invalid protocol id type: ${typeof s}`)}var gv=rr("ip4"),mv=rr("ip6"),bv=rr("ipcidr");function Bl(s,e){switch(rr(s).code){case 4:case 41:return ud(e);case 42:return So(e);case 6:case 273:case 33:case 132:return Il(e).toString();case 53:case 54:case 55:case 56:case 400:case 449:case 777:return So(e);case 421:return fd(e);case 444:return Sl(e);case 445:return Sl(e);case 466:return hd(e);case 481:return globalThis.encodeURIComponent(So(e));default:return $(e,"base16")}}var Eo=Object.values(Us).map(s=>s.decoder),wv=function(){let s=Eo[0].or(Eo[1]);return Eo.slice(2).forEach(e=>s=s.or(e)),s}();function ud(s){let e=El(s,0,s.length);if(e==null)throw new Error("ipBuff is required");if(!yo(e))throw new Error("invalid ip address");return e}function Il(s){return new DataView(s.buffer).getUint16(s.byteOffset)}function So(s){let e=us(s);if(s=s.slice(Ee(e)),s.length!==e)throw new Error("inconsistent lengths");return $(s)}function hd(s){let e=us(s),t=s.slice(Ee(e));if(t.length!==e)throw new Error("inconsistent lengths");return"u"+$(t,"base64url")}function fd(s){let e=us(s),t=s.slice(Ee(e));if(t.length!==e)throw new Error("inconsistent lengths");return $(t,"base58btc")}function Sl(s){let e=s.slice(0,s.length-2),t=s.slice(s.length-2),r=$(e,"base32"),n=Il(t);return`${r}:${n}`}var Hn;(function(s){s[s.ip4=4]="ip4",s[s.ip6=41]="ip6"})(Hn||(Hn={}));function _l(s){for(let e of s.tuples())switch(e[0]){case Hn.ip4:case Hn.ip6:return Bl(e[0],e[1]);default:break}return null}var _s=class{entries=new Map;validityMs;constructor(e){this.validityMs=e.validityMs}get size(){return this.entries.size}put(e,t){return this.entries.has(e)?!0:(this.entries.set(e,{value:t,validUntilMs:Date.now()+this.validityMs}),!1)}prune(){let e=Date.now();for(let[t,r]of this.entries.entries())if(r.validUntilMs<e)this.entries.delete(t);else break}has(e){return this.entries.has(e)}get(e){let t=this.entries.get(e);return t!=null&&t.validUntilMs>=Date.now()?t.value:void 0}clear(){this.entries.clear()}};var Hd=sr,Ue;(function(s){s[s.started=0]="started",s[s.stopped=1]="stopped"})(Ue||(Ue={}));var On=class extends or{globalSignaturePolicy;multicodecs=[sr,Zc,mo];publishConfig;dataTransform;peers=new Map;streamsInbound=new Map;streamsOutbound=new Map;outboundInflightQueue=wt({objectMode:!0});direct=new Set;floodsubPeers=new Set;seenCache;acceptFromWhitelist=new Map;topics=new Map;subscriptions=new Set;mesh=new Map;fanout=new Map;fanoutLastpub=new Map;gossip=new Map;control=new Map;peerhave=new Map;iasked=new Map;backoff=new Map;outbound=new Map;msgIdFn;fastMsgIdFn;msgIdToStrFn;fastMsgIdCache;publishedMessageIds;mcache;score;topicValidators=new Map;log;heartbeatTicks=0;gossipTracer;idontwantCounts=new Map;idontwants=new Map;components;directPeerInitial=null;static multicodec=sr;opts;decodeRpcLimits;metrics;status={code:Ue.stopped};maxInboundStreams;maxOutboundStreams;runOnLimitedConnection;allowedTopics;heartbeatTimer=null;constructor(e,t={}){super();let r={fallbackToFloodsub:!0,floodPublish:!0,batchPublish:!1,tagMeshPeers:!0,doPX:!1,directPeers:[],D:6,Dlo:4,Dhi:12,Dscore:4,Dout:2,Dlazy:6,heartbeatInterval:1e3,fanoutTTL:6e4,mcacheLength:5,mcacheGossip:3,seenTTL:12e4,gossipsubIWantFollowupMs:3e3,prunePeers:16,pruneBackoff:6e4,unsubcribeBackoff:1e4,graftFloodThreshold:1e4,opportunisticGraftPeers:2,opportunisticGraftTicks:60,directConnectTicks:300,gossipFactor:.25,idontwantMinDataSize:512,idontwantMaxMessages:512,...t,scoreParams:sl(t.scoreParams),scoreThresholds:nl(t.scoreThresholds)};if(this.components=e,this.decodeRpcLimits=r.decodeRpcLimits??Yc,this.globalSignaturePolicy=r.globalSignaturePolicy??Ut,r.fallbackToFloodsub&&this.multicodecs.push(go),this.log=e.logger.forComponent(r.debugName??"libp2p:gossipsub"),this.opts=r,this.direct=new Set(r.directPeers.map(n=>n.id.toString())),this.seenCache=new _s({validityMs:r.seenTTL}),this.publishedMessageIds=new _s({validityMs:r.seenTTL}),t.msgIdFn!=null)this.msgIdFn=t.msgIdFn;else switch(this.globalSignaturePolicy){case Ut:this.msgIdFn=bl;break;case Xt:this.msgIdFn=wl;break;default:throw new Error(`Invalid globalSignaturePolicy: ${this.globalSignaturePolicy}`)}if(t.fastMsgIdFn!=null&&(this.fastMsgIdFn=t.fastMsgIdFn,this.fastMsgIdCache=new _s({validityMs:r.seenTTL})),this.msgIdToStrFn=t.msgIdToStrFn??pl,this.mcache=t.messageCache??new Mn(r.mcacheGossip,r.mcacheLength,this.msgIdToStrFn),t.dataTransform!=null&&(this.dataTransform=t.dataTransform),t.metricsRegister!=null){if(t.metricsTopicStrToLabel==null)throw Error("Must set metricsTopicStrToLabel with metrics");let n=Math.max(...Object.values(r.scoreParams.topics).map(o=>o.meshMessageDeliveriesWindow),1e3),i=tl(t.metricsRegister,t.metricsTopicStrToLabel,{gossipPromiseExpireSec:this.opts.gossipsubIWantFollowupMs/1e3,behaviourPenaltyThreshold:r.scoreParams.behaviourPenaltyThreshold,maxMeshMessageDeliveriesWindowSec:n/1e3});i.mcacheSize.addCollect(()=>{this.onScrapeMetrics(i)});for(let o of this.multicodecs)i.protocolsEnabled.set({protocol:o},1);this.metrics=i}else this.metrics=null;this.gossipTracer=new Un(this.opts.gossipsubIWantFollowupMs,this.msgIdToStrFn,this.metrics),this.score=new Nn(this.opts.scoreParams,this.metrics,this.components.logger,{scoreCacheValidityMs:r.heartbeatInterval}),this.maxInboundStreams=t.maxInboundStreams,this.maxOutboundStreams=t.maxOutboundStreams,this.runOnLimitedConnection=t.runOnLimitedConnection,this.allowedTopics=r.allowedTopics!=null?new Set(r.allowedTopics):null}[Symbol.toStringTag]="@chainsafe/libp2p-gossipsub";[Ro]=["@libp2p/pubsub"];[Lo]=["@libp2p/identify"];getPeers(){return[...this.peers.values()]}isStarted(){return this.status.code===Ue.started}async start(){if(this.isStarted())return;this.log("starting"),this.publishConfig=gl(this.globalSignaturePolicy,this.components.peerId,this.components.privateKey),this.outboundInflightQueue=wt({objectMode:!0}),Es(this.outboundInflightQueue,async i=>{for await(let{peerId:o,connection:a}of i)await this.createOutboundStream(o,a)}).catch(i=>{this.log.error("outbound inflight queue error",i)}),await Promise.all(this.opts.directPeers.map(async i=>{await this.components.peerStore.merge(i.id,{multiaddrs:i.addrs})}));let e=this.components.registrar;await Promise.all(this.multicodecs.map(async i=>e.handle(i,this.onIncomingStream.bind(this),{maxInboundStreams:this.maxInboundStreams,maxOutboundStreams:this.maxOutboundStreams,runOnLimitedConnection:this.runOnLimitedConnection})));let t={onConnect:this.onPeerConnected.bind(this),onDisconnect:this.onPeerDisconnected.bind(this),notifyOnLimitedConnection:this.runOnLimitedConnection},r=await Promise.all(this.multicodecs.map(async i=>e.register(i,t))),n=setTimeout(this.runHeartbeat,100);this.status={code:Ue.started,registrarTopologyIds:r,heartbeatTimeout:n,hearbeatStartMs:Date.now()+100},this.score.start(),this.directPeerInitial=setTimeout(()=>{Promise.resolve().then(async()=>{await Promise.all(Array.from(this.direct).map(async i=>this.connect(i)))}).catch(i=>{this.log(i)})},1e3),this.opts.tagMeshPeers&&(this.addEventListener("gossipsub:graft",this.tagMeshPeer),this.addEventListener("gossipsub:prune",this.untagMeshPeer)),this.log("started")}async stop(){if(this.log("stopping"),this.status.code!==Ue.started)return;let{registrarTopologyIds:e}=this.status;this.status={code:Ue.stopped},this.opts.tagMeshPeers&&(this.removeEventListener("gossipsub:graft",this.tagMeshPeer),this.removeEventListener("gossipsub:prune",this.untagMeshPeer));let t=this.components.registrar;await Promise.all(this.multicodecs.map(async n=>t.unhandle(n))),e.forEach(n=>{t.unregister(n)}),this.outboundInflightQueue.end();let r=[];for(let n of this.streamsOutbound.values())r.push(n.close());this.streamsOutbound.clear();for(let n of this.streamsInbound.values())r.push(n.close());this.streamsInbound.clear(),await Promise.all(r),this.peers.clear(),this.subscriptions.clear(),this.heartbeatTimer!=null&&(this.heartbeatTimer.cancel(),this.heartbeatTimer=null),this.score.stop(),this.mesh.clear(),this.fanout.clear(),this.fanoutLastpub.clear(),this.gossip.clear(),this.control.clear(),this.peerhave.clear(),this.iasked.clear(),this.backoff.clear(),this.outbound.clear(),this.gossipTracer.clear(),this.seenCache.clear(),this.fastMsgIdCache!=null&&this.fastMsgIdCache.clear(),this.directPeerInitial!=null&&clearTimeout(this.directPeerInitial),this.idontwantCounts.clear(),this.idontwants.clear(),this.log("stopped")}dumpPeerScoreStats(){return this.score.dumpPeerScoreStats()}onIncomingStream({stream:e,connection:t}){if(!this.isStarted())return;let r=t.remotePeer;this.addPeer(r,t.direction,t.remoteAddr),this.createInboundStream(r,e),this.outboundInflightQueue.push({peerId:r,connection:t})}onPeerConnected(e,t){this.metrics?.newConnectionCount.inc({status:t.status}),!(!this.isStarted()||t.status!=="open")&&(this.addPeer(e,t.direction,t.remoteAddr),this.outboundInflightQueue.push({peerId:e,connection:t}))}onPeerDisconnected(e){this.log("connection ended %p",e),this.removePeer(e)}async createOutboundStream(e,t){if(!this.isStarted())return;let r=e.toString();if(this.peers.has(r)&&!this.streamsOutbound.has(r))try{let n=new Rn(await t.newStream(this.multicodecs,{runOnLimitedConnection:this.runOnLimitedConnection}),o=>{this.log.error("outbound pipe error",o)},{maxBufferSize:this.opts.maxOutboundBufferSize});this.log("create outbound stream %p",e),this.streamsOutbound.set(r,n);let i=n.protocol;i===go&&this.floodsubPeers.add(r),this.metrics?.peersPerProtocol.inc({protocol:i},1),this.subscriptions.size>0&&(this.log("send subscriptions to",r),this.sendSubscriptions(r,Array.from(this.subscriptions),!0))}catch(n){this.log.error("createOutboundStream error",n)}}createInboundStream(e,t){if(!this.isStarted())return;let r=e.toString();if(!this.peers.has(r))return;let n=this.streamsInbound.get(r);n!==void 0&&(this.log("replacing existing inbound steam %s",r),n.close().catch(o=>{this.log.error(o)})),this.log("create inbound stream %s",r);let i=new Ln(t,{maxDataLength:this.opts.maxInboundDataLength});this.streamsInbound.set(r,i),this.pipePeerReadStream(e,i.source).catch(o=>{this.log(o)})}addPeer(e,t,r){let n=e.toString();if(!this.peers.has(n)){this.log("new peer %p",e),this.peers.set(n,e),this.score.addPeer(n);let i=_l(r);i!==null?this.score.addIP(n,i):this.log("Added peer has no IP in current address %s %s",n,r.toString()),this.outbound.has(n)||this.outbound.set(n,t==="outbound")}}removePeer(e){let t=e.toString();if(!this.peers.has(t))return;this.log("delete peer %p",e),this.peers.delete(t);let r=this.streamsOutbound.get(t),n=this.streamsInbound.get(t);r!=null&&this.metrics?.peersPerProtocol.inc({protocol:r.protocol},-1),r?.close().catch(i=>{this.log.error(i)}),n?.close().catch(i=>{this.log.error(i)}),this.streamsOutbound.delete(t),this.streamsInbound.delete(t);for(let i of this.topics.values())i.delete(t);for(let[i,o]of this.mesh)o.delete(t)&&this.metrics?.onRemoveFromMesh(i,$e.Dc,1);for(let i of this.fanout.values())i.delete(t);this.floodsubPeers.delete(t),this.gossip.delete(t),this.control.delete(t),this.outbound.delete(t),this.idontwantCounts.delete(t),this.idontwants.delete(t),this.score.removePeer(t),this.acceptFromWhitelist.delete(t)}get started(){return this.status.code===Ue.started}getMeshPeers(e){let t=this.mesh.get(e);return t!=null?Array.from(t):[]}getSubscribers(e){let t=this.topics.get(e);return(t!=null?Array.from(t):[]).map(r=>this.peers.get(r)??Zt(r))}getTopics(){return Array.from(this.subscriptions)}async pipePeerReadStream(e,t){try{await Es(t,async r=>{for await(let n of r)try{let i=n.subarray(),o=yt.decode(i,{limits:{subscriptions:this.decodeRpcLimits.maxSubscriptions,messages:this.decodeRpcLimits.maxMessages,control$:{ihave:this.decodeRpcLimits.maxIhaveMessageIDs,iwant:this.decodeRpcLimits.maxIwantMessageIDs,graft:this.decodeRpcLimits.maxControlMessages,prune:this.decodeRpcLimits.maxControlMessages,prune$:{peers:this.decodeRpcLimits.maxPeerInfos},idontwant:this.decodeRpcLimits.maxControlMessages,idontwant$:{messageIDs:this.decodeRpcLimits.maxIdontwantMessageIDs}}}});if(this.metrics?.onRpcRecv(o,i.length),this.opts.awaitRpcHandler)try{await this.handleReceivedRpc(e,o)}catch(a){this.metrics?.onRpcRecvError(),this.log(a)}else this.handleReceivedRpc(e,o).catch(a=>{this.metrics?.onRpcRecvError(),this.log(a)})}catch(i){this.metrics?.onRpcDataError(),this.log(i)}})}catch(r){this.metrics?.onPeerReadStreamError(),this.handlePeerReadStreamError(r,e)}}handlePeerReadStreamError(e,t){this.log.error(e),this.onPeerDisconnected(t)}async handleReceivedRpc(e,t){if(!this.acceptFrom(e.toString())){this.log("received message from unacceptable peer %p",e),this.metrics?.rpcRecvNotAccepted.inc();return}let r=t.subscriptions!=null?t.subscriptions.length:0,n=t.messages!=null?t.messages.length:0,i=0,o=0,a=0,c=0;if(t.control!=null&&(t.control.ihave!=null&&(i=t.control.ihave.length),t.control.iwant!=null&&(o=t.control.iwant.length),t.control.graft!=null&&(a=t.control.graft.length),t.control.prune!=null&&(c=t.control.prune.length)),this.log(`rpc.from ${e.toString()} subscriptions ${r} messages ${n} ihave ${i} iwant ${o} graft ${a} prune ${c}`),t.subscriptions!=null&&t.subscriptions.length>0){let h=[];t.subscriptions.forEach(f=>{let l=f.topic,g=f.subscribe===!0;if(l!=null){if(this.allowedTopics!=null&&!this.allowedTopics.has(l))return;this.handleReceivedSubscription(e,l,g),h.push({topic:l,subscribe:g})}}),this.safeDispatchEvent("subscription-change",{detail:{peerId:e,subscriptions:h}})}for(let h of t.messages){if(this.allowedTopics!=null&&!this.allowedTopics.has(h.topic))continue;let f=this.handleReceivedMessage(e,h).catch(l=>{this.metrics?.onMsgRecvError(h.topic),this.log(l)});this.opts.awaitRpcMessageHandler&&await f}t.control!=null&&await this.handleControlMessage(e.toString(),t.control)}handleReceivedSubscription(e,t,r){this.log("subscription update from %p topic %s",e,t);let n=this.topics.get(t);n==null&&(n=new Set,this.topics.set(t,n)),r?n.add(e.toString()):n.delete(e.toString())}async handleReceivedMessage(e,t){this.metrics?.onMsgRecvPreValidation(t.topic);let r=await this.validateReceivedMessage(e,t);this.metrics?.onPrevalidationResult(t.topic,r.code);let n=r.code;switch(n){case we.duplicate:this.score.duplicateMessage(e.toString(),r.msgIdStr,t.topic),this.gossipTracer.deliverMessage(r.msgIdStr,!0),this.mcache.observeDuplicate(r.msgIdStr,e.toString());return;case we.invalid:if(r.msgIdStr!=null){let i=r.msgIdStr;this.score.rejectMessage(e.toString(),i,t.topic,r.reason),this.gossipTracer.rejectMessage(i,r.reason)}else this.score.rejectInvalidMessage(e.toString(),t.topic);this.metrics?.onMsgRecvInvalid(t.topic,r);return;case we.valid:this.score.validateMessage(r.messageId.msgIdStr),this.gossipTracer.deliverMessage(r.messageId.msgIdStr),this.mcache.put(r.messageId,t,!this.opts.asyncValidation),this.subscriptions.has(t.topic)&&(!this.components.peerId.equals(e)||this.opts.emitSelf)&&(super.dispatchEvent(new CustomEvent("gossipsub:message",{detail:{propagationSource:e,msgId:r.messageId.msgIdStr,msg:r.msg}})),super.dispatchEvent(new CustomEvent("message",{detail:r.msg}))),this.opts.asyncValidation||this.forwardMessage(r.messageId.msgIdStr,t,e.toString());break;default:throw new Error(`Invalid validation result: ${n}`)}}async validateReceivedMessage(e,t){let r=this.fastMsgIdFn?.(t),n=r!==void 0?this.fastMsgIdCache?.get(r):void 0;if(n!=null)return{code:we.duplicate,msgIdStr:n};let i=await dl(this.globalSignaturePolicy,t);if(!i.valid)return{code:we.invalid,reason:Ae.Error,error:i.error};let o=i.message;try{this.dataTransform!=null&&(o.data=this.dataTransform.inboundTransform(t.topic,o.data))}catch(l){return this.log("Invalid message, transform failed",l),{code:we.invalid,reason:Ae.Error,error:be.TransformFailed}}let a=await this.msgIdFn(o),c=this.msgIdToStrFn(a),h={msgId:a,msgIdStr:c};if(r!==void 0&&this.fastMsgIdCache!=null&&this.fastMsgIdCache.put(r,c)&&this.metrics?.fastMsgIdCacheCollision.inc(),this.seenCache.has(c))return{code:we.duplicate,msgIdStr:c};this.seenCache.put(c),(t.data?.length??0)>=this.opts.idontwantMinDataSize&&this.sendIDontWants(a,t.topic,e.toString());let f=this.topicValidators.get(t.topic);if(f!=null){let l;try{l=await f(e,o)}catch(g){let u=g.code;u===Xc&&(l=xe.Ignore),u===Jc?l=xe.Reject:l=xe.Ignore}if(l!==xe.Accept)return{code:we.invalid,reason:bo(l),msgIdStr:c}}return{code:we.valid,messageId:h,msg:o}}getScore(e){return this.score.score(e)}sendSubscriptions(e,t,r){this.sendRpc(e,{subscriptions:t.map(n=>({topic:n,subscribe:r})),messages:[]})}async handleControlMessage(e,t){if(t===void 0)return;let r=t.ihave?.length>0?this.handleIHave(e,t.ihave):[],n=t.iwant?.length>0?this.handleIWant(e,t.iwant):[],i=t.graft?.length>0?await this.handleGraft(e,t.graft):[];if(t.prune?.length>0&&await this.handlePrune(e,t.prune),t.idontwant?.length>0&&this.handleIdontwant(e,t.idontwant),r.length===0&&n.length===0&&i.length===0)return;let o=this.sendRpc(e,je(n,{iwant:r,prune:i})),a=r[0]?.messageIDs;a!=null&&(o?this.gossipTracer.addPromise(e,a):this.metrics?.iwantPromiseUntracked.inc(1))}acceptFrom(e){if(this.direct.has(e))return!0;let t=Date.now(),r=this.acceptFromWhitelist.get(e);if(r!=null&&r.messagesAccepted<128&&r.acceptUntil>=t)return r.messagesAccepted+=1,!0;let n=this.score.score(e);return n>=0?this.acceptFromWhitelist.set(e,{messagesAccepted:0,acceptUntil:t+1e3}):this.acceptFromWhitelist.delete(e),n>=this.opts.scoreThresholds.graylistThreshold}handleIHave(e,t){if(t.length===0)return[];let r=this.score.score(e);if(r<this.opts.scoreThresholds.gossipThreshold)return this.log("IHAVE: ignoring peer %s with score below threshold [ score = %d ]",e,r),this.metrics?.ihaveRcvIgnored.inc({reason:Is.LowScore}),[];let n=(this.peerhave.get(e)??0)+1;if(this.peerhave.set(e,n),n>10)return this.log("IHAVE: peer %s has advertised too many times (%d) within this heartbeat interval; ignoring",e,n),this.metrics?.ihaveRcvIgnored.inc({reason:Is.MaxIhave}),[];let i=this.iasked.get(e)??0;if(i>=5e3)return this.log("IHAVE: peer %s has already advertised too many messages (%d); ignoring",e,i),this.metrics?.ihaveRcvIgnored.inc({reason:Is.MaxIasked}),[];let o=new Map;if(t.forEach(({topicID:h,messageIDs:f})=>{if(h==null||f==null||!this.mesh.has(h))return;let l=0;f.forEach(g=>{let u=this.msgIdToStrFn(g);this.seenCache.has(u)||(o.set(u,g),l++)}),this.metrics?.onIhaveRcv(h,f.length,l)}),o.size===0)return[];let a=o.size;a+i>5e3&&(a=5e3-i),this.log("IHAVE: Asking for %d out of %d messages from %s",a,o.size,e);let c=Array.from(o.values());return tt(c),c=c.slice(0,a),this.iasked.set(e,i+a),[{messageIDs:c}]}handleIWant(e,t){if(t.length===0)return[];let r=this.score.score(e);if(r<this.opts.scoreThresholds.gossipThreshold)return this.log("IWANT: ignoring peer %s with score below threshold [score = %d]",e,r),[];let n=new Map,i=new Map,o=0;return t.forEach(({messageIDs:a})=>{a?.forEach(c=>{let h=this.msgIdToStrFn(c),f=this.mcache.getWithIWantCount(h,e);if(f==null){o++;return}if(i.set(f.msg.topic,1+(i.get(f.msg.topic)??0)),f.count>3){this.log("IWANT: Peer %s has asked for message %s too many times: ignoring request",e,c);return}n.set(h,f.msg)})}),this.metrics?.onIwantRcv(i,o),n.size===0?(this.log("IWANT: Could not provide any wanted messages to %s",e),[]):(this.log("IWANT: Sending %d messages to %s",n.size,e),Array.from(n.values()))}async handleGraft(e,t){let r=[],n=this.score.score(e),i=Date.now(),o=this.opts.doPX;if(t.forEach(({topicID:c})=>{if(c==null)return;let h=this.mesh.get(c);if(h==null){o=!1;return}if(h.has(e))return;let f=this.backoff.get(c)?.get(e);if(this.direct.has(e))this.log("GRAFT: ignoring request from direct peer %s",e),r.push(c),o=!1;else if(typeof f=="number"&&i<f){this.log("GRAFT: ignoring backed off peer %s",e),this.score.addPenalty(e,1,Bs.GraftBackoff),o=!1;let l=f+this.opts.graftFloodThreshold-this.opts.pruneBackoff;i<l&&this.score.addPenalty(e,1,Bs.GraftBackoff),this.addBackoff(e,c),r.push(c)}else n<0?(this.log("GRAFT: ignoring peer %s with negative score: score=%d, topic=%s",e,n,c),r.push(c),o=!1,this.addBackoff(e,c)):h.size>=this.opts.Dhi&&!(this.outbound.get(e)??!1)?(r.push(c),this.addBackoff(e,c)):(this.log("GRAFT: Add mesh link from %s in %s",e,c),this.score.graft(e,c),h.add(e),this.metrics?.onAddToMesh(c,ke.Subscribed,1));this.safeDispatchEvent("gossipsub:graft",{detail:{peerId:e,topic:c,direction:"inbound"}})}),r.length===0)return[];let a=!1;return Promise.all(r.map(async c=>this.makePrune(e,c,o,a)))}async handlePrune(e,t){let r=this.score.score(e);for(let{topicID:n,backoff:i,peers:o}of t){if(n==null)continue;let a=this.mesh.get(n);if(a==null)return;this.log("PRUNE: Remove mesh link to %s in %s",e,n),this.score.prune(e,n),a.has(e)&&(a.delete(e),this.metrics?.onRemoveFromMesh(n,$e.Prune,1)),typeof i=="number"&&i>0?this.doAddBackoff(e,n,i*1e3):this.addBackoff(e,n),o!=null&&o.length>0&&(r<this.opts.scoreThresholds.acceptPXThreshold?this.log("PRUNE: ignoring PX from peer %s with insufficient score [score = %d, topic = %s]",e,r,n):await this.pxConnect(o)),this.safeDispatchEvent("gossipsub:prune",{detail:{peerId:e,topic:n,direction:"inbound"}})}}handleIdontwant(e,t){let r=this.idontwantCounts.get(e)??0;if(r>=this.opts.idontwantMaxMessages)return;let n=r,i=this.idontwants.get(e);i==null&&(i=new Map,this.idontwants.set(e,i));let o=0;e:for(let{messageIDs:c}of t)for(let h of c){if(r>=this.opts.idontwantMaxMessages)break e;r++;let f=this.msgIdToStrFn(h);i.set(f,this.heartbeatTicks),this.mcache.msgs.has(f)||o++}this.idontwantCounts.set(e,r);let a=r-n;this.metrics?.onIdontwantRcv(a,o)}addBackoff(e,t){this.doAddBackoff(e,t,this.opts.pruneBackoff)}doAddBackoff(e,t,r){let n=this.backoff.get(t);n==null&&(n=new Map,this.backoff.set(t,n));let i=Date.now()+r;(n.get(e)??0)<i&&n.set(e,i)}applyIwantPenalties(){this.gossipTracer.getBrokenPromises().forEach((e,t)=>{this.log("peer %s didn't follow up in %d IWANT requests; adding penalty",t,e),this.score.addPenalty(t,e,Bs.BrokenPromise)})}clearBackoff(){if(this.heartbeatTicks%15!==0)return;let e=Date.now();this.backoff.forEach((t,r)=>{t.forEach((n,i)=>{n+1*this.opts.heartbeatInterval<e&&t.delete(i)}),t.size===0&&this.backoff.delete(r)})}async directConnect(){let e=[];this.direct.forEach(t=>{this.streamsOutbound.has(t)||e.push(t)}),await Promise.all(e.map(async t=>this.connect(t)))}async pxConnect(e){e.length>this.opts.prunePeers&&(tt(e),e=e.slice(0,this.opts.prunePeers));let t=[];await Promise.all(e.map(async r=>{if(r.peerID==null)return;let n=Qs(Oe(r.peerID)),i=n.toString();if(!this.peers.has(i)){if(r.signedPeerRecord==null){t.push(i);return}try{if(!await this.components.peerStore.consumePeerRecord(r.signedPeerRecord,n)){this.log("bogus peer record obtained through px: could not add peer record to address book");return}t.push(i)}catch{this.log("bogus peer record obtained through px: invalid signature or not a peer record")}}})),t.length!==0&&await Promise.all(t.map(async r=>this.connect(r)))}async connect(e){this.log("Initiating connection with %s",e);let t=Zt(e),r=await this.components.connectionManager.openConnection(t);for(let n of this.multicodecs)for(let i of this.components.registrar.getTopologies(n))i.onConnect?.(t,r)}subscribe(e){if(this.status.code!==Ue.started)throw new Error("Pubsub has not started");if(!this.subscriptions.has(e)){this.subscriptions.add(e);for(let t of this.peers.keys())this.sendSubscriptions(t,[e],!0)}this.join(e)}unsubscribe(e){if(this.status.code!==Ue.started)throw new Error("Pubsub is not started");let t=this.subscriptions.delete(e);if(this.log("unsubscribe from %s - am subscribed %s",e,t),t)for(let r of this.peers.keys())this.sendSubscriptions(r,[e],!1);this.leave(e)}join(e){if(this.status.code!==Ue.started)throw new Error("Gossipsub has not started");if(this.mesh.has(e))return;this.log("JOIN %s",e),this.metrics?.onJoin(e);let t=new Set,r=this.backoff.get(e),n=this.fanout.get(e);if(n!=null&&(this.fanout.delete(e),this.fanoutLastpub.delete(e),n.forEach(i=>{!this.direct.has(i)&&this.score.score(i)>=0&&r?.has(i)!==!0&&t.add(i)}),this.metrics?.onAddToMesh(e,ke.Fanout,t.size)),t.size<this.opts.D){let i=t.size;this.getRandomGossipPeers(e,this.opts.D,a=>!t.has(a)&&!this.direct.has(a)&&this.score.score(a)>=0&&r?.has(a)!==!0).forEach(a=>{t.add(a)}),this.metrics?.onAddToMesh(e,ke.Random,t.size-i)}this.mesh.set(e,t),t.forEach(i=>{this.log("JOIN: Add mesh link to %s in %s",i,e),this.sendGraft(i,e)})}leave(e){if(this.status.code!==Ue.started)throw new Error("Gossipsub has not started");this.log("LEAVE %s",e),this.metrics?.onLeave(e);let t=this.mesh.get(e);t!=null&&(Promise.all(Array.from(t).map(async r=>{this.log("LEAVE: Remove mesh link to %s in %s",r,e),await this.sendPrune(r,e)})).catch(r=>{this.log("Error sending prunes to mesh peers",r)}),this.mesh.delete(e))}selectPeersToForward(e,t,r){let n=new Set,i=this.topics.get(e);i!=null&&(this.direct.forEach(a=>{i.has(a)&&t!==a&&!(r?.has(a)??!1)&&n.add(a)}),this.floodsubPeers.forEach(a=>{i.has(a)&&t!==a&&!(r?.has(a)??!1)&&this.score.score(a)>=this.opts.scoreThresholds.publishThreshold&&n.add(a)}));let o=this.mesh.get(e);return o!=null&&o.size>0&&o.forEach(a=>{t!==a&&!(r?.has(a)??!1)&&n.add(a)}),n}selectPeersToPublish(e){let t=new Set,r={direct:0,floodsub:0,mesh:0,fanout:0},n=this.topics.get(e);if(n!=null)if(this.opts.floodPublish)n.forEach(i=>{this.direct.has(i)?(t.add(i),r.direct++):this.score.score(i)>=this.opts.scoreThresholds.publishThreshold&&(t.add(i),r.floodsub++)});else{this.direct.forEach(o=>{n.has(o)&&(t.add(o),r.direct++)}),this.floodsubPeers.forEach(o=>{n.has(o)&&this.score.score(o)>=this.opts.scoreThresholds.publishThreshold&&(t.add(o),r.floodsub++)});let i=this.mesh.get(e);if(i!=null&&i.size>0)i.forEach(o=>{t.add(o),r.mesh++}),i.size<this.opts.D&&this.getRandomGossipPeers(e,this.opts.D-i.size,a=>!i.has(a)&&!this.direct.has(a)&&!this.floodsubPeers.has(a)&&this.score.score(a)>=this.opts.scoreThresholds.publishThreshold).forEach(a=>{t.add(a),r.mesh++});else{let o=this.fanout.get(e);if(o!=null&&o.size>0)o.forEach(a=>{t.add(a),r.fanout++});else{let a=this.getRandomGossipPeers(e,this.opts.D,c=>this.score.score(c)>=this.opts.scoreThresholds.publishThreshold);a.size>0&&(this.fanout.set(e,a),a.forEach(c=>{t.add(c),r.fanout++}))}this.fanoutLastpub.set(e,Date.now())}}return{tosend:t,tosendCount:r}}forwardMessage(e,t,r,n){r!=null&&this.score.deliverMessage(r,e,t.topic);let i=this.selectPeersToForward(t.topic,r,n);i.forEach(o=>{this.sendRpc(o,je([t]))}),this.metrics?.onForwardMsg(t.topic,i.size)}async publish(e,t,r){let n=Date.now(),i=this.dataTransform!=null?this.dataTransform.outboundTransform(e,t):t;if(this.publishConfig==null)throw Error("PublishError.Uninitialized");let{raw:o,msg:a}=await fl(this.publishConfig,e,t,i),c=await this.msgIdFn(a),h=this.msgIdToStrFn(c),f=r?.ignoreDuplicatePublishError??this.opts.ignoreDuplicatePublishError;if(this.seenCache.has(h)){if(f)return this.metrics?.onPublishDuplicateMsg(e),{recipients:[]};throw Error("PublishError.Duplicate")}let{tosend:l,tosendCount:g}=this.selectPeersToPublish(e),u=this.opts.emitSelf&&this.subscriptions.has(e),d=r?.allowPublishToZeroTopicPeers??this.opts.allowPublishToZeroTopicPeers;if(l.size===0&&!d&&!u)throw Error("PublishError.NoPeersSubscribedToTopic");this.seenCache.put(h),this.mcache.put({msgId:c,msgIdStr:h},o,!0),this.publishedMessageIds.put(h);let m=r?.batchPublish??this.opts.batchPublish,p=je([o]);if(m)this.sendRpcInBatch(l,p);else for(let y of l)this.sendRpc(y,p)||l.delete(y);let b=Date.now()-n;return this.metrics?.onPublishMsg(e,g,l.size,o.data!=null?o.data.length:0,b),u&&(l.add(this.components.peerId.toString()),super.dispatchEvent(new CustomEvent("gossipsub:message",{detail:{propagationSource:this.components.peerId,msgId:h,msg:a}})),super.dispatchEvent(new CustomEvent("message",{detail:a}))),{recipients:Array.from(l.values()).map(y=>this.peers.get(y)??Zt(y))}}sendRpcInBatch(e,t){let r=yt.encode(t),n=vs.single(r);for(let i of e){let o=this.streamsOutbound.get(i);if(o==null){this.log(`Cannot send RPC to ${i} as there is no open stream to it available`),e.delete(i);continue}try{o.pushPrefixed(n)}catch(a){e.delete(i),this.log.error(`Cannot send rpc to ${i}`,a)}this.metrics?.onRpcSent(t,r.length)}}reportMessageValidationResult(e,t,r){let n;if(r===xe.Accept){if(n=this.mcache.validate(e),n!=null){let{message:o,originatingPeers:a}=n;this.score.deliverMessage(t,e,o.topic),this.forwardMessage(e,n.message,t,a)}}else if(n=this.mcache.remove(e),n!=null){let o=bo(r),{message:a,originatingPeers:c}=n;this.score.rejectMessage(t,e,a.topic,o);for(let h of c)this.score.rejectMessage(h,e,a.topic,o)}let i=this.score.messageFirstSeenTimestampMs(e);this.metrics?.onReportValidation(n,r,i)}sendGraft(e,t){let n=je([],{graft:[{topicID:t}]});this.sendRpc(e,n)}async sendPrune(e,t){let n=[await this.makePrune(e,t,this.opts.doPX,!0)],i=je([],{prune:n});this.sendRpc(e,i)}sendIDontWants(e,t,r){let n=this.mesh.get(t);if(n==null)return;let i=new Set(n);i.delete(r);for(let a of i)this.streamsOutbound.get(a)?.protocol!==sr&&i.delete(a);let o=je([],{idontwant:[{messageIDs:[e]}]});this.sendRpcInBatch(i,o)}sendRpc(e,t){let r=this.streamsOutbound.get(e);if(r==null)return this.log(`Cannot send RPC to ${e} as there is no open stream to it available`),!1;let n=this.control.get(e);n!=null&&(this.piggybackControl(e,t,n),this.control.delete(e));let i=this.gossip.get(e);i!=null&&(this.piggybackGossip(e,t,i),this.gossip.delete(e));let o=yt.encode(t);try{r.push(o)}catch(a){return this.log.error(`Cannot send rpc to ${e}`,a),n!=null&&this.control.set(e,n),i!=null&&this.gossip.set(e,i),!1}if(this.metrics?.onRpcSent(t,o.length),t.control?.graft!=null)for(let a of t.control?.graft)a.topicID!=null&&this.safeDispatchEvent("gossipsub:graft",{detail:{peerId:e,topic:a.topicID,direction:"outbound"}});if(t.control?.prune!=null)for(let a of t.control?.prune)a.topicID!=null&&this.safeDispatchEvent("gossipsub:prune",{detail:{peerId:e,topic:a.topicID,direction:"outbound"}});return!0}piggybackControl(e,t,r){let n=wo(t);for(let i of r.graft)i.topicID!=null&&(this.mesh.get(i.topicID)?.has(e)??!1)&&n.control.graft.push(i);for(let i of r.prune)i.topicID!=null&&!(this.mesh.get(i.topicID)?.has(e)??!1)&&n.control.prune.push(i)}piggybackGossip(e,t,r){let n=wo(t);n.control.ihave=r}async sendGraftPrune(e,t,r){let n=this.opts.doPX,i=!1;for(let[o,a]of e){let c=a.map(l=>({topicID:l})),h=[],f=t.get(o);f!=null&&(h=await Promise.all(f.map(async l=>this.makePrune(o,l,n&&!(r.get(o)??!1),i))),t.delete(o)),this.sendRpc(o,je([],{graft:c,prune:h}))}for(let[o,a]of t){let c=await Promise.all(a.map(async h=>this.makePrune(o,h,n&&!(r.get(o)??!1),i)));this.sendRpc(o,je([],{prune:c}))}}emitGossip(e){let t=this.mcache.getGossipIDs(new Set(e.keys()));for(let[r,n]of e)this.doEmitGossip(r,n,t.get(r)??[])}doEmitGossip(e,t,r){if(r.length===0||(tt(r),r.length>5e3&&this.log("too many messages for gossip; will truncate IHAVE list (%d messages)",r.length),t.size===0))return;let n=this.opts.Dlazy,o=this.opts.gossipFactor*t.size,a=t;o>n&&(n=o),n>a.size?n=a.size:a=tt(Array.from(a)).slice(0,n),a.forEach(c=>{let h=r;r.length>5e3&&(h=tt(h.slice()).slice(0,5e3)),this.pushGossip(c,{topicID:e,messageIDs:h})})}flush(){for(let[e,t]of this.gossip.entries())this.gossip.delete(e),this.sendRpc(e,je([],{ihave:t}));for(let[e,t]of this.control.entries()){this.control.delete(e);let r=je([],{graft:t.graft,prune:t.prune});this.sendRpc(e,r)}}pushGossip(e,t){this.log("Add gossip to %s",e);let r=this.gossip.get(e)??[];this.gossip.set(e,r.concat(t))}async makePrune(e,t,r,n){if(this.score.prune(e,t),this.streamsOutbound.get(e)?.protocol===mo)return{topicID:t,peers:[]};let i=n?this.opts.unsubcribeBackoff:this.opts.pruneBackoff,o=i/1e3;if(this.doAddBackoff(e,t,i),!r)return{topicID:t,peers:[],backoff:o};let a=this.getRandomGossipPeers(t,this.opts.prunePeers,h=>h!==e&&this.score.score(h)>=0),c=await Promise.all(Array.from(a).map(async h=>{let f=this.peers.get(h)??Zt(h),l;try{l=await this.components.peerStore.get(f)}catch(g){if(g.name!=="NotFoundError")throw g}return{peerID:f.toMultihash().bytes,signedPeerRecord:l?.peerRecordEnvelope}}));return{topicID:t,peers:c,backoff:o}}runHeartbeat=()=>{let e=this.metrics?.heartbeatDuration.startTimer();this.heartbeat().catch(t=>{this.log("Error running heartbeat",t)}).finally(()=>{if(e?.(),this.status.code===Ue.started){clearTimeout(this.status.heartbeatTimeout);let t=this.opts.heartbeatInterval-(Date.now()-this.status.hearbeatStartMs)%this.opts.heartbeatInterval;t<this.opts.heartbeatInterval*.25&&(t+=this.opts.heartbeatInterval,this.metrics?.heartbeatSkipped.inc()),this.status.heartbeatTimeout=setTimeout(this.runHeartbeat,t)}})};async heartbeat(){let{D:e,Dlo:t,Dhi:r,Dscore:n,Dout:i,fanoutTTL:o}=this.opts;this.heartbeatTicks++;let a=new Map,c=d=>{let m=a.get(d);return m===void 0&&(m=this.score.score(d),a.set(d,m)),m},h=new Map,f=new Map,l=new Map;this.clearBackoff(),this.peerhave.clear(),this.metrics?.cacheSize.set({cache:"iasked"},this.iasked.size),this.iasked.clear(),this.applyIwantPenalties(),this.idontwantCounts.clear();for(let d of this.idontwants.values())for(let[m,p]of d)this.heartbeatTicks-p>=this.opts.mcacheLength&&d.delete(m);this.heartbeatTicks%this.opts.directConnectTicks===0&&await this.directConnect(),this.fastMsgIdCache?.prune(),this.seenCache.prune(),this.gossipTracer.prune(),this.publishedMessageIds.prune();let g=new Map;this.mesh.forEach((d,m)=>{let p=this.topics.get(m),b=new Set,y=new Set;if(g.set(m,y),p!=null){let x=tt(Array.from(p)),B=this.backoff.get(m);for(let E of x){let _=this.streamsOutbound.get(E);if(_!=null&&this.multicodecs.includes(_.protocol)&&!d.has(E)&&!this.direct.has(E)){let k=c(E);B?.has(E)!==!0&&k>=0&&b.add(E),k>=this.opts.scoreThresholds.gossipThreshold&&y.add(E)}}}let w=(x,B)=>{this.log("HEARTBEAT: Remove mesh link to %s in %s",x,m),this.addBackoff(x,m),d.delete(x),c(x)>=this.opts.scoreThresholds.gossipThreshold&&y.add(x),this.metrics?.onRemoveFromMesh(m,B,1);let E=f.get(x);E==null?f.set(x,[m]):E.push(m)},I=(x,B)=>{this.log("HEARTBEAT: Add mesh link to %s in %s",x,m),this.score.graft(x,m),d.add(x),y.delete(x),this.metrics?.onAddToMesh(m,B,1);let E=h.get(x);E==null?h.set(x,[m]):E.push(m)};if(d.forEach(x=>{let B=c(x);B<0&&(this.log("HEARTBEAT: Prune peer %s with negative score: score=%d, topic=%s",x,B,m),w(x,$e.BadScore),l.set(x,!0))}),d.size<t){let x=e-d.size;il(b,x).forEach(E=>{I(E,ke.NotEnough)})}if(d.size>r){let x=Array.from(d);x.sort((E,_)=>c(_)-c(E)),x=x.slice(0,n).concat(tt(x.slice(n)));let B=0;if(x.slice(0,e).forEach(E=>{(this.outbound.get(E)??!1)&&B++}),B<i){let E=k=>{let z=x[k];for(let L=k;L>0;L--)x[L]=x[L-1];x[0]=z};if(B>0){let k=B;for(let z=1;z<e&&k>0;z++)(this.outbound.get(x[z])??!1)&&(E(z),k--)}let _=e-B;for(let k=e;k<x.length&&_>0;k++)(this.outbound.get(x[k])??!1)&&(E(k),_--)}x.slice(e).forEach(E=>{w(E,$e.Excess)})}if(d.size>=t){let x=0;if(d.forEach(B=>{(this.outbound.get(B)??!1)&&x++}),x<i){let B=i-x;Dn(b,B,_=>this.outbound.get(_)===!0).forEach(_=>{I(_,ke.Outbound)})}}if(this.heartbeatTicks%this.opts.opportunisticGraftTicks===0&&d.size>1){let x=Array.from(d).sort((_,k)=>c(_)-c(k)),B=Math.floor(d.size/2),E=c(x[B]);if(E<this.opts.scoreThresholds.opportunisticGraftThreshold){let _=this.opts.opportunisticGraftPeers,k=Dn(b,_,z=>c(z)>E);for(let z of k)this.log("HEARTBEAT: Opportunistically graft peer %s on topic %s",z,m),I(z,ke.Opportunistic)}}});let u=Date.now();this.fanoutLastpub.forEach((d,m)=>{d+o<u&&(this.fanout.delete(m),this.fanoutLastpub.delete(m))}),this.fanout.forEach((d,m)=>{let p=this.topics.get(m);d.forEach(I=>{(!(p?.has(I)??!1)||c(I)<this.opts.scoreThresholds.publishThreshold)&&d.delete(I)});let b=this.topics.get(m),y=[],w=new Set;if(g.set(m,w),b!=null){let I=tt(Array.from(b));for(let x of I){let B=this.streamsOutbound.get(x);if(B!=null&&this.multicodecs.includes(B.protocol)&&!d.has(x)&&!this.direct.has(x)){let E=c(x);E>=this.opts.scoreThresholds.publishThreshold&&y.push(x),E>=this.opts.scoreThresholds.gossipThreshold&&w.add(x)}}}if(d.size<e){let I=e-d.size;y.slice(0,I).forEach(x=>{d.add(x),w?.delete(x)})}}),this.emitGossip(g),await this.sendGraftPrune(h,f,l),this.flush(),this.mcache.shift(),this.dispatchEvent(new CustomEvent("gossipsub:heartbeat"))}getRandomGossipPeers(e,t,r=()=>!0){let n=this.topics.get(e);if(n==null)return new Set;let i=[];return n.forEach(o=>{let a=this.streamsOutbound.get(o);a!=null&&this.multicodecs.includes(a.protocol)&&r(o)&&i.push(o)}),i=tt(i),t>0&&i.length>t&&(i=i.slice(0,t)),new Set(i)}onScrapeMetrics(e){e.mcacheSize.set(this.mcache.size),e.mcacheNotValidatedCount.set(this.mcache.notValidatedCount),e.cacheSize.set({cache:"direct"},this.direct.size),e.cacheSize.set({cache:"seenCache"},this.seenCache.size),e.cacheSize.set({cache:"fastMsgIdCache"},this.fastMsgIdCache?.size??0),e.cacheSize.set({cache:"publishedMessageIds"},this.publishedMessageIds.size),e.cacheSize.set({cache:"mcache"},this.mcache.size),e.cacheSize.set({cache:"score"},this.score.size),e.cacheSize.set({cache:"gossipTracer.promises"},this.gossipTracer.size),e.cacheSize.set({cache:"gossipTracer.requests"},this.gossipTracer.requestMsByMsgSize),e.cacheSize.set({cache:"topics"},this.topics.size),e.cacheSize.set({cache:"subscriptions"},this.subscriptions.size),e.cacheSize.set({cache:"mesh"},this.mesh.size),e.cacheSize.set({cache:"fanout"},this.fanout.size),e.cacheSize.set({cache:"peers"},this.peers.size),e.cacheSize.set({cache:"streamsOutbound"},this.streamsOutbound.size),e.cacheSize.set({cache:"streamsInbound"},this.streamsInbound.size),e.cacheSize.set({cache:"acceptFromWhitelist"},this.acceptFromWhitelist.size),e.cacheSize.set({cache:"gossip"},this.gossip.size),e.cacheSize.set({cache:"control"},this.control.size),e.cacheSize.set({cache:"peerhave"},this.peerhave.size),e.cacheSize.set({cache:"outbound"},this.outbound.size);let t=0,r=Date.now();e.connectedPeersBackoffSec.reset();for(let c of this.backoff.values()){t+=c.size;for(let[h,f]of c.entries())this.peers.has(h)&&e.connectedPeersBackoffSec.observe(Math.max(0,f-r)/1e3)}e.cacheSize.set({cache:"backoff"},t);let n=0;for(let c of this.idontwants.values())n+=c.size;e.cacheSize.set({cache:"idontwants"},n);for(let[c,h]of this.topics)e.topicPeersCount.set({topicStr:c},h.size);for(let[c,h]of this.mesh)e.meshPeerCounts.set({topicStr:c},h.size);let i=[],o=new Map;e.behaviourPenalty.reset();for(let c of this.peers.keys()){let h=this.score.score(c);i.push(h),o.set(c,h),e.behaviourPenalty.observe(this.score.peerStats.get(c)?.behaviourPenalty??0)}e.registerScores(i,this.opts.scoreThresholds),e.registerScorePerMesh(this.mesh,o);let a=ul(this.peers.keys(),this.score.peerStats,this.score.params,this.score.peerIPs,e.topicStrToLabel);e.registerScoreWeights(a)}tagMeshPeer=e=>{let{peerId:t,topic:r}=e.detail;this.components.peerStore.merge(this.peers.get(t)??Zt(t),{tags:{[r]:{value:100}}}).catch(n=>{this.log.error("Error tagging peer %s with topic %s",t,r,n)})};untagMeshPeer=e=>{let{peerId:t,topic:r}=e.detail;this.components.peerStore.merge(this.peers.get(t)??Zt(t),{tags:{[r]:void 0}}).catch(n=>{this.log.error("Error untagging peer %s with topic %s",t,r,n)})}};function Od(s={}){return e=>new On(e,s)}return Ol(zd);})();
/*! Bundled license information:

pvtsutils/build/index.js:
  (*!
   * MIT License
   * 
   * Copyright (c) 2017-2022 Peculiar Ventures, LLC
   * 
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   * 
   * The above copyright notice and this permission notice shall be included in all
   * copies or substantial portions of the Software.
   * 
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
   * SOFTWARE.
   * 
   *)

@noble/hashes/esm/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/esm/abstract/utils.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/esm/abstract/modular.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/esm/abstract/curve.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/esm/abstract/edwards.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/esm/ed25519.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

pvutils/build/utils.es.js:
  (*!
   Copyright (c) Peculiar Ventures, LLC
  *)

asn1js/build/index.es.js:
  (*!
   * Copyright (c) 2014, GMO GlobalSign
   * Copyright (c) 2015-2022, Peculiar Ventures
   * All rights reserved.
   * 
   * Author 2014-2019, Yury Strozhevsky
   * 
   * Redistribution and use in source and binary forms, with or without modification,
   * are permitted provided that the following conditions are met:
   * 
   * * Redistributions of source code must retain the above copyright notice, this
   *   list of conditions and the following disclaimer.
   * 
   * * Redistributions in binary form must reproduce the above copyright notice, this
   *   list of conditions and the following disclaimer in the documentation and/or
   *   other materials provided with the distribution.
   * 
   * * Neither the name of the copyright holder nor the names of its
   *   contributors may be used to endorse or promote products derived from
   *   this software without specific prior written permission.
   * 
   * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
   * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
   * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
   * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
   * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
   * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
   * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
   * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
   * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
   * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
   * 
   *)

@noble/curves/esm/abstract/weierstrass.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/esm/_shortw_utils.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/esm/secp256k1.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)
*/
return ChainsafeLibp2PGossipsub}));
