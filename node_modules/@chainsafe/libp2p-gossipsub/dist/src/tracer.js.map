{"version": 3, "file": "tracer.js", "sourceRoot": "", "sources": ["../../src/tracer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAoD,YAAY,EAAE,MAAM,YAAY,CAAA;AAG3F;;;;;;;GAOG;AACH,MAAM,OAAO,WAAW;IAcH;IACA;IACA;IAfnB;;;OAGG;IACc,QAAQ,GAAG,IAAI,GAAG,EAAoC,CAAA;IACvE;;;OAGG;IACc,cAAc,GAAG,IAAI,GAAG,EAAoB,CAAA;IAC5C,oBAAoB,CAAQ;IAE7C,YACmB,wBAAgC,EAChC,YAA0B,EAC1B,OAAuB;QAFvB,6BAAwB,GAAxB,wBAAwB,CAAQ;QAChC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,YAAO,GAAP,OAAO,CAAgB;QAExC,IAAI,CAAC,oBAAoB,GAAG,EAAE,GAAG,wBAAwB,CAAA;IAC3D,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAA;IAC3B,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,UAAU,CAAE,IAAe,EAAE,MAAoB;QAC/C,oCAAoC;QACpC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;QACpD,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,CAAA;QACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QAEzC,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC9C,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,YAAY,GAAG,IAAI,GAAG,EAAE,CAAA;YACxB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;QAC3C,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEtB,sFAAsF;QACtF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAA;YAE3D,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBACvC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,iBAAiB;QACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAqB,CAAA;QAE3C,IAAI,cAAc,GAAG,CAAC,CAAA;QAEtB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;YAC5C,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjC,8BAA8B;gBAC9B,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;oBACjB,kBAAkB;oBAClB,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;oBACvC,+BAA+B;oBAC/B,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;oBACtB,cAAc;oBACd,cAAc,EAAE,CAAA;gBAClB,CAAC;YACH,CAAC,CAAC,CAAA;YACF,sCAAsC;YACtC,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YAC7B,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAEpD,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;OAEG;IACH,cAAc,CAAE,QAAkB,EAAE,WAAW,GAAG,KAAK;QACrD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;QAE3B,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAEhD,wCAAwC;QACxC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAE9B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBACxC,IAAI,WAAW;oBAAE,IAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBACtE,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;YAC/D,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,aAAa,CAAE,QAAkB,EAAE,MAAoB;QACrD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;QAE3B,4FAA4F;QAC5F,iDAAiD;QACjD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,YAAY,CAAC,KAAK;gBACrB,OAAM;YACR;gBACE,MAAK;QACT,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAChC,CAAC;IAED,KAAK;QACH,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;IACvB,CAAC;IAED,KAAK;QACH,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAA;QACpD,IAAI,KAAK,GAAG,CAAC,CAAA;QAEb,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YACnD,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC;gBACd,gEAAgE;gBAChE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;gBAC7B,KAAK,EAAE,CAAA;YACT,CAAC;iBAAM,CAAC;gBACN,6BAA6B;gBAC7B,0BAA0B;gBAC1B,MAAK;YACP,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IAC7C,CAAC;IAEO,YAAY,CAAE,QAAkB;QACtC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YACnD,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC,CAAA;gBAC9E,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YACtC,CAAC;QACH,CAAC;IACH,CAAC;CACF"}