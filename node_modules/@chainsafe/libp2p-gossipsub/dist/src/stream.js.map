{"version": 3, "file": "stream.js", "sourceRoot": "", "sources": ["../../src/stream.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AACnD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAA;AAC9B,OAAO,EAAE,QAAQ,EAAiB,MAAM,aAAa,CAAA;AAcrD,MAAM,OAAO,cAAc;IAKK;IAJb,QAAQ,CAAuC;IAC/C,eAAe,CAAiB;IAChC,aAAa,CAAQ;IAEtC,YAA8B,SAAiB,EAAE,WAA+B,EAAE,IAAwB;QAA5E,cAAS,GAAT,SAAS,CAAQ;QAC7C,IAAI,CAAC,QAAQ,GAAG,QAAQ,EAAE,CAAA;QAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAA;QAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAA;QAEnD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACzD,SAAS,CAAC,KAAK,EAAE;iBACd,KAAK,CAAC,GAAG,CAAC,EAAE;gBACX,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACtB,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QAEF,IAAI,CACF,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,CACf,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;IACtB,CAAC;IAED,IAAI,QAAQ;QACV,uHAAuH;QACvH,oEAAoE;QACpE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAS,CAAA;IACjC,CAAC;IAED,IAAI,CAAE,IAAgB;QACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtD,MAAM,KAAK,CAAC,sCAAsC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;QACzE,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;IACzC,CAAC;IAED;;OAEG;IACH,YAAY,CAAE,IAAoB;QAChC,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtD,MAAM,KAAK,CAAC,sCAAsC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;QACzE,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;QAC5B,0DAA0D;QAC1D,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAA;IAC9B,CAAC;CACF;AAED,MAAM,OAAO,aAAa;IACR,MAAM,CAA+B;IAEpC,SAAS,CAAQ;IACjB,eAAe,CAAiB;IAEjD,YAAa,SAAiB,EAAE,OAA0B,EAAE;QAC1D,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAA;QAE5C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACzD,SAAS,CAAC,KAAK,EAAE;iBACd,KAAK,CAAC,GAAG,CAAC,EAAE;gBACX,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACtB,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,GAAG,IAAI,CAChB,IAAI,CAAC,SAAS,EACd,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CACjC,CAAA;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;IAC9B,CAAC;CACF"}