{"version": 3, "file": "compute-score.js", "sourceRoot": "", "sources": ["../../../src/score/compute-score.ts"], "names": [], "mappings": "AAGA,MAAM,UAAU,YAAY,CAC1B,IAAY,EACZ,MAAiB,EACjB,MAAuB,EACvB,OAAiC;IAEjC,IAAI,KAAK,GAAG,CAAC,CAAA;IAEb,eAAe;IACf,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE;QACxD,uBAAuB;QACvB,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACxC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,gCAAgC;YAChC,OAAM;QACR,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,CAAA;QAElB,mBAAmB;QACnB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,EAAE,GAAG,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,iBAAiB,CAAA;YACxD,IAAI,EAAE,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;gBACnC,EAAE,GAAG,WAAW,CAAC,aAAa,CAAA;YAChC,CAAC;YACD,UAAU,IAAI,EAAE,GAAG,WAAW,CAAC,gBAAgB,CAAA;QACjD,CAAC;QAED,+BAA+B;QAC/B,IAAI,EAAE,GAAG,MAAM,CAAC,sBAAsB,CAAA;QACtC,IAAI,EAAE,GAAG,WAAW,CAAC,yBAAyB,EAAE,CAAC;YAC/C,EAAE,GAAG,WAAW,CAAC,yBAAyB,CAAA;QAC5C,CAAC;QACD,UAAU,IAAI,EAAE,GAAG,WAAW,CAAC,4BAA4B,CAAA;QAE3D,8BAA8B;QAC9B,IACE,MAAM,CAAC,2BAA2B;YAClC,MAAM,CAAC,qBAAqB,GAAG,WAAW,CAAC,8BAA8B,EACzE,CAAC;YACD,MAAM,OAAO,GAAG,WAAW,CAAC,8BAA8B,GAAG,MAAM,CAAC,qBAAqB,CAAA;YACzF,MAAM,EAAE,GAAG,OAAO,GAAG,OAAO,CAAA;YAC5B,UAAU,IAAI,EAAE,GAAG,WAAW,CAAC,2BAA2B,CAAA;QAC5D,CAAC;QAED,OAAO;QACP,+FAA+F;QAC/F,MAAM,GAAG,GAAG,MAAM,CAAC,kBAAkB,CAAA;QACrC,UAAU,IAAI,GAAG,GAAG,WAAW,CAAC,wBAAwB,CAAA;QAExD,uBAAuB;QACvB,8FAA8F;QAC9F,MAAM,EAAE,GAAG,MAAM,CAAC,wBAAwB,GAAG,MAAM,CAAC,wBAAwB,CAAA;QAC5E,UAAU,IAAI,EAAE,GAAG,WAAW,CAAC,8BAA8B,CAAA;QAE7D,yCAAyC;QACzC,KAAK,IAAI,UAAU,GAAG,WAAW,CAAC,WAAW,CAAA;IAC/C,CAAC,CAAC,CAAA;IAEF,oCAAoC;IACpC,IAAI,MAAM,CAAC,aAAa,GAAG,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAC7D,KAAK,GAAG,MAAM,CAAC,aAAa,CAAA;IAC9B,CAAC;IAED,iCAAiC;IACjC,MAAM,EAAE,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;IACxC,KAAK,IAAI,EAAE,GAAG,MAAM,CAAC,iBAAiB,CAAA;IAEtC,2BAA2B;IAC3B,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;QAC7B,IAAI,MAAM,CAAC,2BAA2B,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YAC/C,OAAM;QACR,CAAC;QAED,+CAA+C;QAC/C,8FAA8F;QAC9F,qFAAqF;QACrF,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACjC,MAAM,YAAY,GAAG,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7D,IAAI,YAAY,GAAG,MAAM,CAAC,2BAA2B,EAAE,CAAC;YACtD,MAAM,OAAO,GAAG,YAAY,GAAG,MAAM,CAAC,2BAA2B,CAAA;YACjE,MAAM,EAAE,GAAG,OAAO,GAAG,OAAO,CAAA;YAC5B,KAAK,IAAI,EAAE,GAAG,MAAM,CAAC,wBAAwB,CAAA;QAC/C,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,kCAAkC;IAClC,IAAI,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,yBAAyB,EAAE,CAAC;QAC/D,MAAM,MAAM,GAAG,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,yBAAyB,CAAA;QACzE,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM,CAAA;QAC1B,KAAK,IAAI,EAAE,GAAG,MAAM,CAAC,sBAAsB,CAAA;IAC7C,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC"}