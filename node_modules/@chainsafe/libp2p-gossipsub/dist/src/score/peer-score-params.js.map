{"version": 3, "file": "peer-score-params.js", "sourceRoot": "", "sources": ["../../../src/score/peer-score-params.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,2BAA2B,EAAE,MAAM,cAAc,CAAA;AA0I1D,MAAM,CAAC,MAAM,sBAAsB,GAAoB;IACrD,MAAM,EAAE,EAAE;IACV,aAAa,EAAE,IAAI;IACnB,gBAAgB,EAAE,GAAG,EAAE,CAAC,GAAG;IAC3B,iBAAiB,EAAE,IAAI;IACvB,wBAAwB,EAAE,CAAC,GAAG;IAC9B,2BAA2B,EAAE,IAAI;IACjC,2BAA2B,EAAE,IAAI,GAAG,EAAE;IACtC,sBAAsB,EAAE,CAAC,IAAI;IAC7B,yBAAyB,EAAE,GAAG;IAC9B,qBAAqB,EAAE,GAAG;IAC1B,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,GAAG;IAChB,WAAW,EAAE,IAAI,GAAG,IAAI;CACzB,CAAA;AAED,MAAM,CAAC,MAAM,uBAAuB,GAAqB;IACvD,WAAW,EAAE,GAAG;IAChB,gBAAgB,EAAE,CAAC;IACnB,iBAAiB,EAAE,CAAC;IACpB,aAAa,EAAE,IAAI;IAEnB,4BAA4B,EAAE,CAAC;IAC/B,2BAA2B,EAAE,GAAG;IAChC,yBAAyB,EAAE,IAAI;IAE/B,2BAA2B,EAAE,CAAC,CAAC;IAC/B,0BAA0B,EAAE,GAAG;IAC/B,wBAAwB,EAAE,GAAG;IAC7B,8BAA8B,EAAE,EAAE;IAClC,2BAA2B,EAAE,EAAE;IAC/B,+BAA+B,EAAE,IAAI;IAErC,wBAAwB,EAAE,CAAC,CAAC;IAC5B,uBAAuB,EAAE,GAAG;IAE5B,8BAA8B,EAAE,CAAC,CAAC;IAClC,6BAA6B,EAAE,GAAG;CACnC,CAAA;AAED,MAAM,UAAU,qBAAqB,CAAE,IAA8B,EAAE;IACrE,OAAO;QACL,GAAG,sBAAsB;QACzB,GAAG,CAAC;QACJ,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC;YACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAmC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,EAAE;gBACxG,MAAM,CAAC,KAAK,CAAC,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,CAAA;gBACxD,OAAO,MAAM,CAAA;YACf,CAAC,EAAE,EAAE,CAAC;YACN,CAAC,CAAC,EAAE;KACP,CAAA;AACH,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAE,IAA+B,EAAE;IACvE,OAAO;QACL,GAAG,uBAAuB;QAC1B,GAAG,CAAC;KACL,CAAA;AACH,CAAC;AAED,kCAAkC;AAClC,MAAM,UAAU,uBAAuB,CAAE,CAAkB;IACzD,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;QACvD,IAAI,CAAC;YACH,wBAAwB,CAAC,MAAM,CAAC,CAAA;QAClC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,2BAA2B,CAAC,sCAAsC,KAAK,KAAM,CAAW,CAAC,OAAO,EAAE,CAAC,CAAA;QAC/G,CAAC;IACH,CAAC;IAED,wDAAwD;IACxD,IAAI,CAAC,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,2BAA2B,CAAC,6DAA6D,CAAC,CAAA;IACtG,CAAC;IAED,+FAA+F;IAC/F,IAAI,CAAC,CAAC,gBAAgB,KAAK,IAAI,IAAI,CAAC,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;QACpE,MAAM,IAAI,2BAA2B,CAAC,6CAA6C,CAAC,CAAA;IACtF,CAAC;IAED,iCAAiC;IACjC,IAAI,CAAC,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,2BAA2B,CAAC,sEAAsE,CAAC,CAAA;IAC/G,CAAC;IACD,IAAI,CAAC,CAAC,wBAAwB,KAAK,CAAC,IAAI,CAAC,CAAC,2BAA2B,GAAG,CAAC,EAAE,CAAC;QAC1E,MAAM,IAAI,2BAA2B,CAAC,yDAAyD,CAAC,CAAA;IAClG,CAAC;IAED,8BAA8B;IAC9B,IAAI,CAAC,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;QACjC,MAAM,IAAI,2BAA2B,CAAC,oEAAoE,CAAC,CAAA;IAC7G,CAAC;IACD,IAAI,CAAC,CAAC,sBAAsB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,CAAC,qBAAqB,IAAI,CAAC,CAAC,EAAE,CAAC;QACrG,MAAM,IAAI,2BAA2B,CAAC,wDAAwD,CAAC,CAAA;IACjG,CAAC;IAED,6BAA6B;IAC7B,IAAI,CAAC,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;QAC3B,MAAM,IAAI,2BAA2B,CAAC,4CAA4C,CAAC,CAAA;IACrF,CAAC;IACD,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;QAC7C,MAAM,IAAI,2BAA2B,CAAC,8CAA8C,CAAC,CAAA;IACvF,CAAC;IAED,uFAAuF;AACzF,CAAC;AAED,sCAAsC;AACtC,MAAM,UAAU,wBAAwB,CAAE,CAAmB;IAC3D,wCAAwC;IACxC,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,IAAI,2BAA2B,CAAC,oCAAoC,CAAC,CAAA;IAC7E,CAAC;IAED,WAAW;IACX,IAAI,CAAC,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;QAC9B,MAAM,IAAI,2BAA2B,CAAC,6CAA6C,CAAC,CAAA;IACtF,CAAC;IACD,IAAI,CAAC,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,2BAA2B,CAAC,8DAA8D,CAAC,CAAA;IACvG,CAAC;IACD,IAAI,CAAC,CAAC,gBAAgB,KAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,IAAI,CAAC,EAAE,CAAC;QACzD,MAAM,IAAI,2BAA2B,CAAC,6CAA6C,CAAC,CAAA;IACtF,CAAC;IACD,IAAI,CAAC,CAAC,gBAAgB,KAAK,CAAC,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,EAAE,CAAC;QACrD,MAAM,IAAI,2BAA2B,CAAC,yCAAyC,CAAC,CAAA;IAClF,CAAC;IAED,WAAW;IACX,IAAI,CAAC,CAAC,4BAA4B,GAAG,CAAC,EAAE,CAAC;QACvC,MAAM,IAAI,2BAA2B,CAAC,2EAA2E,CAAC,CAAA;IACpH,CAAC;IACD,IACE,CAAC,CAAC,4BAA4B,KAAK,CAAC;QACpC,CAAC,CAAC,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,CAAC,2BAA2B,IAAI,CAAC,CAAC,EAC1E,CAAC;QACD,MAAM,IAAI,2BAA2B,CAAC,8DAA8D,CAAC,CAAA;IACvG,CAAC;IACD,IAAI,CAAC,CAAC,4BAA4B,KAAK,CAAC,IAAI,CAAC,CAAC,yBAAyB,IAAI,CAAC,EAAE,CAAC;QAC7E,MAAM,IAAI,2BAA2B,CAAC,qDAAqD,CAAC,CAAA;IAC9F,CAAC;IAED,WAAW;IACX,IAAI,CAAC,CAAC,2BAA2B,GAAG,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,2BAA2B,CAAC,yEAAyE,CAAC,CAAA;IAClH,CAAC;IACD,IAAI,CAAC,CAAC,2BAA2B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B,IAAI,CAAC,IAAI,CAAC,CAAC,0BAA0B,IAAI,CAAC,CAAC,EAAE,CAAC;QACpH,MAAM,IAAI,2BAA2B,CAAC,6DAA6D,CAAC,CAAA;IACtG,CAAC;IACD,IAAI,CAAC,CAAC,2BAA2B,KAAK,CAAC,IAAI,CAAC,CAAC,wBAAwB,IAAI,CAAC,EAAE,CAAC;QAC3E,MAAM,IAAI,2BAA2B,CAAC,oDAAoD,CAAC,CAAA;IAC7F,CAAC;IACD,IAAI,CAAC,CAAC,2BAA2B,KAAK,CAAC,IAAI,CAAC,CAAC,8BAA8B,IAAI,CAAC,EAAE,CAAC;QACjF,MAAM,IAAI,2BAA2B,CAAC,0DAA0D,CAAC,CAAA;IACnG,CAAC;IACD,IAAI,CAAC,CAAC,2BAA2B,GAAG,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,2BAA2B,CAAC,2DAA2D,CAAC,CAAA;IACpG,CAAC;IACD,IAAI,CAAC,CAAC,2BAA2B,KAAK,CAAC,IAAI,CAAC,CAAC,+BAA+B,GAAG,IAAI,EAAE,CAAC;QACpF,MAAM,IAAI,2BAA2B,CAAC,8DAA8D,CAAC,CAAA;IACvG,CAAC;IAED,YAAY;IACZ,IAAI,CAAC,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,2BAA2B,CAAC,sEAAsE,CAAC,CAAA;IAC/G,CAAC;IACD,IAAI,CAAC,CAAC,wBAAwB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,CAAC,uBAAuB,IAAI,CAAC,CAAC,EAAE,CAAC;QAC3G,MAAM,IAAI,2BAA2B,CAAC,0DAA0D,CAAC,CAAA;IACnG,CAAC;IAED,WAAW;IACX,IAAI,CAAC,CAAC,8BAA8B,GAAG,CAAC,EAAE,CAAC;QACzC,MAAM,IAAI,2BAA2B,CAAC,4EAA4E,CAAC,CAAA;IACrH,CAAC;IACD,IAAI,CAAC,CAAC,6BAA6B,IAAI,CAAC,IAAI,CAAC,CAAC,6BAA6B,IAAI,CAAC,EAAE,CAAC;QACjF,MAAM,IAAI,2BAA2B,CAAC,gEAAgE,CAAC,CAAA;IACzG,CAAC;AACH,CAAC"}