import { InvalidPeerScoreThresholdsError } from '../errors.js';
export const defaultPeerScoreThresholds = {
    gossipThreshold: -10,
    publishThreshold: -50,
    graylistThreshold: -80,
    acceptPXThreshold: 10,
    opportunisticGraftThreshold: 20
};
export function createPeerScoreThresholds(p = {}) {
    return {
        ...defaultPeerScoreThresholds,
        ...p
    };
}
export function validatePeerScoreThresholds(p) {
    if (p.gossipThreshold > 0) {
        throw new InvalidPeerScoreThresholdsError('invalid gossip threshold; it must be <= 0');
    }
    if (p.publishThreshold > 0 || p.publishThreshold > p.gossipThreshold) {
        throw new InvalidPeerScoreThresholdsError('invalid publish threshold; it must be <= 0 and <= gossip threshold');
    }
    if (p.graylistThreshold > 0 || p.graylistThreshold > p.publishThreshold) {
        throw new InvalidPeerScoreThresholdsError('invalid graylist threshold; it must be <= 0 and <= publish threshold');
    }
    if (p.acceptPXThreshold < 0) {
        throw new InvalidPeerScoreThresholdsError('invalid accept PX threshold; it must be >= 0');
    }
    if (p.opportunisticGraftThreshold < 0) {
        throw new InvalidPeerScoreThresholdsError('invalid opportunistic grafting threshold; it must be >= 0');
    }
}
//# sourceMappingURL=peer-score-thresholds.js.map