import { StrictSign, StrictNoSign } from '@libp2p/interface';
import { type PublishConfig } from '../types.js';
import type { PeerId, PrivateKey } from '@libp2p/interface';
/**
 * Prepare a PublishConfig object from a PeerId.
 */
export declare function getPublishConfigFromPeerId(signaturePolicy: typeof StrictSign | typeof StrictNoSign, peerId: PeerId, privateKey: PrivateKey): PublishConfig;
//# sourceMappingURL=publishConfig.d.ts.map