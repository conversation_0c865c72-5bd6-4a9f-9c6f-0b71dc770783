{"version": 3, "file": "buildRawMessage.js", "sourceRoot": "", "sources": ["../../../src/utils/buildRawMessage.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAA;AAC5C,OAAO,EAAE,qBAAqB,EAAE,MAAM,qBAAqB,CAAA;AAC3D,OAAO,EAAE,UAAU,EAAE,YAAY,EAA6C,MAAM,mBAAmB,CAAA;AACvG,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAA;AACrD,OAAO,KAAK,MAAM,MAAM,4BAA4B,CAAA;AACpD,OAAO,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAC/D,OAAO,EAAE,UAAU,IAAI,oBAAoB,EAAE,MAAM,yBAAyB,CAAA;AAC5E,OAAO,EAAE,QAAQ,IAAI,kBAAkB,EAAE,MAAM,uBAAuB,CAAA;AACtE,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAA;AACvC,OAAO,EAAsB,iBAAiB,EAAiB,aAAa,EAAE,MAAM,aAAa,CAAA;AAEjG,MAAM,CAAC,MAAM,UAAU,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAA;AAOhE,MAAM,CAAC,KAAK,UAAU,eAAe,CACnC,aAA4B,EAC5B,KAAe,EACf,YAAwB,EACxB,eAA2B;IAE3B,QAAQ,aAAa,CAAC,IAAI,EAAE,CAAC;QAC3B,KAAK,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;YAC/B,MAAM,MAAM,GAAgB;gBAC1B,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK;gBAC9C,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;gBACrB,KAAK;gBACL,SAAS,EAAE,SAAS,EAAE,sCAAsC;gBAC5D,GAAG,EAAE,SAAS,CAAC,gCAAgC;aAChD,CAAA;YAED,+DAA+D;YAC/D,qEAAqE;YACrE,MAAM,KAAK,GAAG,gBAAgB,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAExE,MAAM,CAAC,SAAS,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7D,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAA;YAE9B,MAAM,GAAG,GAAY;gBACnB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,aAAa,CAAC,MAAM;gBAC1B,IAAI,EAAE,YAAY;gBAClB,cAAc,EAAE,MAAM,CAAC,KAAK,kBAAkB,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC9F,KAAK;gBACL,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,GAAG,EAAE,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC;aACvC,CAAA;YACD,OAAO;gBACL,GAAG,EAAE,MAAM;gBACX,GAAG;aACJ,CAAA;QACH,CAAC;QAED,KAAK,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;YACjC,OAAO;gBACL,GAAG,EAAE;oBACH,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,SAAS;oBAChB,KAAK;oBACL,SAAS,EAAE,SAAS;oBACpB,GAAG,EAAE,SAAS;iBACf;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,YAAY;oBAClB,KAAK;iBACN;aACF,CAAA;QACH,CAAC;QAED;YACE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAA;IAClC,CAAC;AACH,CAAC;AAID,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,eAAwD,EACxD,GAAgB;IAEhB,6BAA6B;IAC7B,2CAA2C;IAE3C,QAAQ,eAAe,EAAE,CAAC;QACxB,KAAK,YAAY;YACf,IAAI,GAAG,CAAC,SAAS,IAAI,IAAI;gBAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,gBAAgB,EAAE,CAAA;YACzF,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI;gBAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,YAAY,EAAE,CAAA;YACjF,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI;gBAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,EAAE,CAAA;YAE9E,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;QAE9G,KAAK,UAAU,CAAC,CAAC,CAAC;YAChB,eAAe;YACf,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI;gBAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,YAAY,EAAE,CAAA;YACjF,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,YAAY,EAAE,CAAA;YAC5D,CAAC;YAED,IAAI,GAAG,CAAC,SAAS,IAAI,IAAI;gBAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,gBAAgB,EAAE,CAAA;YACzF,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI;gBAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,aAAa,EAAE,CAAA;YAEjF,IAAI,UAAkB,CAAA;YACtB,IAAI,CAAC;gBACH,yBAAyB;gBACzB,UAAU,GAAG,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;YAC3D,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,aAAa,EAAE,CAAA;YAC7D,CAAC;YAED,uBAAuB;YACvB,+BAA+B;YAC/B,oBAAoB;YACpB,+BAA+B;YAC/B,mCAAmC;YACnC,eAAe;YAEf,IAAI,SAAoB,CAAA;YACxB,IAAI,GAAG,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;gBACpB,SAAS,GAAG,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;gBAC1C,gDAAgD;gBAChD,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClF,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,aAAa,EAAE,CAAA;gBAC7D,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,UAAU,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;oBACjC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,aAAa,EAAE,CAAA;gBAC7D,CAAC;gBACD,SAAS,GAAG,UAAU,CAAC,SAAS,CAAA;YAClC,CAAC;YAED,MAAM,aAAa,GAAgB;gBACjC,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,KAAK,EAAE,GAAG,CAAC,KAAK;gBAChB,KAAK,EAAE,GAAG,CAAC,KAAK;gBAChB,SAAS,EAAE,SAAS,EAAE,sCAAsC;gBAC5D,GAAG,EAAE,SAAS,CAAC,gCAAgC;aAChD,CAAA;YAED,+DAA+D;YAC/D,qEAAqE;YACrE,MAAM,KAAK,GAAG,gBAAgB,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YAE/E,IAAI,CAAC,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBACpD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,gBAAgB,EAAE,CAAA;YAChE,CAAC;YAED,OAAO;gBACL,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC;oBACnC,cAAc,EAAE,MAAM,CAAC,KAAK,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;oBACtE,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,SAAS,EAAE,GAAG,CAAC,SAAS;oBACxB,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;iBAClE;aACF,CAAA;QACH,CAAC;QAED;YACE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAA;IAClC,CAAC;AACH,CAAC"}