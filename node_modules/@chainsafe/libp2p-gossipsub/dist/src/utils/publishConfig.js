import { publicKeyToProtobuf } from '@libp2p/crypto/keys';
import { StrictSign, StrictNoSign } from '@libp2p/interface';
import { PublishConfigType } from '../types.js';
/**
 * Prepare a PublishConfig object from a PeerId.
 */
export function getPublishConfigFromPeerId(signaturePolicy, peerId, privateKey) {
    switch (signaturePolicy) {
        case StrictSign: {
            return {
                type: PublishConfigType.Signing,
                author: peerId,
                key: publicKeyToProtobuf(privateKey.publicKey),
                privateKey
            };
        }
        case StrictNoSign:
            return {
                type: PublishConfigType.Anonymous
            };
        default:
            throw new Error(`Unknown signature policy "${signaturePolicy}"`);
    }
}
//# sourceMappingURL=publishConfig.js.map