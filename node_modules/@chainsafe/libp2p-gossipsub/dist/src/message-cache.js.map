{"version": 3, "file": "message-cache.js", "sourceRoot": "", "sources": ["../../src/message-cache.ts"], "names": [], "mappings": "AAyBA,MAAM,OAAO,YAAY;IAmBJ;IAlBnB,IAAI,GAAG,IAAI,GAAG,EAA+B,CAAA;IAE7C,YAAY,CAAc;IAE1B,OAAO,GAAmB,EAAE,CAAA;IAE5B,iFAAiF;IACjF,iBAAiB,GAAG,CAAC,CAAA;IAErB;;OAEG;IACH;IACE;;;;OAIG;IACc,MAAc,EAC/B,eAAuB,EACvB,YAA0B;QAFT,WAAM,GAAN,MAAM,CAAQ;QAI/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;QACtB,CAAC;IACH,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IAED;;;OAGG;IACH,GAAG,CAAE,SAAoB,EAAE,GAAgB,EAAE,SAAS,GAAG,KAAK;QAC5D,MAAM,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAA;QAC9B,4CAA4C;QAC5C,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;YACtB,OAAO,EAAE,GAAG;YACZ,SAAS;YACT,gBAAgB,EAAE,IAAI,GAAG,EAAE;YAC3B,WAAW,EAAE,IAAI,GAAG,EAAE;SACvB,CAAC,CAAA;QAEF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,SAAS,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;QAExD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC1B,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,gBAAgB,CAAE,KAAe,EAAE,aAAwB;QACzD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QAElC,IACE,CAAC,KAAK,IAAI,IAAI,CAAC;YACf,qFAAqF;YACrF,uDAAuD;YACvD,CAAC,KAAK,CAAC,SAAS,EAChB,CAAC;YACD,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,GAAG,CAAE,KAAiB;QACpB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAA;IACzD,CAAC;IAED;;;OAGG;IACH,iBAAiB,CAAE,QAAgB,EAAE,CAAS;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACnC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAChB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QAC/C,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QAE7B,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,CAAA;IACpC,CAAC;IAED;;OAEG;IACH,YAAY,CAAE,MAAmB;QAC/B,MAAM,aAAa,GAAG,IAAI,GAAG,EAAwB,CAAA;QACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAChC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBACzC,IAAI,CAAC,GAAG,EAAE,SAAS,IAAI,KAAK,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzD,IAAI,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;oBAC3C,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;wBACnB,MAAM,GAAG,EAAE,CAAA;wBACX,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;oBACxC,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;gBAC1B,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,aAAa,CAAA;IACtB,CAAC;IAED;;;;OAIG;IACH,QAAQ,CAAE,KAAe;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QAClC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC1B,CAAC;QAED,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,KAAK,CAAA;QAC3C,KAAK,CAAC,SAAS,GAAG,IAAI,CAAA;QACtB,sFAAsF;QACtF,+CAA+C;QAC/C,KAAK,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;QAClC,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,KAAK;QACH,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAC9D,gBAAgB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACtC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;YAChD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;gBACrC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;oBACrB,IAAI,CAAC,iBAAiB,EAAE,CAAA;gBAC1B,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAA;QAClB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC1B,CAAC;IAED,MAAM,CAAE,KAAe;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QAClC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,0EAA0E;QAC1E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACvB,OAAO,KAAK,CAAA;IACd,CAAC;CACF"}