{"version": 3, "file": "rpc.d.ts", "sourceRoot": "", "sources": ["../../../src/message/rpc.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,KAAK,KAAK,EAAiB,KAAK,aAAa,EAA0C,MAAM,iBAAiB,CAAA;AACvH,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAA;AAEpD,MAAM,WAAW,GAAG;IAClB,aAAa,EAAE,GAAG,CAAC,OAAO,EAAE,CAAA;IAC5B,QAAQ,EAAE,GAAG,CAAC,OAAO,EAAE,CAAA;IACvB,OAAO,CAAC,EAAE,GAAG,CAAC,cAAc,CAAA;CAC7B;AAED,yBAAiB,GAAG,CAAC;IACnB,UAAiB,OAAO;QACtB,SAAS,CAAC,EAAE,OAAO,CAAA;QACnB,KAAK,CAAC,EAAE,MAAM,CAAA;KACf;IAED,UAAiB,OAAO,CAAC;QAGhB,MAAM,KAAK,QAAO,MAAM,OAAO,CAiDrC,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,OAAO,CAAC,KAAG,UAE9C,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,OAAO,CAAC,KAAG,OAExF,CAAA;KACF;IAED,UAAiB,OAAO;QACtB,IAAI,CAAC,EAAE,UAAU,CAAA;QACjB,IAAI,CAAC,EAAE,UAAU,CAAA;QACjB,KAAK,CAAC,EAAE,UAAU,CAAA;QAClB,KAAK,EAAE,MAAM,CAAA;QACb,SAAS,CAAC,EAAE,UAAU,CAAA;QACtB,GAAG,CAAC,EAAE,UAAU,CAAA;KACjB;IAED,UAAiB,OAAO,CAAC;QAGhB,MAAM,KAAK,QAAO,MAAM,OAAO,CAuFrC,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,OAAO,CAAC,KAAG,UAE9C,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,OAAO,CAAC,KAAG,OAExF,CAAA;KACF;IAED,UAAiB,cAAc;QAC7B,KAAK,EAAE,GAAG,CAAC,YAAY,EAAE,CAAA;QACzB,KAAK,EAAE,GAAG,CAAC,YAAY,EAAE,CAAA;QACzB,KAAK,EAAE,GAAG,CAAC,YAAY,EAAE,CAAA;QACzB,KAAK,EAAE,GAAG,CAAC,YAAY,EAAE,CAAA;QACzB,SAAS,EAAE,GAAG,CAAC,gBAAgB,EAAE,CAAA;KAClC;IAED,UAAiB,cAAc,CAAC;QAGvB,MAAM,KAAK,QAAO,MAAM,cAAc,CA0H5C,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,cAAc,CAAC,KAAG,UAErD,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,cAAc,CAAC,KAAG,cAE/F,CAAA;KACF;IAED,UAAiB,YAAY;QAC3B,OAAO,CAAC,EAAE,MAAM,CAAA;QAChB,UAAU,EAAE,UAAU,EAAE,CAAA;KACzB;IAED,UAAiB,YAAY,CAAC;QAGrB,MAAM,KAAK,QAAO,MAAM,YAAY,CAyD1C,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,YAAY,CAAC,KAAG,UAEnD,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,YAAY,CAAC,KAAG,YAE7F,CAAA;KACF;IAED,UAAiB,YAAY;QAC3B,UAAU,EAAE,UAAU,EAAE,CAAA;KACzB;IAED,UAAiB,YAAY,CAAC;QAGrB,MAAM,KAAK,QAAO,MAAM,YAAY,CAgD1C,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,YAAY,CAAC,KAAG,UAEnD,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,YAAY,CAAC,KAAG,YAE7F,CAAA;KACF;IAED,UAAiB,YAAY;QAC3B,OAAO,CAAC,EAAE,MAAM,CAAA;KACjB;IAED,UAAiB,YAAY,CAAC;QAGrB,MAAM,KAAK,QAAO,MAAM,YAAY,CAwC1C,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,YAAY,CAAC,KAAG,UAEnD,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,YAAY,CAAC,KAAG,YAE7F,CAAA;KACF;IAED,UAAiB,YAAY;QAC3B,OAAO,CAAC,EAAE,MAAM,CAAA;QAChB,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAA;QACrB,OAAO,CAAC,EAAE,MAAM,CAAA;KACjB;IAED,UAAiB,YAAY,CAAC;QAGrB,MAAM,KAAK,QAAO,MAAM,YAAY,CAoE1C,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,YAAY,CAAC,KAAG,UAEnD,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,YAAY,CAAC,KAAG,YAE7F,CAAA;KACF;IAED,UAAiB,QAAQ;QACvB,MAAM,CAAC,EAAE,UAAU,CAAA;QACnB,gBAAgB,CAAC,EAAE,UAAU,CAAA;KAC9B;IAED,UAAiB,QAAQ,CAAC;QAGjB,MAAM,KAAK,QAAO,MAAM,QAAQ,CAiDtC,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,QAAQ,CAAC,KAAG,UAE/C,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,QAAQ,CAAC,KAAG,QAEzF,CAAA;KACF;IAED,UAAiB,gBAAgB;QAC/B,UAAU,EAAE,UAAU,EAAE,CAAA;KACzB;IAED,UAAiB,gBAAgB,CAAC;QAGzB,MAAM,KAAK,QAAO,MAAM,gBAAgB,CAgD9C,CAAA;QAEM,MAAM,MAAM,QAAS,QAAQ,gBAAgB,CAAC,KAAG,UAEvD,CAAA;QAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,gBAAgB,CAAC,KAAG,gBAEjG,CAAA;KACF;IAIM,MAAM,KAAK,QAAO,MAAM,GAAG,CA+EjC,CAAA;IAEM,MAAM,MAAM,QAAS,QAAQ,GAAG,CAAC,KAAG,UAE1C,CAAA;IAEM,MAAM,MAAM,QAAS,UAAU,GAAG,cAAc,SAAS,cAAc,GAAG,CAAC,KAAG,GAEpF,CAAA;CACF"}