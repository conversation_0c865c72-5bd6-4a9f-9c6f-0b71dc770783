{"name": "@chainsafe/as-chacha20poly1305", "version": "0.1.0", "description": "An AssemblyScript implementation of chacha20poly1305", "author": "ChainSafe Systems", "license": "Apache-2.0", "bugs": {"url": "https://github.com/ChainSafe/as-chacha20poly1305/issues"}, "homepage": "https://github.com/ChainSafe/as-chacha20poly1305#readme", "repository": {"type": "git", "url": "git+https://github.com/chainsafe/as-chacha20poly1305.git"}, "main": "lib/src/index.js", "types": "lib/src/index.d.ts", "files": ["lib", "build"], "scripts": {"prebuild": "rm -rf ./dist && node -r ts-node/register ./scripts/codegen.ts", "prepublish": "yarn build", "build": "yarn asbuild:untouched && yarn asbuild:optimized && yarn build:lib", "asbuild:untouched": "asc assembly/index.ts -b build/untouched.wasm -t build/untouched.wat --runtime none --validate --debug", "asbuild:optimized": "asc assembly/index.ts -b build/optimized.wasm -t build/optimized.wat --runtime none --validate -O3z --noAssert", "build:lib": "tsc -p tsconfig.build.json", "build:web": "webpack --mode production --entry ./index.js --output ./dist/as-chacha20poly1305.min.js", "test": "yarn run test:unit", "test:unit": "yarn run test:unit:node", "test:unit:node": "mocha -r ts-node/register test/unit/*.test.ts", "test:unit:browser": "karma start --single-run --browsers ChromeHeadless,FirefoxHeadless karma.config.js", "benchmark": "node -r ts-node/register node_modules/.bin/benchmark 'test/perf/*.test.ts'", "benchmark:local": "yarn benchmark --local"}, "devDependencies": {"@as-pect/assembly": "2.8.1", "@as-pect/cli": "2.8.1", "@as-pect/core": "2.8.1", "@stablelib/chacha": "^1.0.1", "@stablelib/poly1305": "^1.0.1", "@stablelib/chacha20poly1305": "^1.0.1", "assemblyscript": "0.9.2", "@dapplion/benchmark": "^0.2.2", "@types/chai": "^4.2.15", "@types/mocha": "^8.2.2", "@types/node": "^14.14.17", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "chai": "^4.3.4", "eslint": "^7.30.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-no-only-tests": "^2.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.3.1", "karma": "^6.3.16", "karma-babel-preprocessor": "^8.0.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^1.2.0", "karma-mocha": "^1.3.0", "karma-webpack": "^5.0.0", "mocha": "^8.3.2", "nyc": "^15.0.0", "prettier": "^2.2.1", "ts-loader": "^9.2.8", "ts-node": "^9.1.1", "typescript": "~4.2.3", "webpack": "^5.43.0", "webpack-cli": "^4.7.2", "webpack-dev-server": "^3.11.2"}}