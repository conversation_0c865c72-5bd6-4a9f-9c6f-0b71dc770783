{"version": 3, "file": "chacha20.js", "sourceRoot": "", "sources": ["../../src/chacha20.ts"], "names": [], "mappings": ";;;AACA,iCAAmC;AAEnC,MAAM,GAAG,GAAG,kBAAW,EAAE,CAAC;AAC1B,MAAM,cAAc,GAAG,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC;AAC/C,MAAM,eAAe,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC;AACjD,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC;AAC3C,MAAM,gBAAgB,GAAG,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC;AACnD,MAAM,EAAC,qBAAqB,EAAE,UAAU,EAAE,uBAAuB,EAAC,GAAG,GAAG,CAAC;AAEzE,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,EAAE,qBAAqB,CAAC,CAAC;AAC1F,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,qBAAqB,CAAC,CAAC;AAC5F,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AAC3E,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;AAChG,8EAA8E;AAE9E;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAAC,GAAY,EAAE,KAAc,EAAE,GAAe;IAC7E,gCAAgC;IAChC,IAAI,GAAG,CAAC,MAAM,IAAI,UAAU,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,8CAA8C,GAAG,UAAU,GAAG,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;KACrG;IAED,IAAI,KAAK,CAAC,MAAM,IAAI,uBAAuB,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;KAC/D;IAED,OAAO;IACP,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChB,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACtB,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAE1C,oBAAoB;IACpB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,qBAAqB,CAAC,CAAC;IAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE;QAC9B,MAAM,KAAK,GAAG,CAAC,GAAG,qBAAqB,CAAC;QACxC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAClE,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;QAC1D,MAAM,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC;QAC3B,MAAM,UAAU,GAAG,GAAG,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,CAAC,GAAG,CAAC,UAAU,KAAK,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;KACzG;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AA3BD,8CA2BC"}