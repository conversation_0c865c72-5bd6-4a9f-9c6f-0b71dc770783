{"version": 3, "file": "chacha20poly1305.js", "sourceRoot": "", "sources": ["../../src/chacha20poly1305.ts"], "names": [], "mappings": ";;;AAAA,2CAAwF;AAIxF,MAAa,gBAAgB;IAS3B,YAA6B,GAAgB;QAAhB,QAAG,GAAH,GAAG,CAAa;QAC3C,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,kBAAU,CAAC,CAAC;QAC9E,MAAM,cAAc,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,EAAE,oBAAY,CAAC,CAAC;QACpF,MAAM,WAAW,GAAG,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAC/C,yBAAyB;QACzB,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,kBAAU,CAAC,CAAC;QAC5E,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;QAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,yBAAiB,CAAC,CAAC;QAC1F,MAAM,uBAAuB,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC;QACzD,IAAI,CAAC,qBAAqB,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,uBAAuB,EAAE,yBAAiB,CAAC,CAAC;QAC3G,MAAM,uBAAuB,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC;QACzD,IAAI,CAAC,qBAAqB,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,uBAAuB,EAAE,kBAAU,CAAC,CAAC;QACpG,MAAM,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,IAAI,CACF,GAAY,EACZ,KAAc,EACd,SAAqB,EACrB,cAA2B,EAC3B,GAAgB;QAEhB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;QACtC,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,GAAG,kBAAU,CAAC;QACnD,IAAI,MAAM,CAAC;QACX,IAAI,GAAG,EAAE;YACP,IAAI,GAAG,CAAC,MAAM,KAAK,YAAY,EAAE;gBAC/B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;aACnE;YACD,MAAM,GAAG,GAAG,CAAC;SACd;aAAM;YACL,MAAM,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC;SACvC;QACD,MAAM,YAAY,GAAG,cAAc,EAAE,MAAM,IAAI,CAAC,CAAC;QAEjD,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;QACjD,iEAAiE;QACjE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACzD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAI,CACF,GAAY,EACZ,KAAc,EACd,MAAkB,EAClB,cAA2B,EAC3B,GAAgB;QAEhB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,kBAAU,CAAC,CAAC;QAEnE,IAAI,MAAM,CAAC;QACX,IAAI,GAAG,EAAE;YACP,IAAI,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;gBACrC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;aACnE;YACD,MAAM,GAAG,GAAG,CAAC;SACd;aAAM;YACL,MAAM,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC7C;QACD,MAAM,YAAY,GAAG,cAAc,EAAE,MAAM,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;QAEnD,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,kBAAU,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACvE,iEAAiE;QACjE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACvC,OAAO,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IACpC,CAAC;IAEO,IAAI,CAAC,GAAY,EAAE,KAAc,EAAE,KAAiB,IAAI,UAAU,CAAC,CAAC,CAAC;QAC3E,IAAI,GAAG,CAAC,MAAM,IAAI,kBAAU,EAAE;YAC5B,MAAM,KAAK,CAAC,uCAAuC,GAAG,CAAC,MAAM,YAAY,kBAAU,EAAE,CAAC,CAAC;SACxF;QACD,IAAI,EAAE,CAAC,MAAM,GAAG,kBAAU,EAAE;YAC1B,MAAM,KAAK,CAAC,qBAAqB,EAAE,CAAC,MAAM,eAAe,kBAAU,EAAE,CAAC,CAAC;SACxE;QACD,IAAI,KAAK,CAAC,MAAM,KAAK,oBAAY,EAAE;YACjC,MAAM,KAAK,CAAC,wBAAwB,KAAK,CAAC,MAAM,YAAY,oBAAY,EAAE,CAAC,CAAC;SAC7E;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACvB,gGAAgG;IAClG,CAAC;IAEO,UAAU,CAAC,IAAgB,EAAE,YAAoB,EAAE,GAAe;QACxE,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;IAClE,CAAC;IAEO,UAAU,CAAC,IAAgB,EAAE,YAAoB,EAAE,GAAe;QACxE,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;IAClE,CAAC;IAEO,YAAY,CAAC,IAAgB,EAAE,QAAkB,EAAE,YAAoB,EAAE,GAAe;QAC9F,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,CAAC,MAAM,IAAI,yBAAiB,EAAE;YACpC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC5B,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YACnD,GAAG,CAAC,GAAG,CACL,MAAM,KAAK,yBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAC3G,CAAC;YACF,OAAO;SACR;QAED,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,MAAM,IAAI,yBAAiB,EAAE;YACjE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,yBAAiB,CAAC,CAAC;YACzD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,GAAG,yBAAiB,IAAI,MAAM,CAAC;YACpD,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,GAAG,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YAC9D,GAAG,CAAC,GAAG,CACL,GAAG,GAAG,MAAM,KAAK,yBAAiB;gBAChC,CAAC,CAAC,IAAI,CAAC,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,EACxD,MAAM,CACP,CAAC;SACH;IACH,CAAC;IAEO,SAAS,CAAC,GAAe;QAC/B,gEAAgE;QAChE,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAU,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;gBAC5C,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;aACP;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAtJD,4CAsJC"}